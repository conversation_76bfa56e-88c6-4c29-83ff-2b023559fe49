#!/bin/bash

# Start development environment for Sanitization Config Service

echo "Starting Sanitization Config Service Development Environment..."

# Function to cleanup processes on exit
cleanup() {
    echo "Stopping services..."
    kill $BACKEND_PID $FRONTEND_PID 2>/dev/null
    exit 0
}

# Set trap to cleanup on script exit
trap cleanup SIGINT SIGTERM

# Start backend service
echo "Starting backend service on port 8081..."
cd "$(dirname "$0")"
SERVER_PORT=8081 go run main.go &
BACKEND_PID=$!

# Wait a moment for backend to start
sleep 3

# Start frontend development server
echo "Starting frontend development server on port 3000..."
cd frontend
npm start &
FRONTEND_PID=$!

echo ""
echo "Services started successfully!"
echo "Backend API: http://localhost:8081"
echo "Frontend UI: http://localhost:3000"
echo ""
echo "Press Ctrl+C to stop all services"

# Wait for processes
wait $BACKEND_PID $FRONTEND_PID
