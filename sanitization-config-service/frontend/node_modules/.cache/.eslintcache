[{"/Users/<USER>/dev/work/lab/javaagent/sanitization-config-service/frontend/src/index.tsx": "1", "/Users/<USER>/dev/work/lab/javaagent/sanitization-config-service/frontend/src/reportWebVitals.ts": "2", "/Users/<USER>/dev/work/lab/javaagent/sanitization-config-service/frontend/src/App.tsx": "3", "/Users/<USER>/dev/work/lab/javaagent/sanitization-config-service/frontend/src/components/ConfigManager.tsx": "4", "/Users/<USER>/dev/work/lab/javaagent/sanitization-config-service/frontend/src/components/Navigation.tsx": "5", "/Users/<USER>/dev/work/lab/javaagent/sanitization-config-service/frontend/src/components/RulesList.tsx": "6", "/Users/<USER>/dev/work/lab/javaagent/sanitization-config-service/frontend/src/components/Dashboard.tsx": "7", "/Users/<USER>/dev/work/lab/javaagent/sanitization-config-service/frontend/src/services/api.ts": "8", "/Users/<USER>/dev/work/lab/javaagent/sanitization-config-service/frontend/src/utils/index.ts": "9"}, {"size": 554, "mtime": 1752758697871, "results": "10", "hashOfConfig": "11"}, {"size": 425, "mtime": 1752758697872, "results": "12", "hashOfConfig": "11"}, {"size": 2240, "mtime": 1752759003625, "results": "13", "hashOfConfig": "11"}, {"size": 11291, "mtime": 1752759981088, "results": "14", "hashOfConfig": "11"}, {"size": 6525, "mtime": 1752760060611, "results": "15", "hashOfConfig": "11"}, {"size": 15512, "mtime": 1752760172813, "results": "16", "hashOfConfig": "11"}, {"size": 13926, "mtime": 1752759822644, "results": "17", "hashOfConfig": "11"}, {"size": 1872, "mtime": 1752758814744, "results": "18", "hashOfConfig": "11"}, {"size": 2627, "mtime": 1752758830422, "results": "19", "hashOfConfig": "11"}, {"filePath": "20", "messages": "21", "suppressedMessages": "22", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "1a6fn3w", {"filePath": "23", "messages": "24", "suppressedMessages": "25", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "26", "messages": "27", "suppressedMessages": "28", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "29", "messages": "30", "suppressedMessages": "31", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "32", "messages": "33", "suppressedMessages": "34", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "35", "messages": "36", "suppressedMessages": "37", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "38", "messages": "39", "suppressedMessages": "40", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "41", "messages": "42", "suppressedMessages": "43", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "44", "messages": "45", "suppressedMessages": "46", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "/Users/<USER>/dev/work/lab/javaagent/sanitization-config-service/frontend/src/index.tsx", [], [], "/Users/<USER>/dev/work/lab/javaagent/sanitization-config-service/frontend/src/reportWebVitals.ts", [], [], "/Users/<USER>/dev/work/lab/javaagent/sanitization-config-service/frontend/src/App.tsx", [], [], "/Users/<USER>/dev/work/lab/javaagent/sanitization-config-service/frontend/src/components/ConfigManager.tsx", [], [], "/Users/<USER>/dev/work/lab/javaagent/sanitization-config-service/frontend/src/components/Navigation.tsx", [], [], "/Users/<USER>/dev/work/lab/javaagent/sanitization-config-service/frontend/src/components/RulesList.tsx", [], ["47", "48"], "/Users/<USER>/dev/work/lab/javaagent/sanitization-config-service/frontend/src/components/Dashboard.tsx", [], [], "/Users/<USER>/dev/work/lab/javaagent/sanitization-config-service/frontend/src/services/api.ts", [], [], "/Users/<USER>/dev/work/lab/javaagent/sanitization-config-service/frontend/src/utils/index.ts", [], [], {"ruleId": "49", "severity": 1, "message": "50", "line": 90, "column": 6, "nodeType": "51", "endLine": 90, "endColumn": 19, "suggestions": "52", "suppressions": "53"}, {"ruleId": "49", "severity": 1, "message": "54", "line": 94, "column": 6, "nodeType": "51", "endLine": 94, "endColumn": 75, "suggestions": "55", "suppressions": "56"}, "react-hooks/exhaustive-deps", "React Hook useEffect has a missing dependency: 'fetchRules'. Either include it or remove the dependency array.", "ArrayExpression", ["57"], ["58"], "React Hook useEffect has a missing dependency: 'filterRules'. Either include it or remove the dependency array.", ["59"], ["60"], {"desc": "61", "fix": "62"}, {"kind": "63", "justification": "64"}, {"desc": "65", "fix": "66"}, {"kind": "63", "justification": "64"}, "Update the dependencies array to be: [fetchRules, serviceName]", {"range": "67", "text": "68"}, "directive", "", "Update the dependencies array to be: [config, searchTerm, selectedType, selectedSeverity, showEnabledOnly, filterRules]", {"range": "69", "text": "70"}, [2926, 2939], "[fetchRules, serviceName]", [3038, 3107], "[config, searchTerm, selectedType, selectedSeverity, showEnabledOnly, filterRules]"]