[{"/Users/<USER>/dev/work/lab/javaagent/sanitization-config-service/frontend/src/index.tsx": "1", "/Users/<USER>/dev/work/lab/javaagent/sanitization-config-service/frontend/src/reportWebVitals.ts": "2", "/Users/<USER>/dev/work/lab/javaagent/sanitization-config-service/frontend/src/App.tsx": "3", "/Users/<USER>/dev/work/lab/javaagent/sanitization-config-service/frontend/src/components/SimpleRulesList.tsx": "4", "/Users/<USER>/dev/work/lab/javaagent/sanitization-config-service/frontend/src/services/api.ts": "5"}, {"size": 554, "mtime": 1752758697871, "results": "6", "hashOfConfig": "7"}, {"size": 425, "mtime": 1752758697872, "results": "8", "hashOfConfig": "7"}, {"size": 780, "mtime": 1752762627535, "results": "9", "hashOfConfig": "7"}, {"size": 11089, "mtime": 1752762605414, "results": "10", "hashOfConfig": "7"}, {"size": 2140, "mtime": 1752763232884, "results": "11", "hashOfConfig": "7"}, {"filePath": "12", "messages": "13", "suppressedMessages": "14", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "1a6fn3w", {"filePath": "15", "messages": "16", "suppressedMessages": "17", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "18", "messages": "19", "suppressedMessages": "20", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "21", "messages": "22", "suppressedMessages": "23", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "24", "messages": "25", "suppressedMessages": "26", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "/Users/<USER>/dev/work/lab/javaagent/sanitization-config-service/frontend/src/index.tsx", [], [], "/Users/<USER>/dev/work/lab/javaagent/sanitization-config-service/frontend/src/reportWebVitals.ts", [], [], "/Users/<USER>/dev/work/lab/javaagent/sanitization-config-service/frontend/src/App.tsx", [], [], "/Users/<USER>/dev/work/lab/javaagent/sanitization-config-service/frontend/src/components/SimpleRulesList.tsx", [], [], "/Users/<USER>/dev/work/lab/javaagent/sanitization-config-service/frontend/src/services/api.ts", [], []]