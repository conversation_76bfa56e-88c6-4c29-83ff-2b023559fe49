[{"/Users/<USER>/dev/work/lab/javaagent/sanitization-config-service/frontend/src/index.tsx": "1", "/Users/<USER>/dev/work/lab/javaagent/sanitization-config-service/frontend/src/reportWebVitals.ts": "2", "/Users/<USER>/dev/work/lab/javaagent/sanitization-config-service/frontend/src/App.tsx": "3", "/Users/<USER>/dev/work/lab/javaagent/sanitization-config-service/frontend/src/services/api.ts": "4", "/Users/<USER>/dev/work/lab/javaagent/sanitization-config-service/frontend/src/utils/index.ts": "5", "/Users/<USER>/dev/work/lab/javaagent/sanitization-config-service/frontend/src/components/SimpleRulesList.tsx": "6"}, {"size": 554, "mtime": 1752758697871, "results": "7", "hashOfConfig": "8"}, {"size": 425, "mtime": 1752758697872, "results": "9", "hashOfConfig": "8"}, {"size": 392, "mtime": 1752761494795, "results": "10", "hashOfConfig": "8"}, {"size": 2140, "mtime": 1752762020309, "results": "11", "hashOfConfig": "8"}, {"size": 2627, "mtime": 1752758830422, "results": "12", "hashOfConfig": "8"}, {"size": 9900, "mtime": 1752762124490, "results": "13", "hashOfConfig": "8"}, {"filePath": "14", "messages": "15", "suppressedMessages": "16", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "1a6fn3w", {"filePath": "17", "messages": "18", "suppressedMessages": "19", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "20", "messages": "21", "suppressedMessages": "22", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "23", "messages": "24", "suppressedMessages": "25", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "26", "messages": "27", "suppressedMessages": "28", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "29", "messages": "30", "suppressedMessages": "31", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "/Users/<USER>/dev/work/lab/javaagent/sanitization-config-service/frontend/src/index.tsx", [], [], "/Users/<USER>/dev/work/lab/javaagent/sanitization-config-service/frontend/src/reportWebVitals.ts", [], [], "/Users/<USER>/dev/work/lab/javaagent/sanitization-config-service/frontend/src/App.tsx", [], [], "/Users/<USER>/dev/work/lab/javaagent/sanitization-config-service/frontend/src/services/api.ts", [], [], "/Users/<USER>/dev/work/lab/javaagent/sanitization-config-service/frontend/src/utils/index.ts", [], [], "/Users/<USER>/dev/work/lab/javaagent/sanitization-config-service/frontend/src/components/SimpleRulesList.tsx", [], []]