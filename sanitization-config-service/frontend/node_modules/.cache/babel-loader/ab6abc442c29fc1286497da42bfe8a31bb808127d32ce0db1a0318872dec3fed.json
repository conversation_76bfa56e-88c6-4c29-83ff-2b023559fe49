{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"M14.106 5.553a2 2 0 0 0 1.788 0l3.659-1.83A1 1 0 0 1 21 4.619v12.764a1 1 0 0 1-.553.894l-4.553 2.277a2 2 0 0 1-1.788 0l-4.212-2.106a2 2 0 0 0-1.788 0l-3.659 1.83A1 1 0 0 1 3 19.381V6.618a1 1 0 0 1 .553-.894l4.553-2.277a2 2 0 0 1 1.788 0z\",\n  key: \"169xi5\"\n}], [\"path\", {\n  d: \"M15 5.764v15\",\n  key: \"1pn4in\"\n}], [\"path\", {\n  d: \"M9 3.236v15\",\n  key: \"1uimfh\"\n}]];\nconst Map = createLucideIcon(\"map\", __iconNode);\nexport { __iconNode, Map as default };\n//# sourceMappingURL=map.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}