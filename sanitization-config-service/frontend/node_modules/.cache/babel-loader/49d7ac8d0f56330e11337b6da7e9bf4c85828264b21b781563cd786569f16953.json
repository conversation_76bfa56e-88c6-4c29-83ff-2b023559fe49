{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"circle\", {\n  cx: \"12\",\n  cy: \"16\",\n  r: \"1\",\n  key: \"1au0dj\"\n}], [\"rect\", {\n  x: \"3\",\n  y: \"10\",\n  width: \"18\",\n  height: \"12\",\n  rx: \"2\",\n  key: \"6s8ecr\"\n}], [\"path\", {\n  d: \"M7 10V7a5 5 0 0 1 10 0v3\",\n  key: \"1pqi11\"\n}]];\nconst LockKeyhole = createLucideIcon(\"lock-keyhole\", __iconNode);\nexport { __iconNode, LockKeyhole as default };\n//# sourceMappingURL=lock-keyhole.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}