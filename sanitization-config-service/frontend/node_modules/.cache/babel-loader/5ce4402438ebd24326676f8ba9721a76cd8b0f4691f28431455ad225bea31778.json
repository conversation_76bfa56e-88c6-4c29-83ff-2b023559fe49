{"ast": null, "code": "var _jsxFileName = \"/Users/<USER>/dev/work/lab/javaagent/sanitization-config-service/frontend/src/App.tsx\",\n  _s = $RefreshSig$();\nimport React, { useState } from 'react';\nimport { BrowserRouter as Router, Routes, Route } from 'react-router-dom';\nimport { Toaster } from 'react-hot-toast';\nimport Navigation from './components/Navigation';\nimport Dashboard from './components/Dashboard';\nimport RulesList from './components/RulesList';\nimport ConfigManager from './components/ConfigManager';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nfunction App() {\n  _s();\n  const [sidebarOpen, setSidebarOpen] = useState(false);\n  return /*#__PURE__*/_jsxDEV(Router, {\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"h-screen flex overflow-hidden bg-gray-100\",\n      children: [/*#__PURE__*/_jsxDEV(Navigation, {\n        isOpen: sidebarOpen,\n        setIsOpen: setSidebarOpen\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 15,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex flex-col w-0 flex-1 overflow-hidden lg:pl-64\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"relative z-10 flex-shrink-0 flex h-16 bg-white shadow lg:hidden\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex-1 px-4 flex justify-between\",\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex-1 flex\",\n              children: /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"w-full flex md:ml-0\",\n                children: /*#__PURE__*/_jsxDEV(\"label\", {\n                  htmlFor: \"search-field\",\n                  className: \"sr-only\",\n                  children: \"Search\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 22,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 21,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 20,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 19,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 18,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"main\", {\n          className: \"flex-1 relative overflow-y-auto focus:outline-none\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"py-6\",\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"max-w-7xl mx-auto px-4 sm:px-6 md:px-8\",\n              children: /*#__PURE__*/_jsxDEV(Routes, {\n                children: [/*#__PURE__*/_jsxDEV(Route, {\n                  path: \"/\",\n                  element: /*#__PURE__*/_jsxDEV(Dashboard, {}, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 34,\n                    columnNumber: 44\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 34,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Route, {\n                  path: \"/rules\",\n                  element: /*#__PURE__*/_jsxDEV(RulesList, {}, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 35,\n                    columnNumber: 49\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 35,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Route, {\n                  path: \"/config\",\n                  element: /*#__PURE__*/_jsxDEV(ConfigManager, {}, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 36,\n                    columnNumber: 50\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 36,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 33,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 32,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 31,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 30,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 17,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 14,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Toaster, {\n      position: \"top-right\",\n      toastOptions: {\n        duration: 4000,\n        style: {\n          background: '#363636',\n          color: '#fff'\n        },\n        success: {\n          duration: 3000,\n          iconTheme: {\n            primary: '#4ade80',\n            secondary: '#fff'\n          }\n        },\n        error: {\n          duration: 5000,\n          iconTheme: {\n            primary: '#ef4444',\n            secondary: '#fff'\n          }\n        }\n      }\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 44,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 13,\n    columnNumber: 5\n  }, this);\n}\n_s(App, \"5rGDkYpGQ8fHM9RkMWnKOwsxadk=\");\n_c = App;\nexport default App;\nvar _c;\n$RefreshReg$(_c, \"App\");", "map": {"version": 3, "names": ["React", "useState", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Router", "Routes", "Route", "Toaster", "Navigation", "Dashboard", "RulesList", "ConfigManager", "jsxDEV", "_jsxDEV", "App", "_s", "sidebarOpen", "setSidebarOpen", "children", "className", "isOpen", "setIsOpen", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "htmlFor", "path", "element", "position", "toastOptions", "duration", "style", "background", "color", "success", "iconTheme", "primary", "secondary", "error", "_c", "$RefreshReg$"], "sources": ["/Users/<USER>/dev/work/lab/javaagent/sanitization-config-service/frontend/src/App.tsx"], "sourcesContent": ["import React, { useState } from 'react';\nimport { BrowserRouter as Router, Routes, Route } from 'react-router-dom';\nimport { Toaster } from 'react-hot-toast';\nimport Navigation from './components/Navigation';\nimport Dashboard from './components/Dashboard';\nimport RulesList from './components/RulesList';\nimport ConfigManager from './components/ConfigManager';\n\nfunction App() {\n  const [sidebarOpen, setSidebarOpen] = useState(false);\n\n  return (\n    <Router>\n      <div className=\"h-screen flex overflow-hidden bg-gray-100\">\n        <Navigation isOpen={sidebarOpen} setIsOpen={setSidebarOpen} />\n\n        <div className=\"flex flex-col w-0 flex-1 overflow-hidden lg:pl-64\">\n          <div className=\"relative z-10 flex-shrink-0 flex h-16 bg-white shadow lg:hidden\">\n            <div className=\"flex-1 px-4 flex justify-between\">\n              <div className=\"flex-1 flex\">\n                <div className=\"w-full flex md:ml-0\">\n                  <label htmlFor=\"search-field\" className=\"sr-only\">\n                    Search\n                  </label>\n                </div>\n              </div>\n            </div>\n          </div>\n\n          <main className=\"flex-1 relative overflow-y-auto focus:outline-none\">\n            <div className=\"py-6\">\n              <div className=\"max-w-7xl mx-auto px-4 sm:px-6 md:px-8\">\n                <Routes>\n                  <Route path=\"/\" element={<Dashboard />} />\n                  <Route path=\"/rules\" element={<RulesList />} />\n                  <Route path=\"/config\" element={<ConfigManager />} />\n                </Routes>\n              </div>\n            </div>\n          </main>\n        </div>\n      </div>\n\n      <Toaster\n        position=\"top-right\"\n        toastOptions={{\n          duration: 4000,\n          style: {\n            background: '#363636',\n            color: '#fff',\n          },\n          success: {\n            duration: 3000,\n            iconTheme: {\n              primary: '#4ade80',\n              secondary: '#fff',\n            },\n          },\n          error: {\n            duration: 5000,\n            iconTheme: {\n              primary: '#ef4444',\n              secondary: '#fff',\n            },\n          },\n        }}\n      />\n    </Router>\n  );\n}\n\nexport default App;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AACvC,SAASC,aAAa,IAAIC,MAAM,EAAEC,MAAM,EAAEC,KAAK,QAAQ,kBAAkB;AACzE,SAASC,OAAO,QAAQ,iBAAiB;AACzC,OAAOC,UAAU,MAAM,yBAAyB;AAChD,OAAOC,SAAS,MAAM,wBAAwB;AAC9C,OAAOC,SAAS,MAAM,wBAAwB;AAC9C,OAAOC,aAAa,MAAM,4BAA4B;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEvD,SAASC,GAAGA,CAAA,EAAG;EAAAC,EAAA;EACb,MAAM,CAACC,WAAW,EAAEC,cAAc,CAAC,GAAGf,QAAQ,CAAC,KAAK,CAAC;EAErD,oBACEW,OAAA,CAACT,MAAM;IAAAc,QAAA,gBACLL,OAAA;MAAKM,SAAS,EAAC,2CAA2C;MAAAD,QAAA,gBACxDL,OAAA,CAACL,UAAU;QAACY,MAAM,EAAEJ,WAAY;QAACK,SAAS,EAAEJ;MAAe;QAAAK,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eAE9DZ,OAAA;QAAKM,SAAS,EAAC,mDAAmD;QAAAD,QAAA,gBAChEL,OAAA;UAAKM,SAAS,EAAC,iEAAiE;UAAAD,QAAA,eAC9EL,OAAA;YAAKM,SAAS,EAAC,kCAAkC;YAAAD,QAAA,eAC/CL,OAAA;cAAKM,SAAS,EAAC,aAAa;cAAAD,QAAA,eAC1BL,OAAA;gBAAKM,SAAS,EAAC,qBAAqB;gBAAAD,QAAA,eAClCL,OAAA;kBAAOa,OAAO,EAAC,cAAc;kBAACP,SAAS,EAAC,SAAS;kBAAAD,QAAA,EAAC;gBAElD;kBAAAI,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACL;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAENZ,OAAA;UAAMM,SAAS,EAAC,oDAAoD;UAAAD,QAAA,eAClEL,OAAA;YAAKM,SAAS,EAAC,MAAM;YAAAD,QAAA,eACnBL,OAAA;cAAKM,SAAS,EAAC,wCAAwC;cAAAD,QAAA,eACrDL,OAAA,CAACR,MAAM;gBAAAa,QAAA,gBACLL,OAAA,CAACP,KAAK;kBAACqB,IAAI,EAAC,GAAG;kBAACC,OAAO,eAAEf,OAAA,CAACJ,SAAS;oBAAAa,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAE;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,eAC1CZ,OAAA,CAACP,KAAK;kBAACqB,IAAI,EAAC,QAAQ;kBAACC,OAAO,eAAEf,OAAA,CAACH,SAAS;oBAAAY,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAE;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,eAC/CZ,OAAA,CAACP,KAAK;kBAACqB,IAAI,EAAC,SAAS;kBAACC,OAAO,eAAEf,OAAA,CAACF,aAAa;oBAAAW,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAE;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC9C;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAENZ,OAAA,CAACN,OAAO;MACNsB,QAAQ,EAAC,WAAW;MACpBC,YAAY,EAAE;QACZC,QAAQ,EAAE,IAAI;QACdC,KAAK,EAAE;UACLC,UAAU,EAAE,SAAS;UACrBC,KAAK,EAAE;QACT,CAAC;QACDC,OAAO,EAAE;UACPJ,QAAQ,EAAE,IAAI;UACdK,SAAS,EAAE;YACTC,OAAO,EAAE,SAAS;YAClBC,SAAS,EAAE;UACb;QACF,CAAC;QACDC,KAAK,EAAE;UACLR,QAAQ,EAAE,IAAI;UACdK,SAAS,EAAE;YACTC,OAAO,EAAE,SAAS;YAClBC,SAAS,EAAE;UACb;QACF;MACF;IAAE;MAAAhB,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACI,CAAC;AAEb;AAACV,EAAA,CA7DQD,GAAG;AAAA0B,EAAA,GAAH1B,GAAG;AA+DZ,eAAeA,GAAG;AAAC,IAAA0B,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}