{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"M12 2v5\",\n  key: \"nd4vlx\"\n}], [\"path\", {\n  d: \"M14.829 15.998a3 3 0 1 1-5.658 0\",\n  key: \"1pybiy\"\n}], [\"path\", {\n  d: \"M20.92 14.606A1 1 0 0 1 20 16H4a1 1 0 0 1-.92-1.394l3-7A1 1 0 0 1 7 7h10a1 1 0 0 1 .92.606z\",\n  key: \"ma1wor\"\n}]];\nconst LampCeiling = createLucideIcon(\"lamp-ceiling\", __iconNode);\nexport { __iconNode, LampCeiling as default };\n//# sourceMappingURL=lamp-ceiling.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}