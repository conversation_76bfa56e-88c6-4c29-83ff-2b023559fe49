{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"M12 3v12\",\n  key: \"1x0j5s\"\n}], [\"path\", {\n  d: \"m8 11 4 4 4-4\",\n  key: \"1dohi6\"\n}], [\"path\", {\n  d: \"M8 5H4a2 2 0 0 0-2 2v10a2 2 0 0 0 2 2h16a2 2 0 0 0 2-2V7a2 2 0 0 0-2-2h-4\",\n  key: \"1ywtjm\"\n}]];\nconst Import = createLucideIcon(\"import\", __iconNode);\nexport { __iconNode, Import as default };\n//# sourceMappingURL=import.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}