{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"m15.5 7.5 2.3 2.3a1 1 0 0 0 1.4 0l2.1-2.1a1 1 0 0 0 0-1.4L19 4\",\n  key: \"g0fldk\"\n}], [\"path\", {\n  d: \"m21 2-9.6 9.6\",\n  key: \"1j0ho8\"\n}], [\"circle\", {\n  cx: \"7.5\",\n  cy: \"15.5\",\n  r: \"5.5\",\n  key: \"yqb3hr\"\n}]];\nconst Key = createLucideIcon(\"key\", __iconNode);\nexport { __iconNode, Key as default };\n//# sourceMappingURL=key.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}