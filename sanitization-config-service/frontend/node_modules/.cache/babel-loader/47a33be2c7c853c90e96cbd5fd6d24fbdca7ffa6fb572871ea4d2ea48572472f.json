{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"M10 2h4\",\n  key: \"n1abiw\"\n}], [\"path\", {\n  d: \"M12 14v-4\",\n  key: \"1evpnu\"\n}], [\"path\", {\n  d: \"M4 13a8 8 0 0 1 8-7 8 8 0 1 1-5.3 14L4 17.6\",\n  key: \"1ts96g\"\n}], [\"path\", {\n  d: \"M9 17H4v5\",\n  key: \"8t5av\"\n}]];\nconst TimerReset = createLucideIcon(\"timer-reset\", __iconNode);\nexport { __iconNode, TimerReset as default };\n//# sourceMappingURL=timer-reset.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}