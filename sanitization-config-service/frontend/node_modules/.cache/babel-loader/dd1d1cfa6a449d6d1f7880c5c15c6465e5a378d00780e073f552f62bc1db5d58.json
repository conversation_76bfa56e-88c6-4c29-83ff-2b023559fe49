{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"m15 15 6 6m-6-6v4.8m0-4.8h4.8\",\n  key: \"17vawe\"\n}], [\"path\", {\n  d: \"M9 19.8V15m0 0H4.2M9 15l-6 6\",\n  key: \"chjx8e\"\n}], [\"path\", {\n  d: \"M15 4.2V9m0 0h4.8M15 9l6-6\",\n  key: \"lav6yq\"\n}], [\"path\", {\n  d: \"M9 4.2V9m0 0H4.2M9 9 3 3\",\n  key: \"1pxi2q\"\n}]];\nconst Shrink = createLucideIcon(\"shrink\", __iconNode);\nexport { __iconNode, Shrink as default };\n//# sourceMappingURL=shrink.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}