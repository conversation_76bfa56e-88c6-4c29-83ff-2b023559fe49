{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"M12 13V2l8 4-8 4\",\n  key: \"5wlwwj\"\n}], [\"path\", {\n  d: \"M20.561 10.222a9 9 0 1 1-12.55-5.29\",\n  key: \"1c0wjv\"\n}], [\"path\", {\n  d: \"M8.002 9.997a5 5 0 1 0 8.9 2.02\",\n  key: \"gb1g7m\"\n}]];\nconst Goal = createLucideIcon(\"goal\", __iconNode);\nexport { __iconNode, Goal as default };\n//# sourceMappingURL=goal.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}