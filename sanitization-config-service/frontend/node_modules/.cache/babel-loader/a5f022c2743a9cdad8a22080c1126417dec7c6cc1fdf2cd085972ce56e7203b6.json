{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"M4 4v16\",\n  key: \"6qkkli\"\n}], [\"path\", {\n  d: \"M9 4v16\",\n  key: \"81ygyz\"\n}]];\nconst Tally2 = createLucideIcon(\"tally-2\", __iconNode);\nexport { __iconNode, Tally2 as default };\n//# sourceMappingURL=tally-2.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}