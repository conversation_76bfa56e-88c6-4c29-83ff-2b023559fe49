{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"M5.5 20H8\",\n  key: \"1k40s5\"\n}], [\"path\", {\n  d: \"M17 9h.01\",\n  key: \"1j24nn\"\n}], [\"rect\", {\n  width: \"10\",\n  height: \"16\",\n  x: \"12\",\n  y: \"4\",\n  rx: \"2\",\n  key: \"ixliua\"\n}], [\"path\", {\n  d: \"M8 6H4a2 2 0 0 0-2 2v6a2 2 0 0 0 2 2h4\",\n  key: \"1mp6e1\"\n}], [\"circle\", {\n  cx: \"17\",\n  cy: \"15\",\n  r: \"1\",\n  key: \"tqvash\"\n}]];\nconst MonitorSpeaker = createLucideIcon(\"monitor-speaker\", __iconNode);\nexport { __iconNode, MonitorSpeaker as default };\n//# sourceMappingURL=monitor-speaker.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}