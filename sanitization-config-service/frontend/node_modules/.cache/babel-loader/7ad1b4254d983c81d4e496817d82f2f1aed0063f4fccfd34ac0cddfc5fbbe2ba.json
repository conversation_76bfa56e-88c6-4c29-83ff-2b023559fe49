{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"m11 17 2 2a1 1 0 1 0 3-3\",\n  key: \"efffak\"\n}], [\"path\", {\n  d: \"m14 14 2.5 2.5a1 1 0 1 0 3-3l-3.88-3.88a3 3 0 0 0-4.24 0l-.88.88a1 1 0 1 1-3-3l2.81-2.81a5.79 5.79 0 0 1 7.06-.87l.47.28a2 2 0 0 0 1.42.25L21 4\",\n  key: \"9pr0kb\"\n}], [\"path\", {\n  d: \"m21 3 1 11h-2\",\n  key: \"1tisrp\"\n}], [\"path\", {\n  d: \"M3 3 2 14l6.5 6.5a1 1 0 1 0 3-3\",\n  key: \"1uvwmv\"\n}], [\"path\", {\n  d: \"M3 4h8\",\n  key: \"1ep09j\"\n}]];\nconst Handshake = createLucideIcon(\"handshake\", __iconNode);\nexport { __iconNode, Handshake as default };\n//# sourceMappingURL=handshake.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}