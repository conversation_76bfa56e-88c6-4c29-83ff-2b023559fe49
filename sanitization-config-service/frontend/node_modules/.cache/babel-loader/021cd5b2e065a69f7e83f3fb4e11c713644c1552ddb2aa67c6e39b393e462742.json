{"ast": null, "code": "var _jsxFileName = \"/Users/<USER>/dev/work/lab/javaagent/sanitization-config-service/frontend/src/components/RulesList.tsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { Search, RefreshCw, Download, Shield } from 'lucide-react';\nimport { sanitizationApi } from '../services/api';\nimport { getSeverityBadgeColor, getRuleTypeColor, getRuleTypeIcon, truncateText, downloadJson } from '../utils';\nimport toast from 'react-hot-toast';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst RulesList = () => {\n  _s();\n  const [config, setConfig] = useState(null);\n  const [filteredRules, setFilteredRules] = useState([]);\n  const [loading, setLoading] = useState(true);\n  const [refreshing, setRefreshing] = useState(false);\n  const [searchTerm, setSearchTerm] = useState('');\n  const [selectedType, setSelectedType] = useState('ALL');\n  const [selectedSeverity, setSelectedSeverity] = useState('ALL');\n  const [showEnabledOnly, setShowEnabledOnly] = useState(false);\n  const [serviceName, setServiceName] = useState('');\n  const fetchRules = async () => {\n    try {\n      const data = await sanitizationApi.getRules(serviceName || undefined);\n      setConfig(data);\n      setFilteredRules(data.rules);\n    } catch (error) {\n      console.error('Failed to fetch rules:', error);\n      toast.error('Failed to load rules');\n    } finally {\n      setLoading(false);\n      setRefreshing(false);\n    }\n  };\n  const handleRefresh = async () => {\n    setRefreshing(true);\n    await fetchRules();\n  };\n  const handleReloadConfig = async () => {\n    try {\n      await sanitizationApi.reloadRules();\n      toast.success('Configuration reloaded successfully');\n      await fetchRules();\n    } catch (error) {\n      console.error('Failed to reload config:', error);\n      toast.error('Failed to reload configuration');\n    }\n  };\n  const handleExport = () => {\n    if (config) {\n      downloadJson(config, `sanitization-config-${Date.now()}.json`);\n      toast.success('Configuration exported successfully');\n    }\n  };\n  const filterRules = () => {\n    if (!config) return;\n    let filtered = config.rules;\n\n    // Filter by search term\n    if (searchTerm) {\n      filtered = filtered.filter(rule => rule.name.toLowerCase().includes(searchTerm.toLowerCase()) || rule.description.toLowerCase().includes(searchTerm.toLowerCase()) || rule.id.toLowerCase().includes(searchTerm.toLowerCase()));\n    }\n\n    // Filter by type\n    if (selectedType !== 'ALL') {\n      filtered = filtered.filter(rule => rule.type === selectedType);\n    }\n\n    // Filter by severity\n    if (selectedSeverity !== 'ALL') {\n      filtered = filtered.filter(rule => rule.severity === selectedSeverity);\n    }\n\n    // Filter by enabled status\n    if (showEnabledOnly) {\n      filtered = filtered.filter(rule => rule.enabled);\n    }\n    setFilteredRules(filtered);\n  };\n  useEffect(() => {\n    fetchRules();\n  }, [serviceName]); // eslint-disable-line react-hooks/exhaustive-deps\n\n  useEffect(() => {\n    filterRules();\n  }, [config, searchTerm, selectedType, selectedSeverity, showEnabledOnly]); // eslint-disable-line react-hooks/exhaustive-deps\n\n  if (loading) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"flex items-center justify-center h-64\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"animate-spin rounded-full h-12 w-12 border-b-2 border-primary-600\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 99,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 98,\n      columnNumber: 7\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"min-h-screen bg-gray-50\",\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"space-y-8 fade-in\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"bg-white rounded-xl shadow-sm border border-gray-200 p-6\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex flex-col sm:flex-row sm:items-center sm:justify-between\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"mb-4 sm:mb-0\",\n              children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n                className: \"text-3xl font-bold text-gray-900 mb-2\",\n                children: [/*#__PURE__*/_jsxDEV(Shield, {\n                  className: \"inline-block h-8 w-8 text-primary-600 mr-3\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 113,\n                  columnNumber: 19\n                }, this), \"\\u8131\\u654F\\u89C4\\u5219\\u7BA1\\u7406\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 112,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"text-gray-600 text-lg\",\n                children: \"\\u914D\\u7F6E\\u548C\\u7BA1\\u7406\\u6570\\u636E\\u8131\\u654F\\u89C4\\u5219\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 116,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 111,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex flex-wrap items-center gap-3\",\n              children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                onClick: handleExport,\n                className: \"btn btn-secondary\",\n                children: [/*#__PURE__*/_jsxDEV(Download, {\n                  className: \"h-4 w-4 mr-2\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 123,\n                  columnNumber: 19\n                }, this), \"\\u5BFC\\u51FA\\u914D\\u7F6E\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 119,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                onClick: handleReloadConfig,\n                className: \"btn bg-yellow-600 text-white hover:bg-yellow-700\",\n                children: [/*#__PURE__*/_jsxDEV(RefreshCw, {\n                  className: \"h-4 w-4 mr-2\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 130,\n                  columnNumber: 19\n                }, this), \"\\u91CD\\u8F7D\\u914D\\u7F6E\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 126,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                onClick: handleRefresh,\n                disabled: refreshing,\n                className: \"btn btn-primary\",\n                children: [/*#__PURE__*/_jsxDEV(RefreshCw, {\n                  className: `h-4 w-4 mr-2 ${refreshing ? 'animate-spin' : ''}`\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 138,\n                  columnNumber: 19\n                }, this), \"\\u5237\\u65B0\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 133,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 118,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 110,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 109,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"card\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"card-body\",\n            children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n              className: \"text-lg font-semibold text-gray-900 mb-6 flex items-center\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"w-1 h-6 bg-primary-500 rounded-full mr-3\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 149,\n                columnNumber: 17\n              }, this), \"\\u7B5B\\u9009\\u6761\\u4EF6\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 148,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-4\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"lg:col-span-2\",\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  htmlFor: \"search\",\n                  className: \"block text-sm font-medium text-gray-700 mb-2\",\n                  children: \"\\u641C\\u7D22\\u89C4\\u5219\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 155,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"relative\",\n                  children: [/*#__PURE__*/_jsxDEV(Search, {\n                    className: \"absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 159,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                    type: \"text\",\n                    id: \"search\",\n                    value: searchTerm,\n                    onChange: e => setSearchTerm(e.target.value),\n                    placeholder: \"\\u641C\\u7D22\\u89C4\\u5219\\u540D\\u79F0\\u3001\\u63CF\\u8FF0\\u6216ID...\",\n                    className: \"form-input pl-10\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 160,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 158,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 154,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  htmlFor: \"service\",\n                  className: \"block text-sm font-medium text-gray-700 mb-2\",\n                  children: \"\\u670D\\u52A1\\u540D\\u79F0\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 173,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                  type: \"text\",\n                  id: \"service\",\n                  value: serviceName,\n                  onChange: e => setServiceName(e.target.value),\n                  placeholder: \"\\u8F93\\u5165\\u670D\\u52A1\\u540D\\u79F0\",\n                  className: \"form-input\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 176,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 172,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  htmlFor: \"type\",\n                  className: \"block text-sm font-medium text-gray-700 mb-2\",\n                  children: \"\\u89C4\\u5219\\u7C7B\\u578B\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 188,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n                  id: \"type\",\n                  value: selectedType,\n                  onChange: e => setSelectedType(e.target.value),\n                  className: \"form-input form-select\",\n                  children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                    value: \"ALL\",\n                    children: \"\\u6240\\u6709\\u7C7B\\u578B\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 197,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                    value: \"FIELD_NAME\",\n                    children: \"\\u5B57\\u6BB5\\u540D\\u79F0\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 198,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                    value: \"PATTERN\",\n                    children: \"\\u6B63\\u5219\\u6A21\\u5F0F\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 199,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                    value: \"CONTENT_TYPE\",\n                    children: \"\\u5185\\u5BB9\\u7C7B\\u578B\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 200,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                    value: \"CUSTOM\",\n                    children: \"\\u81EA\\u5B9A\\u4E49\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 201,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 191,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 187,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  htmlFor: \"severity\",\n                  className: \"block text-sm font-medium text-gray-700 mb-2\",\n                  children: \"\\u4E25\\u91CD\\u7A0B\\u5EA6\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 207,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n                  id: \"severity\",\n                  value: selectedSeverity,\n                  onChange: e => setSelectedSeverity(e.target.value),\n                  className: \"form-input form-select\",\n                  children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                    value: \"ALL\",\n                    children: \"\\u6240\\u6709\\u7EA7\\u522B\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 216,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                    value: \"CRITICAL\",\n                    children: \"\\u4E25\\u91CD\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 217,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                    value: \"HIGH\",\n                    children: \"\\u9AD8\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 218,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                    value: \"MEDIUM\",\n                    children: \"\\u4E2D\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 219,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                    value: \"LOW\",\n                    children: \"\\u4F4E\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 220,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 210,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 206,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 152,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"mt-6 pt-6 border-t border-gray-200\",\n              children: /*#__PURE__*/_jsxDEV(\"label\", {\n                className: \"flex items-center\",\n                children: [/*#__PURE__*/_jsxDEV(\"input\", {\n                  type: \"checkbox\",\n                  checked: showEnabledOnly,\n                  onChange: e => setShowEnabledOnly(e.target.checked),\n                  className: \"rounded border-gray-300 text-primary-600 shadow-sm focus:border-primary-300 focus:ring focus:ring-primary-200 focus:ring-opacity-50\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 228,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"ml-3 text-sm font-medium text-gray-700\",\n                  children: \"\\u4EC5\\u663E\\u793A\\u542F\\u7528\\u7684\\u89C4\\u5219\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 234,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 227,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 226,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 147,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 146,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"card overflow-hidden\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"px-6 py-4 border-b border-gray-200 bg-gray-50\",\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-center justify-between\",\n              children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                className: \"text-lg font-semibold text-gray-900\",\n                children: \"\\u8131\\u654F\\u89C4\\u5219\\u5217\\u8868\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 244,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"text-sm text-gray-600\",\n                children: [\"\\u663E\\u793A \", filteredRules.length, \" / \", (config === null || config === void 0 ? void 0 : config.rules.length) || 0, \" \\u6761\\u89C4\\u5219\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 247,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 243,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 242,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"overflow-x-auto\",\n            children: [/*#__PURE__*/_jsxDEV(\"table\", {\n              className: \"table\",\n              children: [/*#__PURE__*/_jsxDEV(\"thead\", {\n                children: /*#__PURE__*/_jsxDEV(\"tr\", {\n                  children: [/*#__PURE__*/_jsxDEV(\"th\", {\n                    children: \"\\u89C4\\u5219\\u4FE1\\u606F\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 256,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                    children: \"\\u7C7B\\u578B\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 257,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                    children: \"\\u4E25\\u91CD\\u7A0B\\u5EA6\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 258,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                    children: \"\\u4F18\\u5148\\u7EA7\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 259,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                    children: \"\\u72B6\\u6001\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 260,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                    children: \"\\u914D\\u7F6E\\u8BE6\\u60C5\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 261,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 255,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 254,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"tbody\", {\n                children: filteredRules.map(rule => {\n                  const typeNames = {\n                    'FIELD_NAME': '字段名称',\n                    'PATTERN': '正则模式',\n                    'CONTENT_TYPE': '内容类型',\n                    'CUSTOM': '自定义'\n                  };\n                  const severityNames = {\n                    'CRITICAL': '严重',\n                    'HIGH': '高',\n                    'MEDIUM': '中',\n                    'LOW': '低'\n                  };\n                  return /*#__PURE__*/_jsxDEV(\"tr\", {\n                    children: [/*#__PURE__*/_jsxDEV(\"td\", {\n                      children: /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"space-y-1\",\n                        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                          className: \"font-semibold text-gray-900\",\n                          children: rule.name\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 283,\n                          columnNumber: 29\n                        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                          className: \"text-sm text-gray-600\",\n                          children: truncateText(rule.description, 80)\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 284,\n                          columnNumber: 29\n                        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                          className: \"text-xs text-gray-400 font-mono\",\n                          children: [\"ID: \", rule.id]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 285,\n                          columnNumber: 29\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 282,\n                        columnNumber: 27\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 281,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                      children: /*#__PURE__*/_jsxDEV(\"span\", {\n                        className: `badge ${getRuleTypeColor(rule.type)}`,\n                        children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                          className: \"mr-1\",\n                          children: getRuleTypeIcon(rule.type)\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 290,\n                          columnNumber: 29\n                        }, this), typeNames[rule.type] || rule.type]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 289,\n                        columnNumber: 27\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 288,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                      children: /*#__PURE__*/_jsxDEV(\"span\", {\n                        className: `badge ${getSeverityBadgeColor(rule.severity)}`,\n                        children: severityNames[rule.severity] || rule.severity\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 295,\n                        columnNumber: 27\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 294,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                      children: /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"flex items-center\",\n                        children: /*#__PURE__*/_jsxDEV(\"div\", {\n                          className: \"w-8 h-8 rounded-full bg-gray-100 flex items-center justify-center text-sm font-semibold text-gray-700\",\n                          children: rule.priority\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 301,\n                          columnNumber: 29\n                        }, this)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 300,\n                        columnNumber: 27\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 299,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                      children: /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"flex items-center\",\n                        children: rule.enabled ? /*#__PURE__*/_jsxDEV(_Fragment, {\n                          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                            className: \"w-2 h-2 bg-green-400 rounded-full mr-2 animate-pulse\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 310,\n                            columnNumber: 33\n                          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                            className: \"text-sm font-medium text-green-600\",\n                            children: \"\\u5DF2\\u542F\\u7528\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 311,\n                            columnNumber: 33\n                          }, this)]\n                        }, void 0, true) : /*#__PURE__*/_jsxDEV(_Fragment, {\n                          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                            className: \"w-2 h-2 bg-gray-400 rounded-full mr-2\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 315,\n                            columnNumber: 33\n                          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                            className: \"text-sm font-medium text-gray-500\",\n                            children: \"\\u5DF2\\u7981\\u7528\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 316,\n                            columnNumber: 33\n                          }, this)]\n                        }, void 0, true)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 307,\n                        columnNumber: 27\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 306,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                      children: /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"space-y-2 text-sm\",\n                        children: [rule.fieldNames && rule.fieldNames.length > 0 && /*#__PURE__*/_jsxDEV(\"div\", {\n                          children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                            className: \"font-medium text-gray-700\",\n                            children: \"\\u5B57\\u6BB5:\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 325,\n                            columnNumber: 33\n                          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                            className: \"ml-1 text-gray-600\",\n                            children: [rule.fieldNames.slice(0, 2).join(', '), rule.fieldNames.length > 2 && ` +${rule.fieldNames.length - 2}个`]\n                          }, void 0, true, {\n                            fileName: _jsxFileName,\n                            lineNumber: 326,\n                            columnNumber: 33\n                          }, this)]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 324,\n                          columnNumber: 31\n                        }, this), rule.pattern && /*#__PURE__*/_jsxDEV(\"div\", {\n                          children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                            className: \"font-medium text-gray-700\",\n                            children: \"\\u6A21\\u5F0F:\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 334,\n                            columnNumber: 33\n                          }, this), /*#__PURE__*/_jsxDEV(\"code\", {\n                            className: \"ml-1 text-xs\",\n                            children: truncateText(rule.pattern, 30)\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 335,\n                            columnNumber: 33\n                          }, this)]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 333,\n                          columnNumber: 31\n                        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                          children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                            className: \"font-medium text-gray-700\",\n                            children: \"\\u63A9\\u7801:\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 339,\n                            columnNumber: 31\n                          }, this), /*#__PURE__*/_jsxDEV(\"code\", {\n                            className: \"ml-1 text-xs\",\n                            children: rule.maskValue\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 340,\n                            columnNumber: 31\n                          }, this)]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 338,\n                          columnNumber: 29\n                        }, this), rule.includeServices && rule.includeServices.length > 0 && /*#__PURE__*/_jsxDEV(\"div\", {\n                          children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                            className: \"font-medium text-gray-700\",\n                            children: \"\\u9002\\u7528\\u670D\\u52A1:\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 344,\n                            columnNumber: 33\n                          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                            className: \"ml-1 text-gray-600\",\n                            children: rule.includeServices.join(', ')\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 345,\n                            columnNumber: 33\n                          }, this)]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 343,\n                          columnNumber: 31\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 322,\n                        columnNumber: 27\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 321,\n                      columnNumber: 25\n                    }, this)]\n                  }, rule.id, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 280,\n                    columnNumber: 23\n                  }, this);\n                })\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 264,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 253,\n              columnNumber: 15\n            }, this), filteredRules.length === 0 && /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"text-center py-12\",\n              children: [/*#__PURE__*/_jsxDEV(Shield, {\n                className: \"mx-auto h-12 w-12 text-gray-400\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 358,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n                className: \"mt-2 text-sm font-medium text-gray-900\",\n                children: \"\\u6CA1\\u6709\\u627E\\u5230\\u5339\\u914D\\u7684\\u89C4\\u5219\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 359,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"mt-1 text-sm text-gray-500\",\n                children: \"\\u8BF7\\u5C1D\\u8BD5\\u8C03\\u6574\\u7B5B\\u9009\\u6761\\u4EF6\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 360,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 357,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 252,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 241,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 107,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 106,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 105,\n    columnNumber: 5\n  }, this);\n};\n_s(RulesList, \"oIrIsbnuX2L/jREZ9YltMRaoNN4=\");\n_c = RulesList;\nexport default RulesList;\nvar _c;\n$RefreshReg$(_c, \"RulesList\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "Search", "RefreshCw", "Download", "Shield", "sanitizationApi", "getSeverityBadgeColor", "getRuleTypeColor", "getRuleTypeIcon", "truncateText", "downloadJson", "toast", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "RulesList", "_s", "config", "setConfig", "filteredRules", "setFilteredRules", "loading", "setLoading", "refreshing", "setRefreshing", "searchTerm", "setSearchTerm", "selectedType", "setSelectedType", "selectedSeverity", "setSelectedSeverity", "showEnabledOnly", "setShowEnabledOnly", "serviceName", "setServiceName", "fetchRules", "data", "getRules", "undefined", "rules", "error", "console", "handleRefresh", "handleReloadConfig", "reloadRules", "success", "handleExport", "Date", "now", "filterRules", "filtered", "filter", "rule", "name", "toLowerCase", "includes", "description", "id", "type", "severity", "enabled", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "onClick", "disabled", "htmlFor", "value", "onChange", "e", "target", "placeholder", "checked", "length", "map", "typeNames", "severityNames", "priority", "fieldNames", "slice", "join", "pattern", "maskValue", "includeServices", "_c", "$RefreshReg$"], "sources": ["/Users/<USER>/dev/work/lab/javaagent/sanitization-config-service/frontend/src/components/RulesList.tsx"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { Search, RefreshCw, Download, Shield } from 'lucide-react';\nimport { sanitizationApi } from '../services/api';\nimport { SanitizationConfig, SanitizationRule, RuleType, SeverityLevel } from '../types';\nimport { getSeverityBadgeColor, getRuleTypeColor, getRuleTypeIcon, truncateText, downloadJson } from '../utils';\nimport toast from 'react-hot-toast';\n\nconst RulesList: React.FC = () => {\n  const [config, setConfig] = useState<SanitizationConfig | null>(null);\n  const [filteredRules, setFilteredRules] = useState<SanitizationRule[]>([]);\n  const [loading, setLoading] = useState(true);\n  const [refreshing, setRefreshing] = useState(false);\n  const [searchTerm, setSearchTerm] = useState('');\n  const [selectedType, setSelectedType] = useState<RuleType | 'ALL'>('ALL');\n  const [selectedSeverity, setSelectedSeverity] = useState<SeverityLevel | 'ALL'>('ALL');\n  const [showEnabledOnly, setShowEnabledOnly] = useState(false);\n  const [serviceName, setServiceName] = useState('');\n\n  const fetchRules = async () => {\n    try {\n      const data = await sanitizationApi.getRules(serviceName || undefined);\n      setConfig(data);\n      setFilteredRules(data.rules);\n    } catch (error) {\n      console.error('Failed to fetch rules:', error);\n      toast.error('Failed to load rules');\n    } finally {\n      setLoading(false);\n      setRefreshing(false);\n    }\n  };\n\n  const handleRefresh = async () => {\n    setRefreshing(true);\n    await fetchRules();\n  };\n\n  const handleReloadConfig = async () => {\n    try {\n      await sanitizationApi.reloadRules();\n      toast.success('Configuration reloaded successfully');\n      await fetchRules();\n    } catch (error) {\n      console.error('Failed to reload config:', error);\n      toast.error('Failed to reload configuration');\n    }\n  };\n\n  const handleExport = () => {\n    if (config) {\n      downloadJson(config, `sanitization-config-${Date.now()}.json`);\n      toast.success('Configuration exported successfully');\n    }\n  };\n\n  const filterRules = () => {\n    if (!config) return;\n\n    let filtered = config.rules;\n\n    // Filter by search term\n    if (searchTerm) {\n      filtered = filtered.filter(rule =>\n        rule.name.toLowerCase().includes(searchTerm.toLowerCase()) ||\n        rule.description.toLowerCase().includes(searchTerm.toLowerCase()) ||\n        rule.id.toLowerCase().includes(searchTerm.toLowerCase())\n      );\n    }\n\n    // Filter by type\n    if (selectedType !== 'ALL') {\n      filtered = filtered.filter(rule => rule.type === selectedType);\n    }\n\n    // Filter by severity\n    if (selectedSeverity !== 'ALL') {\n      filtered = filtered.filter(rule => rule.severity === selectedSeverity);\n    }\n\n    // Filter by enabled status\n    if (showEnabledOnly) {\n      filtered = filtered.filter(rule => rule.enabled);\n    }\n\n    setFilteredRules(filtered);\n  };\n\n  useEffect(() => {\n    fetchRules();\n  }, [serviceName]); // eslint-disable-line react-hooks/exhaustive-deps\n\n  useEffect(() => {\n    filterRules();\n  }, [config, searchTerm, selectedType, selectedSeverity, showEnabledOnly]); // eslint-disable-line react-hooks/exhaustive-deps\n\n  if (loading) {\n    return (\n      <div className=\"flex items-center justify-center h-64\">\n        <div className=\"animate-spin rounded-full h-12 w-12 border-b-2 border-primary-600\"></div>\n      </div>\n    );\n  }\n\n  return (\n    <div className=\"min-h-screen bg-gray-50\">\n      <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8\">\n        <div className=\"space-y-8 fade-in\">\n          {/* Header */}\n          <div className=\"bg-white rounded-xl shadow-sm border border-gray-200 p-6\">\n            <div className=\"flex flex-col sm:flex-row sm:items-center sm:justify-between\">\n              <div className=\"mb-4 sm:mb-0\">\n                <h1 className=\"text-3xl font-bold text-gray-900 mb-2\">\n                  <Shield className=\"inline-block h-8 w-8 text-primary-600 mr-3\" />\n                  脱敏规则管理\n                </h1>\n                <p className=\"text-gray-600 text-lg\">配置和管理数据脱敏规则</p>\n              </div>\n              <div className=\"flex flex-wrap items-center gap-3\">\n                <button\n                  onClick={handleExport}\n                  className=\"btn btn-secondary\"\n                >\n                  <Download className=\"h-4 w-4 mr-2\" />\n                  导出配置\n                </button>\n                <button\n                  onClick={handleReloadConfig}\n                  className=\"btn bg-yellow-600 text-white hover:bg-yellow-700\"\n                >\n                  <RefreshCw className=\"h-4 w-4 mr-2\" />\n                  重载配置\n                </button>\n                <button\n                  onClick={handleRefresh}\n                  disabled={refreshing}\n                  className=\"btn btn-primary\"\n                >\n                  <RefreshCw className={`h-4 w-4 mr-2 ${refreshing ? 'animate-spin' : ''}`} />\n                  刷新\n                </button>\n              </div>\n            </div>\n          </div>\n\n          {/* Filters */}\n          <div className=\"card\">\n            <div className=\"card-body\">\n              <h3 className=\"text-lg font-semibold text-gray-900 mb-6 flex items-center\">\n                <div className=\"w-1 h-6 bg-primary-500 rounded-full mr-3\"></div>\n                筛选条件\n              </h3>\n              <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-4\">\n                {/* Search */}\n                <div className=\"lg:col-span-2\">\n                  <label htmlFor=\"search\" className=\"block text-sm font-medium text-gray-700 mb-2\">\n                    搜索规则\n                  </label>\n                  <div className=\"relative\">\n                    <Search className=\"absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400\" />\n                    <input\n                      type=\"text\"\n                      id=\"search\"\n                      value={searchTerm}\n                      onChange={(e) => setSearchTerm(e.target.value)}\n                      placeholder=\"搜索规则名称、描述或ID...\"\n                      className=\"form-input pl-10\"\n                    />\n                  </div>\n                </div>\n\n                {/* Service Name */}\n                <div>\n                  <label htmlFor=\"service\" className=\"block text-sm font-medium text-gray-700 mb-2\">\n                    服务名称\n                  </label>\n                  <input\n                    type=\"text\"\n                    id=\"service\"\n                    value={serviceName}\n                    onChange={(e) => setServiceName(e.target.value)}\n                    placeholder=\"输入服务名称\"\n                    className=\"form-input\"\n                  />\n                </div>\n\n                {/* Type Filter */}\n                <div>\n                  <label htmlFor=\"type\" className=\"block text-sm font-medium text-gray-700 mb-2\">\n                    规则类型\n                  </label>\n                  <select\n                    id=\"type\"\n                    value={selectedType}\n                    onChange={(e) => setSelectedType(e.target.value as RuleType | 'ALL')}\n                    className=\"form-input form-select\"\n                  >\n                    <option value=\"ALL\">所有类型</option>\n                    <option value=\"FIELD_NAME\">字段名称</option>\n                    <option value=\"PATTERN\">正则模式</option>\n                    <option value=\"CONTENT_TYPE\">内容类型</option>\n                    <option value=\"CUSTOM\">自定义</option>\n                  </select>\n                </div>\n\n                {/* Severity Filter */}\n                <div>\n                  <label htmlFor=\"severity\" className=\"block text-sm font-medium text-gray-700 mb-2\">\n                    严重程度\n                  </label>\n                  <select\n                    id=\"severity\"\n                    value={selectedSeverity}\n                    onChange={(e) => setSelectedSeverity(e.target.value as SeverityLevel | 'ALL')}\n                    className=\"form-input form-select\"\n                  >\n                    <option value=\"ALL\">所有级别</option>\n                    <option value=\"CRITICAL\">严重</option>\n                    <option value=\"HIGH\">高</option>\n                    <option value=\"MEDIUM\">中</option>\n                    <option value=\"LOW\">低</option>\n                  </select>\n                </div>\n              </div>\n\n              {/* Enabled Only Toggle */}\n              <div className=\"mt-6 pt-6 border-t border-gray-200\">\n                <label className=\"flex items-center\">\n                  <input\n                    type=\"checkbox\"\n                    checked={showEnabledOnly}\n                    onChange={(e) => setShowEnabledOnly(e.target.checked)}\n                    className=\"rounded border-gray-300 text-primary-600 shadow-sm focus:border-primary-300 focus:ring focus:ring-primary-200 focus:ring-opacity-50\"\n                  />\n                  <span className=\"ml-3 text-sm font-medium text-gray-700\">仅显示启用的规则</span>\n                </label>\n              </div>\n            </div>\n          </div>\n\n          {/* Rules Table */}\n          <div className=\"card overflow-hidden\">\n            <div className=\"px-6 py-4 border-b border-gray-200 bg-gray-50\">\n              <div className=\"flex items-center justify-between\">\n                <h3 className=\"text-lg font-semibold text-gray-900\">\n                  脱敏规则列表\n                </h3>\n                <div className=\"text-sm text-gray-600\">\n                  显示 {filteredRules.length} / {config?.rules.length || 0} 条规则\n                </div>\n              </div>\n            </div>\n            <div className=\"overflow-x-auto\">\n              <table className=\"table\">\n                <thead>\n                  <tr>\n                    <th>规则信息</th>\n                    <th>类型</th>\n                    <th>严重程度</th>\n                    <th>优先级</th>\n                    <th>状态</th>\n                    <th>配置详情</th>\n                  </tr>\n                </thead>\n                <tbody>\n                  {filteredRules.map((rule) => {\n                    const typeNames: Record<string, string> = {\n                      'FIELD_NAME': '字段名称',\n                      'PATTERN': '正则模式',\n                      'CONTENT_TYPE': '内容类型',\n                      'CUSTOM': '自定义'\n                    };\n                    const severityNames: Record<string, string> = {\n                      'CRITICAL': '严重',\n                      'HIGH': '高',\n                      'MEDIUM': '中',\n                      'LOW': '低'\n                    };\n\n                    return (\n                      <tr key={rule.id}>\n                        <td>\n                          <div className=\"space-y-1\">\n                            <div className=\"font-semibold text-gray-900\">{rule.name}</div>\n                            <div className=\"text-sm text-gray-600\">{truncateText(rule.description, 80)}</div>\n                            <div className=\"text-xs text-gray-400 font-mono\">ID: {rule.id}</div>\n                          </div>\n                        </td>\n                        <td>\n                          <span className={`badge ${getRuleTypeColor(rule.type)}`}>\n                            <span className=\"mr-1\">{getRuleTypeIcon(rule.type)}</span>\n                            {typeNames[rule.type] || rule.type}\n                          </span>\n                        </td>\n                        <td>\n                          <span className={`badge ${getSeverityBadgeColor(rule.severity)}`}>\n                            {severityNames[rule.severity] || rule.severity}\n                          </span>\n                        </td>\n                        <td>\n                          <div className=\"flex items-center\">\n                            <div className=\"w-8 h-8 rounded-full bg-gray-100 flex items-center justify-center text-sm font-semibold text-gray-700\">\n                              {rule.priority}\n                            </div>\n                          </div>\n                        </td>\n                        <td>\n                          <div className=\"flex items-center\">\n                            {rule.enabled ? (\n                              <>\n                                <div className=\"w-2 h-2 bg-green-400 rounded-full mr-2 animate-pulse\"></div>\n                                <span className=\"text-sm font-medium text-green-600\">已启用</span>\n                              </>\n                            ) : (\n                              <>\n                                <div className=\"w-2 h-2 bg-gray-400 rounded-full mr-2\"></div>\n                                <span className=\"text-sm font-medium text-gray-500\">已禁用</span>\n                              </>\n                            )}\n                          </div>\n                        </td>\n                        <td>\n                          <div className=\"space-y-2 text-sm\">\n                            {rule.fieldNames && rule.fieldNames.length > 0 && (\n                              <div>\n                                <span className=\"font-medium text-gray-700\">字段:</span>\n                                <span className=\"ml-1 text-gray-600\">\n                                  {rule.fieldNames.slice(0, 2).join(', ')}\n                                  {rule.fieldNames.length > 2 && ` +${rule.fieldNames.length - 2}个`}\n                                </span>\n                              </div>\n                            )}\n                            {rule.pattern && (\n                              <div>\n                                <span className=\"font-medium text-gray-700\">模式:</span>\n                                <code className=\"ml-1 text-xs\">{truncateText(rule.pattern, 30)}</code>\n                              </div>\n                            )}\n                            <div>\n                              <span className=\"font-medium text-gray-700\">掩码:</span>\n                              <code className=\"ml-1 text-xs\">{rule.maskValue}</code>\n                            </div>\n                            {rule.includeServices && rule.includeServices.length > 0 && (\n                              <div>\n                                <span className=\"font-medium text-gray-700\">适用服务:</span>\n                                <span className=\"ml-1 text-gray-600\">{rule.includeServices.join(', ')}</span>\n                              </div>\n                            )}\n                          </div>\n                        </td>\n                      </tr>\n                    );\n                  })}\n                </tbody>\n              </table>\n\n              {filteredRules.length === 0 && (\n                <div className=\"text-center py-12\">\n                  <Shield className=\"mx-auto h-12 w-12 text-gray-400\" />\n                  <h3 className=\"mt-2 text-sm font-medium text-gray-900\">没有找到匹配的规则</h3>\n                  <p className=\"mt-1 text-sm text-gray-500\">请尝试调整筛选条件</p>\n                </div>\n              )}\n            </div>\n          </div>\n        </div>\n      </div>\n    </div>\n  );\n};\n\nexport default RulesList;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,MAAM,EAAEC,SAAS,EAAEC,QAAQ,EAAEC,MAAM,QAAQ,cAAc;AAClE,SAASC,eAAe,QAAQ,iBAAiB;AAEjD,SAASC,qBAAqB,EAAEC,gBAAgB,EAAEC,eAAe,EAAEC,YAAY,EAAEC,YAAY,QAAQ,UAAU;AAC/G,OAAOC,KAAK,MAAM,iBAAiB;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAEpC,MAAMC,SAAmB,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAChC,MAAM,CAACC,MAAM,EAAEC,SAAS,CAAC,GAAGpB,QAAQ,CAA4B,IAAI,CAAC;EACrE,MAAM,CAACqB,aAAa,EAAEC,gBAAgB,CAAC,GAAGtB,QAAQ,CAAqB,EAAE,CAAC;EAC1E,MAAM,CAACuB,OAAO,EAAEC,UAAU,CAAC,GAAGxB,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAM,CAACyB,UAAU,EAAEC,aAAa,CAAC,GAAG1B,QAAQ,CAAC,KAAK,CAAC;EACnD,MAAM,CAAC2B,UAAU,EAAEC,aAAa,CAAC,GAAG5B,QAAQ,CAAC,EAAE,CAAC;EAChD,MAAM,CAAC6B,YAAY,EAAEC,eAAe,CAAC,GAAG9B,QAAQ,CAAmB,KAAK,CAAC;EACzE,MAAM,CAAC+B,gBAAgB,EAAEC,mBAAmB,CAAC,GAAGhC,QAAQ,CAAwB,KAAK,CAAC;EACtF,MAAM,CAACiC,eAAe,EAAEC,kBAAkB,CAAC,GAAGlC,QAAQ,CAAC,KAAK,CAAC;EAC7D,MAAM,CAACmC,WAAW,EAAEC,cAAc,CAAC,GAAGpC,QAAQ,CAAC,EAAE,CAAC;EAElD,MAAMqC,UAAU,GAAG,MAAAA,CAAA,KAAY;IAC7B,IAAI;MACF,MAAMC,IAAI,GAAG,MAAMhC,eAAe,CAACiC,QAAQ,CAACJ,WAAW,IAAIK,SAAS,CAAC;MACrEpB,SAAS,CAACkB,IAAI,CAAC;MACfhB,gBAAgB,CAACgB,IAAI,CAACG,KAAK,CAAC;IAC9B,CAAC,CAAC,OAAOC,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,wBAAwB,EAAEA,KAAK,CAAC;MAC9C9B,KAAK,CAAC8B,KAAK,CAAC,sBAAsB,CAAC;IACrC,CAAC,SAAS;MACRlB,UAAU,CAAC,KAAK,CAAC;MACjBE,aAAa,CAAC,KAAK,CAAC;IACtB;EACF,CAAC;EAED,MAAMkB,aAAa,GAAG,MAAAA,CAAA,KAAY;IAChClB,aAAa,CAAC,IAAI,CAAC;IACnB,MAAMW,UAAU,CAAC,CAAC;EACpB,CAAC;EAED,MAAMQ,kBAAkB,GAAG,MAAAA,CAAA,KAAY;IACrC,IAAI;MACF,MAAMvC,eAAe,CAACwC,WAAW,CAAC,CAAC;MACnClC,KAAK,CAACmC,OAAO,CAAC,qCAAqC,CAAC;MACpD,MAAMV,UAAU,CAAC,CAAC;IACpB,CAAC,CAAC,OAAOK,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,0BAA0B,EAAEA,KAAK,CAAC;MAChD9B,KAAK,CAAC8B,KAAK,CAAC,gCAAgC,CAAC;IAC/C;EACF,CAAC;EAED,MAAMM,YAAY,GAAGA,CAAA,KAAM;IACzB,IAAI7B,MAAM,EAAE;MACVR,YAAY,CAACQ,MAAM,EAAE,uBAAuB8B,IAAI,CAACC,GAAG,CAAC,CAAC,OAAO,CAAC;MAC9DtC,KAAK,CAACmC,OAAO,CAAC,qCAAqC,CAAC;IACtD;EACF,CAAC;EAED,MAAMI,WAAW,GAAGA,CAAA,KAAM;IACxB,IAAI,CAAChC,MAAM,EAAE;IAEb,IAAIiC,QAAQ,GAAGjC,MAAM,CAACsB,KAAK;;IAE3B;IACA,IAAId,UAAU,EAAE;MACdyB,QAAQ,GAAGA,QAAQ,CAACC,MAAM,CAACC,IAAI,IAC7BA,IAAI,CAACC,IAAI,CAACC,WAAW,CAAC,CAAC,CAACC,QAAQ,CAAC9B,UAAU,CAAC6B,WAAW,CAAC,CAAC,CAAC,IAC1DF,IAAI,CAACI,WAAW,CAACF,WAAW,CAAC,CAAC,CAACC,QAAQ,CAAC9B,UAAU,CAAC6B,WAAW,CAAC,CAAC,CAAC,IACjEF,IAAI,CAACK,EAAE,CAACH,WAAW,CAAC,CAAC,CAACC,QAAQ,CAAC9B,UAAU,CAAC6B,WAAW,CAAC,CAAC,CACzD,CAAC;IACH;;IAEA;IACA,IAAI3B,YAAY,KAAK,KAAK,EAAE;MAC1BuB,QAAQ,GAAGA,QAAQ,CAACC,MAAM,CAACC,IAAI,IAAIA,IAAI,CAACM,IAAI,KAAK/B,YAAY,CAAC;IAChE;;IAEA;IACA,IAAIE,gBAAgB,KAAK,KAAK,EAAE;MAC9BqB,QAAQ,GAAGA,QAAQ,CAACC,MAAM,CAACC,IAAI,IAAIA,IAAI,CAACO,QAAQ,KAAK9B,gBAAgB,CAAC;IACxE;;IAEA;IACA,IAAIE,eAAe,EAAE;MACnBmB,QAAQ,GAAGA,QAAQ,CAACC,MAAM,CAACC,IAAI,IAAIA,IAAI,CAACQ,OAAO,CAAC;IAClD;IAEAxC,gBAAgB,CAAC8B,QAAQ,CAAC;EAC5B,CAAC;EAEDnD,SAAS,CAAC,MAAM;IACdoC,UAAU,CAAC,CAAC;EACd,CAAC,EAAE,CAACF,WAAW,CAAC,CAAC,CAAC,CAAC;;EAEnBlC,SAAS,CAAC,MAAM;IACdkD,WAAW,CAAC,CAAC;EACf,CAAC,EAAE,CAAChC,MAAM,EAAEQ,UAAU,EAAEE,YAAY,EAAEE,gBAAgB,EAAEE,eAAe,CAAC,CAAC,CAAC,CAAC;;EAE3E,IAAIV,OAAO,EAAE;IACX,oBACET,OAAA;MAAKiD,SAAS,EAAC,uCAAuC;MAAAC,QAAA,eACpDlD,OAAA;QAAKiD,SAAS,EAAC;MAAmE;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACtF,CAAC;EAEV;EAEA,oBACEtD,OAAA;IAAKiD,SAAS,EAAC,yBAAyB;IAAAC,QAAA,eACtClD,OAAA;MAAKiD,SAAS,EAAC,6CAA6C;MAAAC,QAAA,eAC1DlD,OAAA;QAAKiD,SAAS,EAAC,mBAAmB;QAAAC,QAAA,gBAEhClD,OAAA;UAAKiD,SAAS,EAAC,0DAA0D;UAAAC,QAAA,eACvElD,OAAA;YAAKiD,SAAS,EAAC,8DAA8D;YAAAC,QAAA,gBAC3ElD,OAAA;cAAKiD,SAAS,EAAC,cAAc;cAAAC,QAAA,gBAC3BlD,OAAA;gBAAIiD,SAAS,EAAC,uCAAuC;gBAAAC,QAAA,gBACnDlD,OAAA,CAACT,MAAM;kBAAC0D,SAAS,EAAC;gBAA4C;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,wCAEnE;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACLtD,OAAA;gBAAGiD,SAAS,EAAC,uBAAuB;gBAAAC,QAAA,EAAC;cAAW;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACjD,CAAC,eACNtD,OAAA;cAAKiD,SAAS,EAAC,mCAAmC;cAAAC,QAAA,gBAChDlD,OAAA;gBACEuD,OAAO,EAAErB,YAAa;gBACtBe,SAAS,EAAC,mBAAmB;gBAAAC,QAAA,gBAE7BlD,OAAA,CAACV,QAAQ;kBAAC2D,SAAS,EAAC;gBAAc;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,4BAEvC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eACTtD,OAAA;gBACEuD,OAAO,EAAExB,kBAAmB;gBAC5BkB,SAAS,EAAC,kDAAkD;gBAAAC,QAAA,gBAE5DlD,OAAA,CAACX,SAAS;kBAAC4D,SAAS,EAAC;gBAAc;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,4BAExC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eACTtD,OAAA;gBACEuD,OAAO,EAAEzB,aAAc;gBACvB0B,QAAQ,EAAE7C,UAAW;gBACrBsC,SAAS,EAAC,iBAAiB;gBAAAC,QAAA,gBAE3BlD,OAAA,CAACX,SAAS;kBAAC4D,SAAS,EAAE,gBAAgBtC,UAAU,GAAG,cAAc,GAAG,EAAE;gBAAG;kBAAAwC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,gBAE9E;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAGNtD,OAAA;UAAKiD,SAAS,EAAC,MAAM;UAAAC,QAAA,eACnBlD,OAAA;YAAKiD,SAAS,EAAC,WAAW;YAAAC,QAAA,gBACxBlD,OAAA;cAAIiD,SAAS,EAAC,4DAA4D;cAAAC,QAAA,gBACxElD,OAAA;gBAAKiD,SAAS,EAAC;cAA0C;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,4BAElE;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACLtD,OAAA;cAAKiD,SAAS,EAAC,sDAAsD;cAAAC,QAAA,gBAEnElD,OAAA;gBAAKiD,SAAS,EAAC,eAAe;gBAAAC,QAAA,gBAC5BlD,OAAA;kBAAOyD,OAAO,EAAC,QAAQ;kBAACR,SAAS,EAAC,8CAA8C;kBAAAC,QAAA,EAAC;gBAEjF;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eACRtD,OAAA;kBAAKiD,SAAS,EAAC,UAAU;kBAAAC,QAAA,gBACvBlD,OAAA,CAACZ,MAAM;oBAAC6D,SAAS,EAAC;kBAA0E;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,eAC/FtD,OAAA;oBACE8C,IAAI,EAAC,MAAM;oBACXD,EAAE,EAAC,QAAQ;oBACXa,KAAK,EAAE7C,UAAW;oBAClB8C,QAAQ,EAAGC,CAAC,IAAK9C,aAAa,CAAC8C,CAAC,CAACC,MAAM,CAACH,KAAK,CAAE;oBAC/CI,WAAW,EAAC,mEAAiB;oBAC7Bb,SAAS,EAAC;kBAAkB;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC7B,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACC,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eAGNtD,OAAA;gBAAAkD,QAAA,gBACElD,OAAA;kBAAOyD,OAAO,EAAC,SAAS;kBAACR,SAAS,EAAC,8CAA8C;kBAAAC,QAAA,EAAC;gBAElF;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eACRtD,OAAA;kBACE8C,IAAI,EAAC,MAAM;kBACXD,EAAE,EAAC,SAAS;kBACZa,KAAK,EAAErC,WAAY;kBACnBsC,QAAQ,EAAGC,CAAC,IAAKtC,cAAc,CAACsC,CAAC,CAACC,MAAM,CAACH,KAAK,CAAE;kBAChDI,WAAW,EAAC,sCAAQ;kBACpBb,SAAS,EAAC;gBAAY;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACvB,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC,eAGNtD,OAAA;gBAAAkD,QAAA,gBACElD,OAAA;kBAAOyD,OAAO,EAAC,MAAM;kBAACR,SAAS,EAAC,8CAA8C;kBAAAC,QAAA,EAAC;gBAE/E;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eACRtD,OAAA;kBACE6C,EAAE,EAAC,MAAM;kBACTa,KAAK,EAAE3C,YAAa;kBACpB4C,QAAQ,EAAGC,CAAC,IAAK5C,eAAe,CAAC4C,CAAC,CAACC,MAAM,CAACH,KAAyB,CAAE;kBACrET,SAAS,EAAC,wBAAwB;kBAAAC,QAAA,gBAElClD,OAAA;oBAAQ0D,KAAK,EAAC,KAAK;oBAAAR,QAAA,EAAC;kBAAI;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC,eACjCtD,OAAA;oBAAQ0D,KAAK,EAAC,YAAY;oBAAAR,QAAA,EAAC;kBAAI;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC,eACxCtD,OAAA;oBAAQ0D,KAAK,EAAC,SAAS;oBAAAR,QAAA,EAAC;kBAAI;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC,eACrCtD,OAAA;oBAAQ0D,KAAK,EAAC,cAAc;oBAAAR,QAAA,EAAC;kBAAI;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC,eAC1CtD,OAAA;oBAAQ0D,KAAK,EAAC,QAAQ;oBAAAR,QAAA,EAAC;kBAAG;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC7B,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACN,CAAC,eAGNtD,OAAA;gBAAAkD,QAAA,gBACElD,OAAA;kBAAOyD,OAAO,EAAC,UAAU;kBAACR,SAAS,EAAC,8CAA8C;kBAAAC,QAAA,EAAC;gBAEnF;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eACRtD,OAAA;kBACE6C,EAAE,EAAC,UAAU;kBACba,KAAK,EAAEzC,gBAAiB;kBACxB0C,QAAQ,EAAGC,CAAC,IAAK1C,mBAAmB,CAAC0C,CAAC,CAACC,MAAM,CAACH,KAA8B,CAAE;kBAC9ET,SAAS,EAAC,wBAAwB;kBAAAC,QAAA,gBAElClD,OAAA;oBAAQ0D,KAAK,EAAC,KAAK;oBAAAR,QAAA,EAAC;kBAAI;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC,eACjCtD,OAAA;oBAAQ0D,KAAK,EAAC,UAAU;oBAAAR,QAAA,EAAC;kBAAE;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC,eACpCtD,OAAA;oBAAQ0D,KAAK,EAAC,MAAM;oBAAAR,QAAA,EAAC;kBAAC;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC,eAC/BtD,OAAA;oBAAQ0D,KAAK,EAAC,QAAQ;oBAAAR,QAAA,EAAC;kBAAC;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC,eACjCtD,OAAA;oBAAQ0D,KAAK,EAAC,KAAK;oBAAAR,QAAA,EAAC;kBAAC;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACxB,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACN,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eAGNtD,OAAA;cAAKiD,SAAS,EAAC,oCAAoC;cAAAC,QAAA,eACjDlD,OAAA;gBAAOiD,SAAS,EAAC,mBAAmB;gBAAAC,QAAA,gBAClClD,OAAA;kBACE8C,IAAI,EAAC,UAAU;kBACfiB,OAAO,EAAE5C,eAAgB;kBACzBwC,QAAQ,EAAGC,CAAC,IAAKxC,kBAAkB,CAACwC,CAAC,CAACC,MAAM,CAACE,OAAO,CAAE;kBACtDd,SAAS,EAAC;gBAAqI;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAChJ,CAAC,eACFtD,OAAA;kBAAMiD,SAAS,EAAC,wCAAwC;kBAAAC,QAAA,EAAC;gBAAQ;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACnE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACL,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAGNtD,OAAA;UAAKiD,SAAS,EAAC,sBAAsB;UAAAC,QAAA,gBACnClD,OAAA;YAAKiD,SAAS,EAAC,+CAA+C;YAAAC,QAAA,eAC5DlD,OAAA;cAAKiD,SAAS,EAAC,mCAAmC;cAAAC,QAAA,gBAChDlD,OAAA;gBAAIiD,SAAS,EAAC,qCAAqC;gBAAAC,QAAA,EAAC;cAEpD;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACLtD,OAAA;gBAAKiD,SAAS,EAAC,uBAAuB;gBAAAC,QAAA,GAAC,eAClC,EAAC3C,aAAa,CAACyD,MAAM,EAAC,KAAG,EAAC,CAAA3D,MAAM,aAANA,MAAM,uBAANA,MAAM,CAAEsB,KAAK,CAACqC,MAAM,KAAI,CAAC,EAAC,qBACzD;cAAA;gBAAAb,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eACNtD,OAAA;YAAKiD,SAAS,EAAC,iBAAiB;YAAAC,QAAA,gBAC9BlD,OAAA;cAAOiD,SAAS,EAAC,OAAO;cAAAC,QAAA,gBACtBlD,OAAA;gBAAAkD,QAAA,eACElD,OAAA;kBAAAkD,QAAA,gBACElD,OAAA;oBAAAkD,QAAA,EAAI;kBAAI;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eACbtD,OAAA;oBAAAkD,QAAA,EAAI;kBAAE;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eACXtD,OAAA;oBAAAkD,QAAA,EAAI;kBAAI;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eACbtD,OAAA;oBAAAkD,QAAA,EAAI;kBAAG;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eACZtD,OAAA;oBAAAkD,QAAA,EAAI;kBAAE;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eACXtD,OAAA;oBAAAkD,QAAA,EAAI;kBAAI;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACX;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACA,CAAC,eACRtD,OAAA;gBAAAkD,QAAA,EACG3C,aAAa,CAAC0D,GAAG,CAAEzB,IAAI,IAAK;kBAC3B,MAAM0B,SAAiC,GAAG;oBACxC,YAAY,EAAE,MAAM;oBACpB,SAAS,EAAE,MAAM;oBACjB,cAAc,EAAE,MAAM;oBACtB,QAAQ,EAAE;kBACZ,CAAC;kBACD,MAAMC,aAAqC,GAAG;oBAC5C,UAAU,EAAE,IAAI;oBAChB,MAAM,EAAE,GAAG;oBACX,QAAQ,EAAE,GAAG;oBACb,KAAK,EAAE;kBACT,CAAC;kBAED,oBACEnE,OAAA;oBAAAkD,QAAA,gBACElD,OAAA;sBAAAkD,QAAA,eACElD,OAAA;wBAAKiD,SAAS,EAAC,WAAW;wBAAAC,QAAA,gBACxBlD,OAAA;0BAAKiD,SAAS,EAAC,6BAA6B;0BAAAC,QAAA,EAAEV,IAAI,CAACC;wBAAI;0BAAAU,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAM,CAAC,eAC9DtD,OAAA;0BAAKiD,SAAS,EAAC,uBAAuB;0BAAAC,QAAA,EAAEtD,YAAY,CAAC4C,IAAI,CAACI,WAAW,EAAE,EAAE;wBAAC;0BAAAO,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAM,CAAC,eACjFtD,OAAA;0BAAKiD,SAAS,EAAC,iCAAiC;0BAAAC,QAAA,GAAC,MAAI,EAACV,IAAI,CAACK,EAAE;wBAAA;0BAAAM,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAM,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACjE;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACJ,CAAC,eACLtD,OAAA;sBAAAkD,QAAA,eACElD,OAAA;wBAAMiD,SAAS,EAAE,SAASvD,gBAAgB,CAAC8C,IAAI,CAACM,IAAI,CAAC,EAAG;wBAAAI,QAAA,gBACtDlD,OAAA;0BAAMiD,SAAS,EAAC,MAAM;0BAAAC,QAAA,EAAEvD,eAAe,CAAC6C,IAAI,CAACM,IAAI;wBAAC;0BAAAK,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAO,CAAC,EACzDY,SAAS,CAAC1B,IAAI,CAACM,IAAI,CAAC,IAAIN,IAAI,CAACM,IAAI;sBAAA;wBAAAK,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAC9B;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACL,CAAC,eACLtD,OAAA;sBAAAkD,QAAA,eACElD,OAAA;wBAAMiD,SAAS,EAAE,SAASxD,qBAAqB,CAAC+C,IAAI,CAACO,QAAQ,CAAC,EAAG;wBAAAG,QAAA,EAC9DiB,aAAa,CAAC3B,IAAI,CAACO,QAAQ,CAAC,IAAIP,IAAI,CAACO;sBAAQ;wBAAAI,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAC1C;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACL,CAAC,eACLtD,OAAA;sBAAAkD,QAAA,eACElD,OAAA;wBAAKiD,SAAS,EAAC,mBAAmB;wBAAAC,QAAA,eAChClD,OAAA;0BAAKiD,SAAS,EAAC,uGAAuG;0BAAAC,QAAA,EACnHV,IAAI,CAAC4B;wBAAQ;0BAAAjB,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACX;sBAAC;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACH;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACJ,CAAC,eACLtD,OAAA;sBAAAkD,QAAA,eACElD,OAAA;wBAAKiD,SAAS,EAAC,mBAAmB;wBAAAC,QAAA,EAC/BV,IAAI,CAACQ,OAAO,gBACXhD,OAAA,CAAAE,SAAA;0BAAAgD,QAAA,gBACElD,OAAA;4BAAKiD,SAAS,EAAC;0BAAsD;4BAAAE,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAM,CAAC,eAC5EtD,OAAA;4BAAMiD,SAAS,EAAC,oCAAoC;4BAAAC,QAAA,EAAC;0BAAG;4BAAAC,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAM,CAAC;wBAAA,eAC/D,CAAC,gBAEHtD,OAAA,CAAAE,SAAA;0BAAAgD,QAAA,gBACElD,OAAA;4BAAKiD,SAAS,EAAC;0BAAuC;4BAAAE,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAM,CAAC,eAC7DtD,OAAA;4BAAMiD,SAAS,EAAC,mCAAmC;4BAAAC,QAAA,EAAC;0BAAG;4BAAAC,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAM,CAAC;wBAAA,eAC9D;sBACH;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACE;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACJ,CAAC,eACLtD,OAAA;sBAAAkD,QAAA,eACElD,OAAA;wBAAKiD,SAAS,EAAC,mBAAmB;wBAAAC,QAAA,GAC/BV,IAAI,CAAC6B,UAAU,IAAI7B,IAAI,CAAC6B,UAAU,CAACL,MAAM,GAAG,CAAC,iBAC5ChE,OAAA;0BAAAkD,QAAA,gBACElD,OAAA;4BAAMiD,SAAS,EAAC,2BAA2B;4BAAAC,QAAA,EAAC;0BAAG;4BAAAC,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAM,CAAC,eACtDtD,OAAA;4BAAMiD,SAAS,EAAC,oBAAoB;4BAAAC,QAAA,GACjCV,IAAI,CAAC6B,UAAU,CAACC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAACC,IAAI,CAAC,IAAI,CAAC,EACtC/B,IAAI,CAAC6B,UAAU,CAACL,MAAM,GAAG,CAAC,IAAI,KAAKxB,IAAI,CAAC6B,UAAU,CAACL,MAAM,GAAG,CAAC,GAAG;0BAAA;4BAAAb,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAC7D,CAAC;wBAAA;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACJ,CACN,EACAd,IAAI,CAACgC,OAAO,iBACXxE,OAAA;0BAAAkD,QAAA,gBACElD,OAAA;4BAAMiD,SAAS,EAAC,2BAA2B;4BAAAC,QAAA,EAAC;0BAAG;4BAAAC,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAM,CAAC,eACtDtD,OAAA;4BAAMiD,SAAS,EAAC,cAAc;4BAAAC,QAAA,EAAEtD,YAAY,CAAC4C,IAAI,CAACgC,OAAO,EAAE,EAAE;0BAAC;4BAAArB,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAO,CAAC;wBAAA;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACnE,CACN,eACDtD,OAAA;0BAAAkD,QAAA,gBACElD,OAAA;4BAAMiD,SAAS,EAAC,2BAA2B;4BAAAC,QAAA,EAAC;0BAAG;4BAAAC,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAM,CAAC,eACtDtD,OAAA;4BAAMiD,SAAS,EAAC,cAAc;4BAAAC,QAAA,EAAEV,IAAI,CAACiC;0BAAS;4BAAAtB,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAO,CAAC;wBAAA;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACnD,CAAC,EACLd,IAAI,CAACkC,eAAe,IAAIlC,IAAI,CAACkC,eAAe,CAACV,MAAM,GAAG,CAAC,iBACtDhE,OAAA;0BAAAkD,QAAA,gBACElD,OAAA;4BAAMiD,SAAS,EAAC,2BAA2B;4BAAAC,QAAA,EAAC;0BAAK;4BAAAC,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAM,CAAC,eACxDtD,OAAA;4BAAMiD,SAAS,EAAC,oBAAoB;4BAAAC,QAAA,EAAEV,IAAI,CAACkC,eAAe,CAACH,IAAI,CAAC,IAAI;0BAAC;4BAAApB,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAO,CAAC;wBAAA;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAC1E,CACN;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACE;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACJ,CAAC;kBAAA,GArEEd,IAAI,CAACK,EAAE;oBAAAM,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAsEZ,CAAC;gBAET,CAAC;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACG,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,EAEP/C,aAAa,CAACyD,MAAM,KAAK,CAAC,iBACzBhE,OAAA;cAAKiD,SAAS,EAAC,mBAAmB;cAAAC,QAAA,gBAChClD,OAAA,CAACT,MAAM;gBAAC0D,SAAS,EAAC;cAAiC;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eACtDtD,OAAA;gBAAIiD,SAAS,EAAC,wCAAwC;gBAAAC,QAAA,EAAC;cAAS;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACrEtD,OAAA;gBAAGiD,SAAS,EAAC,4BAA4B;gBAAAC,QAAA,EAAC;cAAS;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACpD,CACN;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAAClD,EAAA,CAzWID,SAAmB;AAAAwE,EAAA,GAAnBxE,SAAmB;AA2WzB,eAAeA,SAAS;AAAC,IAAAwE,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}