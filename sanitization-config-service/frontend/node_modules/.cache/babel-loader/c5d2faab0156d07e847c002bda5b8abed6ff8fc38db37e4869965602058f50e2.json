{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"M13 13.74a2 2 0 0 1-2 0L2.5 8.87a1 1 0 0 1 0-1.74L11 2.26a2 2 0 0 1 2 0l8.5 4.87a1 1 0 0 1 0 1.74z\",\n  key: \"15q6uc\"\n}], [\"path\", {\n  d: \"m20 14.285 1.5.845a1 1 0 0 1 0 1.74L13 21.74a2 2 0 0 1-2 0l-8.5-4.87a1 1 0 0 1 0-1.74l1.5-.845\",\n  key: \"byia6g\"\n}]];\nconst Layers2 = createLucideIcon(\"layers-2\", __iconNode);\nexport { __iconNode, Layers2 as default };\n//# sourceMappingURL=layers-2.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}