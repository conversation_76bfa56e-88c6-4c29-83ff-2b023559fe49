{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"rect\", {\n  width: \"20\",\n  height: \"20\",\n  x: \"2\",\n  y: \"2\",\n  rx: \"5\",\n  ry: \"5\",\n  key: \"2e1cvw\"\n}], [\"path\", {\n  d: \"M16 11.37A4 4 0 1 1 12.63 8 4 4 0 0 1 16 11.37z\",\n  key: \"9exkf1\"\n}], [\"line\", {\n  x1: \"17.5\",\n  x2: \"17.51\",\n  y1: \"6.5\",\n  y2: \"6.5\",\n  key: \"r4j83e\"\n}]];\nconst Instagram = createLucideIcon(\"instagram\", __iconNode);\nexport { __iconNode, Instagram as default };\n//# sourceMappingURL=instagram.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}