{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"M12 2v1\",\n  key: \"11qlp1\"\n}], [\"path\", {\n  d: \"M15.5 21a1.85 1.85 0 0 1-3.5-1v-8H2a10 10 0 0 1 3.428-6.575\",\n  key: \"eki10q\"\n}], [\"path\", {\n  d: \"M17.5 12H22A10 10 0 0 0 9.004 3.455\",\n  key: \"n2ayka\"\n}], [\"path\", {\n  d: \"m2 2 20 20\",\n  key: \"1ooewy\"\n}]];\nconst UmbrellaOff = createLucideIcon(\"umbrella-off\", __iconNode);\nexport { __iconNode, UmbrellaOff as default };\n//# sourceMappingURL=umbrella-off.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}