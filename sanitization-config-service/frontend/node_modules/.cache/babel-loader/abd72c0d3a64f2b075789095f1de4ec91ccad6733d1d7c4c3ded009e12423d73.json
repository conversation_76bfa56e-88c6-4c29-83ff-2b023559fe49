{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"M14.536 21.686a.5.5 0 0 0 .937-.024l6.5-19a.496.496 0 0 0-.635-.635l-19 6.5a.5.5 0 0 0-.024.937l7.93 3.18a2 2 0 0 1 1.112 1.11z\",\n  key: \"1ffxy3\"\n}], [\"path\", {\n  d: \"m21.854 2.147-10.94 10.939\",\n  key: \"12cjpa\"\n}]];\nconst Send = createLucideIcon(\"send\", __iconNode);\nexport { __iconNode, Send as default };\n//# sourceMappingURL=send.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}