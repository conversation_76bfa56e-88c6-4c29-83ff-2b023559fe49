{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"M12.4 2.7a2.5 2.5 0 0 1 3.4 0l5.5 5.5a2.5 2.5 0 0 1 0 3.4l-3.7 3.7a2.5 2.5 0 0 1-3.4 0L8.7 9.8a2.5 2.5 0 0 1 0-3.4z\",\n  key: \"165ttr\"\n}], [\"path\", {\n  d: \"m14 7 3 3\",\n  key: \"1r5n42\"\n}], [\"path\", {\n  d: \"m9.4 10.6-6.814 6.814A2 2 0 0 0 2 18.828V21a1 1 0 0 0 1 1h3a1 1 0 0 0 1-1v-1a1 1 0 0 1 1-1h1a1 1 0 0 0 1-1v-1a1 1 0 0 1 1-1h.172a2 2 0 0 0 1.414-.586l.814-.814\",\n  key: \"1ubxi2\"\n}]];\nconst KeySquare = createLucideIcon(\"key-square\", __iconNode);\nexport { __iconNode, KeySquare as default };\n//# sourceMappingURL=key-square.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}