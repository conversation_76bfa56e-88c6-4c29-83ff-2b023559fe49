{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"M14.828 14.828 21 21\",\n  key: \"ar5fw7\"\n}], [\"path\", {\n  d: \"M21 16v5h-5\",\n  key: \"1ck2sf\"\n}], [\"path\", {\n  d: \"m21 3-9 9-4-4-6 6\",\n  key: \"1h02xo\"\n}], [\"path\", {\n  d: \"M21 8V3h-5\",\n  key: \"1qoq8a\"\n}]];\nconst TrendingUpDown = createLucideIcon(\"trending-up-down\", __iconNode);\nexport { __iconNode, TrendingUpDown as default };\n//# sourceMappingURL=trending-up-down.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}