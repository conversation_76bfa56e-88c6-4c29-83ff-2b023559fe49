{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"M21 15a2 2 0 0 1-2 2H7l-4 4V5a2 2 0 0 1 2-2h14a2 2 0 0 1 2 2z\",\n  key: \"1lielz\"\n}], [\"path\", {\n  d: \"m14.5 7.5-5 5\",\n  key: \"3lb6iw\"\n}], [\"path\", {\n  d: \"m9.5 7.5 5 5\",\n  key: \"ko136h\"\n}]];\nconst MessageSquareX = createLucideIcon(\"message-square-x\", __iconNode);\nexport { __iconNode, MessageSquareX as default };\n//# sourceMappingURL=message-square-x.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}