{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"polygon\", {\n  points: \"5 4 15 12 5 20 5 4\",\n  key: \"16p6eg\"\n}], [\"line\", {\n  x1: \"19\",\n  x2: \"19\",\n  y1: \"5\",\n  y2: \"19\",\n  key: \"futhcm\"\n}]];\nconst SkipForward = createLucideIcon(\"skip-forward\", __iconNode);\nexport { __iconNode, SkipForward as default };\n//# sourceMappingURL=skip-forward.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}