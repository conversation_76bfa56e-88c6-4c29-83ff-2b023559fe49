{"ast": null, "code": "var _jsxFileName = \"/Users/<USER>/dev/work/lab/javaagent/sanitization-config-service/frontend/src/components/ConfigManager.tsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { RefreshCw, Download, Settings, AlertCircle } from 'lucide-react';\nimport { sanitizationApi } from '../services/api';\nimport { formatTimestamp, downloadJson } from '../utils';\nimport toast from 'react-hot-toast';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst ConfigManager = () => {\n  _s();\n  const [config, setConfig] = useState(null);\n  const [loading, setLoading] = useState(true);\n  const [saving, setSaving] = useState(false);\n  const [refreshing, setRefreshing] = useState(false);\n  const fetchConfig = async () => {\n    try {\n      const data = await sanitizationApi.getRules();\n      setConfig(data);\n    } catch (error) {\n      console.error('Failed to fetch config:', error);\n      toast.error('Failed to load configuration');\n    } finally {\n      setLoading(false);\n      setRefreshing(false);\n    }\n  };\n  const handleRefresh = async () => {\n    setRefreshing(true);\n    await fetchConfig();\n  };\n  const handleReloadConfig = async () => {\n    try {\n      setSaving(true);\n      await sanitizationApi.reloadRules();\n      toast.success('Configuration reloaded successfully');\n      await fetchConfig();\n    } catch (error) {\n      console.error('Failed to reload config:', error);\n      toast.error('Failed to reload configuration');\n    } finally {\n      setSaving(false);\n    }\n  };\n  const handleExport = () => {\n    if (config) {\n      downloadJson(config, `sanitization-config-${Date.now()}.json`);\n      toast.success('Configuration exported successfully');\n    }\n  };\n  const handleConfigChange = (field, value) => {\n    if (config) {\n      setConfig({\n        ...config,\n        [field]: value,\n        timestamp: Date.now()\n      });\n    }\n  };\n  const handleGlobalSettingChange = (key, value) => {\n    if (config) {\n      setConfig({\n        ...config,\n        globalSettings: {\n          ...config.globalSettings,\n          [key]: value\n        },\n        timestamp: Date.now()\n      });\n    }\n  };\n  useEffect(() => {\n    fetchConfig();\n  }, []);\n  if (loading) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"flex items-center justify-center h-64\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"animate-spin rounded-full h-12 w-12 border-b-2 border-primary-600\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 83,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 82,\n      columnNumber: 7\n    }, this);\n  }\n  if (!config) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"text-center py-12\",\n      children: [/*#__PURE__*/_jsxDEV(AlertCircle, {\n        className: \"mx-auto h-12 w-12 text-gray-400\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 91,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n        className: \"mt-2 text-sm font-medium text-gray-900\",\n        children: \"No configuration found\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 92,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n        className: \"mt-1 text-sm text-gray-500\",\n        children: \"Unable to load the sanitization configuration.\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 93,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 90,\n      columnNumber: 7\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"min-h-screen bg-gray-50\",\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"space-y-8 fade-in\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"bg-white rounded-xl shadow-sm border border-gray-200 p-6\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex flex-col sm:flex-row sm:items-center sm:justify-between\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"mb-4 sm:mb-0\",\n              children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n                className: \"text-3xl font-bold text-gray-900 mb-2\",\n                children: [/*#__PURE__*/_jsxDEV(Settings, {\n                  className: \"inline-block h-8 w-8 text-primary-600 mr-3\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 107,\n                  columnNumber: 19\n                }, this), \"\\u914D\\u7F6E\\u7BA1\\u7406\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 106,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"text-gray-600 text-lg\",\n                children: \"\\u7BA1\\u7406\\u5168\\u5C40\\u8131\\u654F\\u8BBE\\u7F6E\\u548C\\u914D\\u7F6E\\u53C2\\u6570\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 110,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 105,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex flex-wrap items-center gap-3\",\n              children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                onClick: handleExport,\n                className: \"btn btn-secondary\",\n                children: [/*#__PURE__*/_jsxDEV(Download, {\n                  className: \"h-4 w-4 mr-2\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 117,\n                  columnNumber: 19\n                }, this), \"\\u5BFC\\u51FA\\u914D\\u7F6E\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 113,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                onClick: handleReloadConfig,\n                disabled: saving,\n                className: \"btn bg-yellow-600 text-white hover:bg-yellow-700\",\n                children: [/*#__PURE__*/_jsxDEV(RefreshCw, {\n                  className: `h-4 w-4 mr-2 ${saving ? 'animate-spin' : ''}`\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 125,\n                  columnNumber: 19\n                }, this), \"\\u91CD\\u8F7D\\u914D\\u7F6E\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 120,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                onClick: handleRefresh,\n                disabled: refreshing,\n                className: \"btn btn-primary\",\n                children: [/*#__PURE__*/_jsxDEV(RefreshCw, {\n                  className: `h-4 w-4 mr-2 ${refreshing ? 'animate-spin' : ''}`\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 133,\n                  columnNumber: 19\n                }, this), \"\\u5237\\u65B0\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 128,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 112,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 104,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 103,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"bg-white shadow rounded-lg p-6\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-center mb-4\",\n            children: [/*#__PURE__*/_jsxDEV(Settings, {\n              className: \"h-5 w-5 text-gray-400 mr-2\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 143,\n              columnNumber: 11\n            }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n              className: \"text-lg font-medium text-gray-900\",\n              children: \"Configuration Information\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 144,\n              columnNumber: 11\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 142,\n            columnNumber: 9\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"grid grid-cols-1 md:grid-cols-3 gap-4\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(\"dt\", {\n                className: \"text-sm font-medium text-gray-500\",\n                children: \"Version\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 148,\n                columnNumber: 13\n              }, this), /*#__PURE__*/_jsxDEV(\"dd\", {\n                className: \"mt-1 text-sm text-gray-900\",\n                children: config.version\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 149,\n                columnNumber: 13\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 147,\n              columnNumber: 11\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(\"dt\", {\n                className: \"text-sm font-medium text-gray-500\",\n                children: \"Last Updated\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 152,\n                columnNumber: 13\n              }, this), /*#__PURE__*/_jsxDEV(\"dd\", {\n                className: \"mt-1 text-sm text-gray-900\",\n                children: formatTimestamp(config.timestamp)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 153,\n                columnNumber: 13\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 151,\n              columnNumber: 11\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(\"dt\", {\n                className: \"text-sm font-medium text-gray-500\",\n                children: \"Total Rules\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 156,\n                columnNumber: 13\n              }, this), /*#__PURE__*/_jsxDEV(\"dd\", {\n                className: \"mt-1 text-sm text-gray-900\",\n                children: config.rules.length\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 157,\n                columnNumber: 13\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 155,\n              columnNumber: 11\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 146,\n            columnNumber: 9\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 141,\n          columnNumber: 7\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"bg-white shadow rounded-lg p-6\",\n          children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n            className: \"text-lg font-medium text-gray-900 mb-6\",\n            children: \"Global Settings\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 164,\n            columnNumber: 9\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"space-y-6\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"grid grid-cols-1 md:grid-cols-2 gap-6\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  className: \"flex items-center\",\n                  children: [/*#__PURE__*/_jsxDEV(\"input\", {\n                    type: \"checkbox\",\n                    checked: config.enabled,\n                    onChange: e => handleConfigChange('enabled', e.target.checked),\n                    className: \"rounded border-gray-300 text-primary-600 shadow-sm focus:border-primary-300 focus:ring focus:ring-primary-200 focus:ring-opacity-50\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 171,\n                    columnNumber: 17\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"ml-2 text-sm font-medium text-gray-700\",\n                    children: \"Enable Sanitization\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 177,\n                    columnNumber: 17\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 170,\n                  columnNumber: 15\n                }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"mt-1 text-sm text-gray-500\",\n                  children: \"Enable or disable the entire sanitization system\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 179,\n                  columnNumber: 15\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 169,\n                columnNumber: 13\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  className: \"flex items-center\",\n                  children: [/*#__PURE__*/_jsxDEV(\"input\", {\n                    type: \"checkbox\",\n                    checked: config.markersEnabled,\n                    onChange: e => handleConfigChange('markersEnabled', e.target.checked),\n                    className: \"rounded border-gray-300 text-primary-600 shadow-sm focus:border-primary-300 focus:ring focus:ring-primary-200 focus:ring-opacity-50\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 184,\n                    columnNumber: 17\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"ml-2 text-sm font-medium text-gray-700\",\n                    children: \"Enable Markers\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 190,\n                    columnNumber: 17\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 183,\n                  columnNumber: 15\n                }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"mt-1 text-sm text-gray-500\",\n                  children: \"Add markers to indicate sanitized data\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 192,\n                  columnNumber: 15\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 182,\n                columnNumber: 13\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 168,\n              columnNumber: 11\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                htmlFor: \"markerFormat\",\n                className: \"block text-sm font-medium text-gray-700 mb-1\",\n                children: \"Marker Format\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 198,\n                columnNumber: 13\n              }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n                id: \"markerFormat\",\n                value: config.markerFormat,\n                onChange: e => handleConfigChange('markerFormat', e.target.value),\n                className: \"block w-full md:w-1/3 border-gray-300 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500 sm:text-sm\",\n                children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                  value: \"BRACKET\",\n                  children: \"Bracket [SANITIZED]\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 207,\n                  columnNumber: 15\n                }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                  value: \"ASTERISK\",\n                  children: \"Asterisk *SANITIZED*\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 208,\n                  columnNumber: 15\n                }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                  value: \"HASH\",\n                  children: \"Hash #SANITIZED#\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 209,\n                  columnNumber: 15\n                }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                  value: \"NONE\",\n                  children: \"None\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 210,\n                  columnNumber: 15\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 201,\n                columnNumber: 13\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"mt-1 text-sm text-gray-500\",\n                children: \"Format for sanitization markers\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 212,\n                columnNumber: 13\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 197,\n              columnNumber: 11\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                htmlFor: \"version\",\n                className: \"block text-sm font-medium text-gray-700 mb-1\",\n                children: \"Configuration Version\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 217,\n                columnNumber: 13\n              }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                type: \"text\",\n                id: \"version\",\n                value: config.version,\n                onChange: e => handleConfigChange('version', e.target.value),\n                className: \"block w-full md:w-1/3 border-gray-300 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500 sm:text-sm\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 220,\n                columnNumber: 13\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"mt-1 text-sm text-gray-500\",\n                children: \"Version identifier for this configuration\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 227,\n                columnNumber: 13\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 216,\n              columnNumber: 11\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 166,\n            columnNumber: 9\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 163,\n          columnNumber: 7\n        }, this), config.globalSettings && /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"bg-white shadow rounded-lg p-6\",\n          children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n            className: \"text-lg font-medium text-gray-900 mb-6\",\n            children: \"Advanced Settings\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 235,\n            columnNumber: 11\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"space-y-6\",\n            children: Object.entries(config.globalSettings).map(([key, value]) => /*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                htmlFor: key,\n                className: \"block text-sm font-medium text-gray-700 mb-1\",\n                children: key.replace(/([A-Z])/g, ' $1').replace(/^./, str => str.toUpperCase())\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 240,\n                columnNumber: 17\n              }, this), typeof value === 'boolean' ? /*#__PURE__*/_jsxDEV(\"label\", {\n                className: \"flex items-center\",\n                children: [/*#__PURE__*/_jsxDEV(\"input\", {\n                  type: \"checkbox\",\n                  checked: value,\n                  onChange: e => handleGlobalSettingChange(key, e.target.checked),\n                  className: \"rounded border-gray-300 text-primary-600 shadow-sm focus:border-primary-300 focus:ring focus:ring-primary-200 focus:ring-opacity-50\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 245,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"ml-2 text-sm text-gray-700\",\n                  children: value ? 'Enabled' : 'Disabled'\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 251,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 244,\n                columnNumber: 19\n              }, this) : typeof value === 'number' ? /*#__PURE__*/_jsxDEV(\"input\", {\n                type: \"number\",\n                value: value,\n                onChange: e => handleGlobalSettingChange(key, parseInt(e.target.value)),\n                className: \"block w-full md:w-1/3 border-gray-300 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500 sm:text-sm\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 254,\n                columnNumber: 19\n              }, this) : /*#__PURE__*/_jsxDEV(\"input\", {\n                type: \"text\",\n                value: String(value),\n                onChange: e => handleGlobalSettingChange(key, e.target.value),\n                className: \"block w-full md:w-1/2 border-gray-300 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500 sm:text-sm\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 261,\n                columnNumber: 19\n              }, this)]\n            }, key, true, {\n              fileName: _jsxFileName,\n              lineNumber: 239,\n              columnNumber: 15\n            }, this))\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 237,\n            columnNumber: 11\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 234,\n          columnNumber: 9\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"bg-white shadow rounded-lg p-6\",\n          children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n            className: \"text-lg font-medium text-gray-900 mb-4\",\n            children: \"Configuration Preview\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 276,\n            columnNumber: 9\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"bg-gray-50 rounded-md p-4 overflow-x-auto\",\n            children: /*#__PURE__*/_jsxDEV(\"pre\", {\n              className: \"text-sm text-gray-800\",\n              children: JSON.stringify(config, null, 2)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 278,\n              columnNumber: 11\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 277,\n            columnNumber: 9\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 275,\n          columnNumber: 7\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 101,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 100,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 99,\n    columnNumber: 5\n  }, this);\n};\n_s(ConfigManager, \"2tboQzOa823mUXSOqQ86SJmET8A=\");\n_c = ConfigManager;\nexport default ConfigManager;\nvar _c;\n$RefreshReg$(_c, \"ConfigManager\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "RefreshCw", "Download", "Settings", "AlertCircle", "sanitizationApi", "formatTimestamp", "downloadJson", "toast", "jsxDEV", "_jsxDEV", "ConfigManager", "_s", "config", "setConfig", "loading", "setLoading", "saving", "setSaving", "refreshing", "setRefreshing", "fetchConfig", "data", "getRules", "error", "console", "handleRefresh", "handleReloadConfig", "reloadRules", "success", "handleExport", "Date", "now", "handleConfigChange", "field", "value", "timestamp", "handleGlobalSettingChange", "key", "globalSettings", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "onClick", "disabled", "version", "rules", "length", "type", "checked", "enabled", "onChange", "e", "target", "markersEnabled", "htmlFor", "id", "markerFormat", "Object", "entries", "map", "replace", "str", "toUpperCase", "parseInt", "String", "JSON", "stringify", "_c", "$RefreshReg$"], "sources": ["/Users/<USER>/dev/work/lab/javaagent/sanitization-config-service/frontend/src/components/ConfigManager.tsx"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { RefreshCw, Download, Settings, AlertCircle } from 'lucide-react';\nimport { sanitizationApi } from '../services/api';\nimport { SanitizationConfig } from '../types';\nimport { formatTimestamp, downloadJson } from '../utils';\nimport toast from 'react-hot-toast';\n\nconst ConfigManager: React.FC = () => {\n  const [config, setConfig] = useState<SanitizationConfig | null>(null);\n  const [loading, setLoading] = useState(true);\n  const [saving, setSaving] = useState(false);\n  const [refreshing, setRefreshing] = useState(false);\n\n  const fetchConfig = async () => {\n    try {\n      const data = await sanitizationApi.getRules();\n      setConfig(data);\n    } catch (error) {\n      console.error('Failed to fetch config:', error);\n      toast.error('Failed to load configuration');\n    } finally {\n      setLoading(false);\n      setRefreshing(false);\n    }\n  };\n\n  const handleRefresh = async () => {\n    setRefreshing(true);\n    await fetchConfig();\n  };\n\n  const handleReloadConfig = async () => {\n    try {\n      setSaving(true);\n      await sanitizationApi.reloadRules();\n      toast.success('Configuration reloaded successfully');\n      await fetchConfig();\n    } catch (error) {\n      console.error('Failed to reload config:', error);\n      toast.error('Failed to reload configuration');\n    } finally {\n      setSaving(false);\n    }\n  };\n\n  const handleExport = () => {\n    if (config) {\n      downloadJson(config, `sanitization-config-${Date.now()}.json`);\n      toast.success('Configuration exported successfully');\n    }\n  };\n\n  const handleConfigChange = (field: keyof SanitizationConfig, value: any) => {\n    if (config) {\n      setConfig({\n        ...config,\n        [field]: value,\n        timestamp: Date.now(),\n      });\n    }\n  };\n\n  const handleGlobalSettingChange = (key: string, value: any) => {\n    if (config) {\n      setConfig({\n        ...config,\n        globalSettings: {\n          ...config.globalSettings,\n          [key]: value,\n        },\n        timestamp: Date.now(),\n      });\n    }\n  };\n\n  useEffect(() => {\n    fetchConfig();\n  }, []);\n\n  if (loading) {\n    return (\n      <div className=\"flex items-center justify-center h-64\">\n        <div className=\"animate-spin rounded-full h-12 w-12 border-b-2 border-primary-600\"></div>\n      </div>\n    );\n  }\n\n  if (!config) {\n    return (\n      <div className=\"text-center py-12\">\n        <AlertCircle className=\"mx-auto h-12 w-12 text-gray-400\" />\n        <h3 className=\"mt-2 text-sm font-medium text-gray-900\">No configuration found</h3>\n        <p className=\"mt-1 text-sm text-gray-500\">Unable to load the sanitization configuration.</p>\n      </div>\n    );\n  }\n\n  return (\n    <div className=\"min-h-screen bg-gray-50\">\n      <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8\">\n        <div className=\"space-y-8 fade-in\">\n          {/* Header */}\n          <div className=\"bg-white rounded-xl shadow-sm border border-gray-200 p-6\">\n            <div className=\"flex flex-col sm:flex-row sm:items-center sm:justify-between\">\n              <div className=\"mb-4 sm:mb-0\">\n                <h1 className=\"text-3xl font-bold text-gray-900 mb-2\">\n                  <Settings className=\"inline-block h-8 w-8 text-primary-600 mr-3\" />\n                  配置管理\n                </h1>\n                <p className=\"text-gray-600 text-lg\">管理全局脱敏设置和配置参数</p>\n              </div>\n              <div className=\"flex flex-wrap items-center gap-3\">\n                <button\n                  onClick={handleExport}\n                  className=\"btn btn-secondary\"\n                >\n                  <Download className=\"h-4 w-4 mr-2\" />\n                  导出配置\n                </button>\n                <button\n                  onClick={handleReloadConfig}\n                  disabled={saving}\n                  className=\"btn bg-yellow-600 text-white hover:bg-yellow-700\"\n                >\n                  <RefreshCw className={`h-4 w-4 mr-2 ${saving ? 'animate-spin' : ''}`} />\n                  重载配置\n                </button>\n                <button\n                  onClick={handleRefresh}\n                  disabled={refreshing}\n                  className=\"btn btn-primary\"\n                >\n                  <RefreshCw className={`h-4 w-4 mr-2 ${refreshing ? 'animate-spin' : ''}`} />\n                  刷新\n                </button>\n              </div>\n            </div>\n          </div>\n\n      {/* Configuration Info */}\n      <div className=\"bg-white shadow rounded-lg p-6\">\n        <div className=\"flex items-center mb-4\">\n          <Settings className=\"h-5 w-5 text-gray-400 mr-2\" />\n          <h3 className=\"text-lg font-medium text-gray-900\">Configuration Information</h3>\n        </div>\n        <div className=\"grid grid-cols-1 md:grid-cols-3 gap-4\">\n          <div>\n            <dt className=\"text-sm font-medium text-gray-500\">Version</dt>\n            <dd className=\"mt-1 text-sm text-gray-900\">{config.version}</dd>\n          </div>\n          <div>\n            <dt className=\"text-sm font-medium text-gray-500\">Last Updated</dt>\n            <dd className=\"mt-1 text-sm text-gray-900\">{formatTimestamp(config.timestamp)}</dd>\n          </div>\n          <div>\n            <dt className=\"text-sm font-medium text-gray-500\">Total Rules</dt>\n            <dd className=\"mt-1 text-sm text-gray-900\">{config.rules.length}</dd>\n          </div>\n        </div>\n      </div>\n\n      {/* Global Settings */}\n      <div className=\"bg-white shadow rounded-lg p-6\">\n        <h3 className=\"text-lg font-medium text-gray-900 mb-6\">Global Settings</h3>\n        \n        <div className=\"space-y-6\">\n          {/* Basic Settings */}\n          <div className=\"grid grid-cols-1 md:grid-cols-2 gap-6\">\n            <div>\n              <label className=\"flex items-center\">\n                <input\n                  type=\"checkbox\"\n                  checked={config.enabled}\n                  onChange={(e) => handleConfigChange('enabled', e.target.checked)}\n                  className=\"rounded border-gray-300 text-primary-600 shadow-sm focus:border-primary-300 focus:ring focus:ring-primary-200 focus:ring-opacity-50\"\n                />\n                <span className=\"ml-2 text-sm font-medium text-gray-700\">Enable Sanitization</span>\n              </label>\n              <p className=\"mt-1 text-sm text-gray-500\">Enable or disable the entire sanitization system</p>\n            </div>\n\n            <div>\n              <label className=\"flex items-center\">\n                <input\n                  type=\"checkbox\"\n                  checked={config.markersEnabled}\n                  onChange={(e) => handleConfigChange('markersEnabled', e.target.checked)}\n                  className=\"rounded border-gray-300 text-primary-600 shadow-sm focus:border-primary-300 focus:ring focus:ring-primary-200 focus:ring-opacity-50\"\n                />\n                <span className=\"ml-2 text-sm font-medium text-gray-700\">Enable Markers</span>\n              </label>\n              <p className=\"mt-1 text-sm text-gray-500\">Add markers to indicate sanitized data</p>\n            </div>\n          </div>\n\n          {/* Marker Format */}\n          <div>\n            <label htmlFor=\"markerFormat\" className=\"block text-sm font-medium text-gray-700 mb-1\">\n              Marker Format\n            </label>\n            <select\n              id=\"markerFormat\"\n              value={config.markerFormat}\n              onChange={(e) => handleConfigChange('markerFormat', e.target.value)}\n              className=\"block w-full md:w-1/3 border-gray-300 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500 sm:text-sm\"\n            >\n              <option value=\"BRACKET\">Bracket [SANITIZED]</option>\n              <option value=\"ASTERISK\">Asterisk *SANITIZED*</option>\n              <option value=\"HASH\">Hash #SANITIZED#</option>\n              <option value=\"NONE\">None</option>\n            </select>\n            <p className=\"mt-1 text-sm text-gray-500\">Format for sanitization markers</p>\n          </div>\n\n          {/* Version */}\n          <div>\n            <label htmlFor=\"version\" className=\"block text-sm font-medium text-gray-700 mb-1\">\n              Configuration Version\n            </label>\n            <input\n              type=\"text\"\n              id=\"version\"\n              value={config.version}\n              onChange={(e) => handleConfigChange('version', e.target.value)}\n              className=\"block w-full md:w-1/3 border-gray-300 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500 sm:text-sm\"\n            />\n            <p className=\"mt-1 text-sm text-gray-500\">Version identifier for this configuration</p>\n          </div>\n        </div>\n      </div>\n\n      {/* Advanced Settings */}\n      {config.globalSettings && (\n        <div className=\"bg-white shadow rounded-lg p-6\">\n          <h3 className=\"text-lg font-medium text-gray-900 mb-6\">Advanced Settings</h3>\n          \n          <div className=\"space-y-6\">\n            {Object.entries(config.globalSettings).map(([key, value]) => (\n              <div key={key}>\n                <label htmlFor={key} className=\"block text-sm font-medium text-gray-700 mb-1\">\n                  {key.replace(/([A-Z])/g, ' $1').replace(/^./, str => str.toUpperCase())}\n                </label>\n                {typeof value === 'boolean' ? (\n                  <label className=\"flex items-center\">\n                    <input\n                      type=\"checkbox\"\n                      checked={value}\n                      onChange={(e) => handleGlobalSettingChange(key, e.target.checked)}\n                      className=\"rounded border-gray-300 text-primary-600 shadow-sm focus:border-primary-300 focus:ring focus:ring-primary-200 focus:ring-opacity-50\"\n                    />\n                    <span className=\"ml-2 text-sm text-gray-700\">{value ? 'Enabled' : 'Disabled'}</span>\n                  </label>\n                ) : typeof value === 'number' ? (\n                  <input\n                    type=\"number\"\n                    value={value}\n                    onChange={(e) => handleGlobalSettingChange(key, parseInt(e.target.value))}\n                    className=\"block w-full md:w-1/3 border-gray-300 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500 sm:text-sm\"\n                  />\n                ) : (\n                  <input\n                    type=\"text\"\n                    value={String(value)}\n                    onChange={(e) => handleGlobalSettingChange(key, e.target.value)}\n                    className=\"block w-full md:w-1/2 border-gray-300 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500 sm:text-sm\"\n                  />\n                )}\n              </div>\n            ))}\n          </div>\n        </div>\n      )}\n\n      {/* Configuration Preview */}\n      <div className=\"bg-white shadow rounded-lg p-6\">\n        <h3 className=\"text-lg font-medium text-gray-900 mb-4\">Configuration Preview</h3>\n        <div className=\"bg-gray-50 rounded-md p-4 overflow-x-auto\">\n          <pre className=\"text-sm text-gray-800\">\n            {JSON.stringify(config, null, 2)}\n          </pre>\n        </div>\n      </div>\n    </div>\n  </div>\n</div>\n  );\n};\n\nexport default ConfigManager;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,SAAS,EAAEC,QAAQ,EAAEC,QAAQ,EAAEC,WAAW,QAAQ,cAAc;AACzE,SAASC,eAAe,QAAQ,iBAAiB;AAEjD,SAASC,eAAe,EAAEC,YAAY,QAAQ,UAAU;AACxD,OAAOC,KAAK,MAAM,iBAAiB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEpC,MAAMC,aAAuB,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACpC,MAAM,CAACC,MAAM,EAAEC,SAAS,CAAC,GAAGf,QAAQ,CAA4B,IAAI,CAAC;EACrE,MAAM,CAACgB,OAAO,EAAEC,UAAU,CAAC,GAAGjB,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAM,CAACkB,MAAM,EAAEC,SAAS,CAAC,GAAGnB,QAAQ,CAAC,KAAK,CAAC;EAC3C,MAAM,CAACoB,UAAU,EAAEC,aAAa,CAAC,GAAGrB,QAAQ,CAAC,KAAK,CAAC;EAEnD,MAAMsB,WAAW,GAAG,MAAAA,CAAA,KAAY;IAC9B,IAAI;MACF,MAAMC,IAAI,GAAG,MAAMjB,eAAe,CAACkB,QAAQ,CAAC,CAAC;MAC7CT,SAAS,CAACQ,IAAI,CAAC;IACjB,CAAC,CAAC,OAAOE,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,yBAAyB,EAAEA,KAAK,CAAC;MAC/ChB,KAAK,CAACgB,KAAK,CAAC,8BAA8B,CAAC;IAC7C,CAAC,SAAS;MACRR,UAAU,CAAC,KAAK,CAAC;MACjBI,aAAa,CAAC,KAAK,CAAC;IACtB;EACF,CAAC;EAED,MAAMM,aAAa,GAAG,MAAAA,CAAA,KAAY;IAChCN,aAAa,CAAC,IAAI,CAAC;IACnB,MAAMC,WAAW,CAAC,CAAC;EACrB,CAAC;EAED,MAAMM,kBAAkB,GAAG,MAAAA,CAAA,KAAY;IACrC,IAAI;MACFT,SAAS,CAAC,IAAI,CAAC;MACf,MAAMb,eAAe,CAACuB,WAAW,CAAC,CAAC;MACnCpB,KAAK,CAACqB,OAAO,CAAC,qCAAqC,CAAC;MACpD,MAAMR,WAAW,CAAC,CAAC;IACrB,CAAC,CAAC,OAAOG,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,0BAA0B,EAAEA,KAAK,CAAC;MAChDhB,KAAK,CAACgB,KAAK,CAAC,gCAAgC,CAAC;IAC/C,CAAC,SAAS;MACRN,SAAS,CAAC,KAAK,CAAC;IAClB;EACF,CAAC;EAED,MAAMY,YAAY,GAAGA,CAAA,KAAM;IACzB,IAAIjB,MAAM,EAAE;MACVN,YAAY,CAACM,MAAM,EAAE,uBAAuBkB,IAAI,CAACC,GAAG,CAAC,CAAC,OAAO,CAAC;MAC9DxB,KAAK,CAACqB,OAAO,CAAC,qCAAqC,CAAC;IACtD;EACF,CAAC;EAED,MAAMI,kBAAkB,GAAGA,CAACC,KAA+B,EAAEC,KAAU,KAAK;IAC1E,IAAItB,MAAM,EAAE;MACVC,SAAS,CAAC;QACR,GAAGD,MAAM;QACT,CAACqB,KAAK,GAAGC,KAAK;QACdC,SAAS,EAAEL,IAAI,CAACC,GAAG,CAAC;MACtB,CAAC,CAAC;IACJ;EACF,CAAC;EAED,MAAMK,yBAAyB,GAAGA,CAACC,GAAW,EAAEH,KAAU,KAAK;IAC7D,IAAItB,MAAM,EAAE;MACVC,SAAS,CAAC;QACR,GAAGD,MAAM;QACT0B,cAAc,EAAE;UACd,GAAG1B,MAAM,CAAC0B,cAAc;UACxB,CAACD,GAAG,GAAGH;QACT,CAAC;QACDC,SAAS,EAAEL,IAAI,CAACC,GAAG,CAAC;MACtB,CAAC,CAAC;IACJ;EACF,CAAC;EAEDhC,SAAS,CAAC,MAAM;IACdqB,WAAW,CAAC,CAAC;EACf,CAAC,EAAE,EAAE,CAAC;EAEN,IAAIN,OAAO,EAAE;IACX,oBACEL,OAAA;MAAK8B,SAAS,EAAC,uCAAuC;MAAAC,QAAA,eACpD/B,OAAA;QAAK8B,SAAS,EAAC;MAAmE;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACtF,CAAC;EAEV;EAEA,IAAI,CAAChC,MAAM,EAAE;IACX,oBACEH,OAAA;MAAK8B,SAAS,EAAC,mBAAmB;MAAAC,QAAA,gBAChC/B,OAAA,CAACN,WAAW;QAACoC,SAAS,EAAC;MAAiC;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eAC3DnC,OAAA;QAAI8B,SAAS,EAAC,wCAAwC;QAAAC,QAAA,EAAC;MAAsB;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eAClFnC,OAAA;QAAG8B,SAAS,EAAC,4BAA4B;QAAAC,QAAA,EAAC;MAA8C;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAG,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACzF,CAAC;EAEV;EAEA,oBACEnC,OAAA;IAAK8B,SAAS,EAAC,yBAAyB;IAAAC,QAAA,eACtC/B,OAAA;MAAK8B,SAAS,EAAC,6CAA6C;MAAAC,QAAA,eAC1D/B,OAAA;QAAK8B,SAAS,EAAC,mBAAmB;QAAAC,QAAA,gBAEhC/B,OAAA;UAAK8B,SAAS,EAAC,0DAA0D;UAAAC,QAAA,eACvE/B,OAAA;YAAK8B,SAAS,EAAC,8DAA8D;YAAAC,QAAA,gBAC3E/B,OAAA;cAAK8B,SAAS,EAAC,cAAc;cAAAC,QAAA,gBAC3B/B,OAAA;gBAAI8B,SAAS,EAAC,uCAAuC;gBAAAC,QAAA,gBACnD/B,OAAA,CAACP,QAAQ;kBAACqC,SAAS,EAAC;gBAA4C;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,4BAErE;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACLnC,OAAA;gBAAG8B,SAAS,EAAC,uBAAuB;gBAAAC,QAAA,EAAC;cAAa;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACnD,CAAC,eACNnC,OAAA;cAAK8B,SAAS,EAAC,mCAAmC;cAAAC,QAAA,gBAChD/B,OAAA;gBACEoC,OAAO,EAAEhB,YAAa;gBACtBU,SAAS,EAAC,mBAAmB;gBAAAC,QAAA,gBAE7B/B,OAAA,CAACR,QAAQ;kBAACsC,SAAS,EAAC;gBAAc;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,4BAEvC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eACTnC,OAAA;gBACEoC,OAAO,EAAEnB,kBAAmB;gBAC5BoB,QAAQ,EAAE9B,MAAO;gBACjBuB,SAAS,EAAC,kDAAkD;gBAAAC,QAAA,gBAE5D/B,OAAA,CAACT,SAAS;kBAACuC,SAAS,EAAE,gBAAgBvB,MAAM,GAAG,cAAc,GAAG,EAAE;gBAAG;kBAAAyB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,4BAE1E;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eACTnC,OAAA;gBACEoC,OAAO,EAAEpB,aAAc;gBACvBqB,QAAQ,EAAE5B,UAAW;gBACrBqB,SAAS,EAAC,iBAAiB;gBAAAC,QAAA,gBAE3B/B,OAAA,CAACT,SAAS;kBAACuC,SAAS,EAAE,gBAAgBrB,UAAU,GAAG,cAAc,GAAG,EAAE;gBAAG;kBAAAuB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,gBAE9E;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAGVnC,OAAA;UAAK8B,SAAS,EAAC,gCAAgC;UAAAC,QAAA,gBAC7C/B,OAAA;YAAK8B,SAAS,EAAC,wBAAwB;YAAAC,QAAA,gBACrC/B,OAAA,CAACP,QAAQ;cAACqC,SAAS,EAAC;YAA4B;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eACnDnC,OAAA;cAAI8B,SAAS,EAAC,mCAAmC;cAAAC,QAAA,EAAC;YAAyB;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC7E,CAAC,eACNnC,OAAA;YAAK8B,SAAS,EAAC,uCAAuC;YAAAC,QAAA,gBACpD/B,OAAA;cAAA+B,QAAA,gBACE/B,OAAA;gBAAI8B,SAAS,EAAC,mCAAmC;gBAAAC,QAAA,EAAC;cAAO;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eAC9DnC,OAAA;gBAAI8B,SAAS,EAAC,4BAA4B;gBAAAC,QAAA,EAAE5B,MAAM,CAACmC;cAAO;gBAAAN,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC7D,CAAC,eACNnC,OAAA;cAAA+B,QAAA,gBACE/B,OAAA;gBAAI8B,SAAS,EAAC,mCAAmC;gBAAAC,QAAA,EAAC;cAAY;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACnEnC,OAAA;gBAAI8B,SAAS,EAAC,4BAA4B;gBAAAC,QAAA,EAAEnC,eAAe,CAACO,MAAM,CAACuB,SAAS;cAAC;gBAAAM,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAChF,CAAC,eACNnC,OAAA;cAAA+B,QAAA,gBACE/B,OAAA;gBAAI8B,SAAS,EAAC,mCAAmC;gBAAAC,QAAA,EAAC;cAAW;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eAClEnC,OAAA;gBAAI8B,SAAS,EAAC,4BAA4B;gBAAAC,QAAA,EAAE5B,MAAM,CAACoC,KAAK,CAACC;cAAM;gBAAAR,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAClE,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAGNnC,OAAA;UAAK8B,SAAS,EAAC,gCAAgC;UAAAC,QAAA,gBAC7C/B,OAAA;YAAI8B,SAAS,EAAC,wCAAwC;YAAAC,QAAA,EAAC;UAAe;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAE3EnC,OAAA;YAAK8B,SAAS,EAAC,WAAW;YAAAC,QAAA,gBAExB/B,OAAA;cAAK8B,SAAS,EAAC,uCAAuC;cAAAC,QAAA,gBACpD/B,OAAA;gBAAA+B,QAAA,gBACE/B,OAAA;kBAAO8B,SAAS,EAAC,mBAAmB;kBAAAC,QAAA,gBAClC/B,OAAA;oBACEyC,IAAI,EAAC,UAAU;oBACfC,OAAO,EAAEvC,MAAM,CAACwC,OAAQ;oBACxBC,QAAQ,EAAGC,CAAC,IAAKtB,kBAAkB,CAAC,SAAS,EAAEsB,CAAC,CAACC,MAAM,CAACJ,OAAO,CAAE;oBACjEZ,SAAS,EAAC;kBAAqI;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAChJ,CAAC,eACFnC,OAAA;oBAAM8B,SAAS,EAAC,wCAAwC;oBAAAC,QAAA,EAAC;kBAAmB;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC9E,CAAC,eACRnC,OAAA;kBAAG8B,SAAS,EAAC,4BAA4B;kBAAAC,QAAA,EAAC;gBAAgD;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAG,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC3F,CAAC,eAENnC,OAAA;gBAAA+B,QAAA,gBACE/B,OAAA;kBAAO8B,SAAS,EAAC,mBAAmB;kBAAAC,QAAA,gBAClC/B,OAAA;oBACEyC,IAAI,EAAC,UAAU;oBACfC,OAAO,EAAEvC,MAAM,CAAC4C,cAAe;oBAC/BH,QAAQ,EAAGC,CAAC,IAAKtB,kBAAkB,CAAC,gBAAgB,EAAEsB,CAAC,CAACC,MAAM,CAACJ,OAAO,CAAE;oBACxEZ,SAAS,EAAC;kBAAqI;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAChJ,CAAC,eACFnC,OAAA;oBAAM8B,SAAS,EAAC,wCAAwC;oBAAAC,QAAA,EAAC;kBAAc;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACzE,CAAC,eACRnC,OAAA;kBAAG8B,SAAS,EAAC,4BAA4B;kBAAAC,QAAA,EAAC;gBAAsC;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAG,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACjF,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eAGNnC,OAAA;cAAA+B,QAAA,gBACE/B,OAAA;gBAAOgD,OAAO,EAAC,cAAc;gBAAClB,SAAS,EAAC,8CAA8C;gBAAAC,QAAA,EAAC;cAEvF;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eACRnC,OAAA;gBACEiD,EAAE,EAAC,cAAc;gBACjBxB,KAAK,EAAEtB,MAAM,CAAC+C,YAAa;gBAC3BN,QAAQ,EAAGC,CAAC,IAAKtB,kBAAkB,CAAC,cAAc,EAAEsB,CAAC,CAACC,MAAM,CAACrB,KAAK,CAAE;gBACpEK,SAAS,EAAC,uHAAuH;gBAAAC,QAAA,gBAEjI/B,OAAA;kBAAQyB,KAAK,EAAC,SAAS;kBAAAM,QAAA,EAAC;gBAAmB;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eACpDnC,OAAA;kBAAQyB,KAAK,EAAC,UAAU;kBAAAM,QAAA,EAAC;gBAAoB;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eACtDnC,OAAA;kBAAQyB,KAAK,EAAC,MAAM;kBAAAM,QAAA,EAAC;gBAAgB;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eAC9CnC,OAAA;kBAAQyB,KAAK,EAAC,MAAM;kBAAAM,QAAA,EAAC;gBAAI;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC5B,CAAC,eACTnC,OAAA;gBAAG8B,SAAS,EAAC,4BAA4B;gBAAAC,QAAA,EAAC;cAA+B;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC1E,CAAC,eAGNnC,OAAA;cAAA+B,QAAA,gBACE/B,OAAA;gBAAOgD,OAAO,EAAC,SAAS;gBAAClB,SAAS,EAAC,8CAA8C;gBAAAC,QAAA,EAAC;cAElF;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eACRnC,OAAA;gBACEyC,IAAI,EAAC,MAAM;gBACXQ,EAAE,EAAC,SAAS;gBACZxB,KAAK,EAAEtB,MAAM,CAACmC,OAAQ;gBACtBM,QAAQ,EAAGC,CAAC,IAAKtB,kBAAkB,CAAC,SAAS,EAAEsB,CAAC,CAACC,MAAM,CAACrB,KAAK,CAAE;gBAC/DK,SAAS,EAAC;cAAuH;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAClI,CAAC,eACFnC,OAAA;gBAAG8B,SAAS,EAAC,4BAA4B;gBAAAC,QAAA,EAAC;cAAyC;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACpF,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,EAGLhC,MAAM,CAAC0B,cAAc,iBACpB7B,OAAA;UAAK8B,SAAS,EAAC,gCAAgC;UAAAC,QAAA,gBAC7C/B,OAAA;YAAI8B,SAAS,EAAC,wCAAwC;YAAAC,QAAA,EAAC;UAAiB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAE7EnC,OAAA;YAAK8B,SAAS,EAAC,WAAW;YAAAC,QAAA,EACvBoB,MAAM,CAACC,OAAO,CAACjD,MAAM,CAAC0B,cAAc,CAAC,CAACwB,GAAG,CAAC,CAAC,CAACzB,GAAG,EAAEH,KAAK,CAAC,kBACtDzB,OAAA;cAAA+B,QAAA,gBACE/B,OAAA;gBAAOgD,OAAO,EAAEpB,GAAI;gBAACE,SAAS,EAAC,8CAA8C;gBAAAC,QAAA,EAC1EH,GAAG,CAAC0B,OAAO,CAAC,UAAU,EAAE,KAAK,CAAC,CAACA,OAAO,CAAC,IAAI,EAAEC,GAAG,IAAIA,GAAG,CAACC,WAAW,CAAC,CAAC;cAAC;gBAAAxB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAClE,CAAC,EACP,OAAOV,KAAK,KAAK,SAAS,gBACzBzB,OAAA;gBAAO8B,SAAS,EAAC,mBAAmB;gBAAAC,QAAA,gBAClC/B,OAAA;kBACEyC,IAAI,EAAC,UAAU;kBACfC,OAAO,EAAEjB,KAAM;kBACfmB,QAAQ,EAAGC,CAAC,IAAKlB,yBAAyB,CAACC,GAAG,EAAEiB,CAAC,CAACC,MAAM,CAACJ,OAAO,CAAE;kBAClEZ,SAAS,EAAC;gBAAqI;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAChJ,CAAC,eACFnC,OAAA;kBAAM8B,SAAS,EAAC,4BAA4B;kBAAAC,QAAA,EAAEN,KAAK,GAAG,SAAS,GAAG;gBAAU;kBAAAO,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC/E,CAAC,GACN,OAAOV,KAAK,KAAK,QAAQ,gBAC3BzB,OAAA;gBACEyC,IAAI,EAAC,QAAQ;gBACbhB,KAAK,EAAEA,KAAM;gBACbmB,QAAQ,EAAGC,CAAC,IAAKlB,yBAAyB,CAACC,GAAG,EAAE6B,QAAQ,CAACZ,CAAC,CAACC,MAAM,CAACrB,KAAK,CAAC,CAAE;gBAC1EK,SAAS,EAAC;cAAuH;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAClI,CAAC,gBAEFnC,OAAA;gBACEyC,IAAI,EAAC,MAAM;gBACXhB,KAAK,EAAEiC,MAAM,CAACjC,KAAK,CAAE;gBACrBmB,QAAQ,EAAGC,CAAC,IAAKlB,yBAAyB,CAACC,GAAG,EAAEiB,CAAC,CAACC,MAAM,CAACrB,KAAK,CAAE;gBAChEK,SAAS,EAAC;cAAuH;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAClI,CACF;YAAA,GA5BOP,GAAG;cAAAI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OA6BR,CACN;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CACN,eAGDnC,OAAA;UAAK8B,SAAS,EAAC,gCAAgC;UAAAC,QAAA,gBAC7C/B,OAAA;YAAI8B,SAAS,EAAC,wCAAwC;YAAAC,QAAA,EAAC;UAAqB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACjFnC,OAAA;YAAK8B,SAAS,EAAC,2CAA2C;YAAAC,QAAA,eACxD/B,OAAA;cAAK8B,SAAS,EAAC,uBAAuB;cAAAC,QAAA,EACnC4B,IAAI,CAACC,SAAS,CAACzD,MAAM,EAAE,IAAI,EAAE,CAAC;YAAC;cAAA6B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC7B;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEN,CAAC;AAACjC,EAAA,CAvRID,aAAuB;AAAA4D,EAAA,GAAvB5D,aAAuB;AAyR7B,eAAeA,aAAa;AAAC,IAAA4D,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}