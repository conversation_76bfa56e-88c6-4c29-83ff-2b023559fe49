{"ast": null, "code": "export const formatTimestamp = timestamp => {\n  return new Date(timestamp * 1000).toLocaleString();\n};\nexport const getSeverityColor = severity => {\n  switch (severity) {\n    case 'CRITICAL':\n      return 'text-red-600 bg-red-50 border-red-200';\n    case 'HIGH':\n      return 'text-orange-600 bg-orange-50 border-orange-200';\n    case 'MEDIUM':\n      return 'text-yellow-600 bg-yellow-50 border-yellow-200';\n    case 'LOW':\n      return 'text-green-600 bg-green-50 border-green-200';\n    default:\n      return 'text-gray-600 bg-gray-50 border-gray-200';\n  }\n};\nexport const getSeverityBadgeColor = severity => {\n  switch (severity) {\n    case 'CRITICAL':\n      return 'bg-red-100 text-red-800';\n    case 'HIGH':\n      return 'bg-orange-100 text-orange-800';\n    case 'MEDIUM':\n      return 'bg-yellow-100 text-yellow-800';\n    case 'LOW':\n      return 'bg-green-100 text-green-800';\n    default:\n      return 'bg-gray-100 text-gray-800';\n  }\n};\nexport const getRuleTypeColor = type => {\n  switch (type) {\n    case 'FIELD_NAME':\n      return 'bg-blue-100 text-blue-800';\n    case 'PATTERN':\n      return 'bg-purple-100 text-purple-800';\n    case 'CONTENT_TYPE':\n      return 'bg-indigo-100 text-indigo-800';\n    case 'CUSTOM':\n      return 'bg-gray-100 text-gray-800';\n    default:\n      return 'bg-gray-100 text-gray-800';\n  }\n};\nexport const getRuleTypeIcon = type => {\n  switch (type) {\n    case 'FIELD_NAME':\n      return '🏷️';\n    case 'PATTERN':\n      return '🔍';\n    case 'CONTENT_TYPE':\n      return '📄';\n    case 'CUSTOM':\n      return '⚙️';\n    default:\n      return '❓';\n  }\n};\nexport const truncateText = (text, maxLength) => {\n  if (text.length <= maxLength) return text;\n  return text.substring(0, maxLength) + '...';\n};\nexport const copyToClipboard = async text => {\n  try {\n    await navigator.clipboard.writeText(text);\n    return true;\n  } catch (err) {\n    console.error('Failed to copy text: ', err);\n    return false;\n  }\n};\nexport const downloadJson = (data, filename) => {\n  const jsonString = JSON.stringify(data, null, 2);\n  const blob = new Blob([jsonString], {\n    type: 'application/json'\n  });\n  const url = URL.createObjectURL(blob);\n  const link = document.createElement('a');\n  link.href = url;\n  link.download = filename;\n  document.body.appendChild(link);\n  link.click();\n  document.body.removeChild(link);\n  URL.revokeObjectURL(url);\n};", "map": {"version": 3, "names": ["formatTimestamp", "timestamp", "Date", "toLocaleString", "getSeverityColor", "severity", "getSeverityBadgeColor", "getRuleTypeColor", "type", "getRuleTypeIcon", "truncateText", "text", "max<PERSON><PERSON><PERSON>", "length", "substring", "copyToClipboard", "navigator", "clipboard", "writeText", "err", "console", "error", "downloadJson", "data", "filename", "jsonString", "JSON", "stringify", "blob", "Blob", "url", "URL", "createObjectURL", "link", "document", "createElement", "href", "download", "body", "append<PERSON><PERSON><PERSON>", "click", "<PERSON><PERSON><PERSON><PERSON>", "revokeObjectURL"], "sources": ["/Users/<USER>/dev/work/lab/javaagent/sanitization-config-service/frontend/src/utils/index.ts"], "sourcesContent": ["import { SeverityLevel, RuleType } from '../types';\n\nexport const formatTimestamp = (timestamp: number): string => {\n  return new Date(timestamp * 1000).toLocaleString();\n};\n\nexport const getSeverityColor = (severity: SeverityLevel): string => {\n  switch (severity) {\n    case 'CRITICAL':\n      return 'text-red-600 bg-red-50 border-red-200';\n    case 'HIGH':\n      return 'text-orange-600 bg-orange-50 border-orange-200';\n    case 'MEDIUM':\n      return 'text-yellow-600 bg-yellow-50 border-yellow-200';\n    case 'LOW':\n      return 'text-green-600 bg-green-50 border-green-200';\n    default:\n      return 'text-gray-600 bg-gray-50 border-gray-200';\n  }\n};\n\nexport const getSeverityBadgeColor = (severity: SeverityLevel): string => {\n  switch (severity) {\n    case 'CRITICAL':\n      return 'bg-red-100 text-red-800';\n    case 'HIGH':\n      return 'bg-orange-100 text-orange-800';\n    case 'MEDIUM':\n      return 'bg-yellow-100 text-yellow-800';\n    case 'LOW':\n      return 'bg-green-100 text-green-800';\n    default:\n      return 'bg-gray-100 text-gray-800';\n  }\n};\n\nexport const getRuleTypeColor = (type: RuleType): string => {\n  switch (type) {\n    case 'FIELD_NAME':\n      return 'bg-blue-100 text-blue-800';\n    case 'PATTERN':\n      return 'bg-purple-100 text-purple-800';\n    case 'CONTENT_TYPE':\n      return 'bg-indigo-100 text-indigo-800';\n    case 'CUSTOM':\n      return 'bg-gray-100 text-gray-800';\n    default:\n      return 'bg-gray-100 text-gray-800';\n  }\n};\n\nexport const getRuleTypeIcon = (type: RuleType): string => {\n  switch (type) {\n    case 'FIELD_NAME':\n      return '🏷️';\n    case 'PATTERN':\n      return '🔍';\n    case 'CONTENT_TYPE':\n      return '📄';\n    case 'CUSTOM':\n      return '⚙️';\n    default:\n      return '❓';\n  }\n};\n\nexport const truncateText = (text: string, maxLength: number): string => {\n  if (text.length <= maxLength) return text;\n  return text.substring(0, maxLength) + '...';\n};\n\nexport const copyToClipboard = async (text: string): Promise<boolean> => {\n  try {\n    await navigator.clipboard.writeText(text);\n    return true;\n  } catch (err) {\n    console.error('Failed to copy text: ', err);\n    return false;\n  }\n};\n\nexport const downloadJson = (data: any, filename: string): void => {\n  const jsonString = JSON.stringify(data, null, 2);\n  const blob = new Blob([jsonString], { type: 'application/json' });\n  const url = URL.createObjectURL(blob);\n  \n  const link = document.createElement('a');\n  link.href = url;\n  link.download = filename;\n  document.body.appendChild(link);\n  link.click();\n  document.body.removeChild(link);\n  \n  URL.revokeObjectURL(url);\n};\n"], "mappings": "AAEA,OAAO,MAAMA,eAAe,GAAIC,SAAiB,IAAa;EAC5D,OAAO,IAAIC,IAAI,CAACD,SAAS,GAAG,IAAI,CAAC,CAACE,cAAc,CAAC,CAAC;AACpD,CAAC;AAED,OAAO,MAAMC,gBAAgB,GAAIC,QAAuB,IAAa;EACnE,QAAQA,QAAQ;IACd,KAAK,UAAU;MACb,OAAO,uCAAuC;IAChD,KAAK,MAAM;MACT,OAAO,gDAAgD;IACzD,KAAK,QAAQ;MACX,OAAO,gDAAgD;IACzD,KAAK,KAAK;MACR,OAAO,6CAA6C;IACtD;MACE,OAAO,0CAA0C;EACrD;AACF,CAAC;AAED,OAAO,MAAMC,qBAAqB,GAAID,QAAuB,IAAa;EACxE,QAAQA,QAAQ;IACd,KAAK,UAAU;MACb,OAAO,yBAAyB;IAClC,KAAK,MAAM;MACT,OAAO,+BAA+B;IACxC,KAAK,QAAQ;MACX,OAAO,+BAA+B;IACxC,KAAK,KAAK;MACR,OAAO,6BAA6B;IACtC;MACE,OAAO,2BAA2B;EACtC;AACF,CAAC;AAED,OAAO,MAAME,gBAAgB,GAAIC,IAAc,IAAa;EAC1D,QAAQA,IAAI;IACV,KAAK,YAAY;MACf,OAAO,2BAA2B;IACpC,KAAK,SAAS;MACZ,OAAO,+BAA+B;IACxC,KAAK,cAAc;MACjB,OAAO,+BAA+B;IACxC,KAAK,QAAQ;MACX,OAAO,2BAA2B;IACpC;MACE,OAAO,2BAA2B;EACtC;AACF,CAAC;AAED,OAAO,MAAMC,eAAe,GAAID,IAAc,IAAa;EACzD,QAAQA,IAAI;IACV,KAAK,YAAY;MACf,OAAO,KAAK;IACd,KAAK,SAAS;MACZ,OAAO,IAAI;IACb,KAAK,cAAc;MACjB,OAAO,IAAI;IACb,KAAK,QAAQ;MACX,OAAO,IAAI;IACb;MACE,OAAO,GAAG;EACd;AACF,CAAC;AAED,OAAO,MAAME,YAAY,GAAGA,CAACC,IAAY,EAAEC,SAAiB,KAAa;EACvE,IAAID,IAAI,CAACE,MAAM,IAAID,SAAS,EAAE,OAAOD,IAAI;EACzC,OAAOA,IAAI,CAACG,SAAS,CAAC,CAAC,EAAEF,SAAS,CAAC,GAAG,KAAK;AAC7C,CAAC;AAED,OAAO,MAAMG,eAAe,GAAG,MAAOJ,IAAY,IAAuB;EACvE,IAAI;IACF,MAAMK,SAAS,CAACC,SAAS,CAACC,SAAS,CAACP,IAAI,CAAC;IACzC,OAAO,IAAI;EACb,CAAC,CAAC,OAAOQ,GAAG,EAAE;IACZC,OAAO,CAACC,KAAK,CAAC,uBAAuB,EAAEF,GAAG,CAAC;IAC3C,OAAO,KAAK;EACd;AACF,CAAC;AAED,OAAO,MAAMG,YAAY,GAAGA,CAACC,IAAS,EAAEC,QAAgB,KAAW;EACjE,MAAMC,UAAU,GAAGC,IAAI,CAACC,SAAS,CAACJ,IAAI,EAAE,IAAI,EAAE,CAAC,CAAC;EAChD,MAAMK,IAAI,GAAG,IAAIC,IAAI,CAAC,CAACJ,UAAU,CAAC,EAAE;IAAEjB,IAAI,EAAE;EAAmB,CAAC,CAAC;EACjE,MAAMsB,GAAG,GAAGC,GAAG,CAACC,eAAe,CAACJ,IAAI,CAAC;EAErC,MAAMK,IAAI,GAAGC,QAAQ,CAACC,aAAa,CAAC,GAAG,CAAC;EACxCF,IAAI,CAACG,IAAI,GAAGN,GAAG;EACfG,IAAI,CAACI,QAAQ,GAAGb,QAAQ;EACxBU,QAAQ,CAACI,IAAI,CAACC,WAAW,CAACN,IAAI,CAAC;EAC/BA,IAAI,CAACO,KAAK,CAAC,CAAC;EACZN,QAAQ,CAACI,IAAI,CAACG,WAAW,CAACR,IAAI,CAAC;EAE/BF,GAAG,CAACW,eAAe,CAACZ,GAAG,CAAC;AAC1B,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}