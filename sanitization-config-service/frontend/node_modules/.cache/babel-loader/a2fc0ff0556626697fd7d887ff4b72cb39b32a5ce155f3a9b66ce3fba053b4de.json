{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"M11.7 3H5a2 2 0 0 0-2 2v16l4-4h12a2 2 0 0 0 2-2v-2.7\",\n  key: \"uodpkb\"\n}], [\"circle\", {\n  cx: \"18\",\n  cy: \"6\",\n  r: \"3\",\n  key: \"1h7g24\"\n}]];\nconst MessageSquareDot = createLucideIcon(\"message-square-dot\", __iconNode);\nexport { __iconNode, MessageSquareDot as default };\n//# sourceMappingURL=message-square-dot.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}