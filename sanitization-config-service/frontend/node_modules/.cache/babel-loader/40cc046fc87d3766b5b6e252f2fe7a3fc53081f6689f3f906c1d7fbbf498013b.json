{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"M13.77 3.043a34 34 0 0 0-3.54 0\",\n  key: \"1oaobr\"\n}], [\"path\", {\n  d: \"M13.771 20.956a33 33 0 0 1-3.541.001\",\n  key: \"95iq0j\"\n}], [\"path\", {\n  d: \"M20.18 17.74c-.51 1.15-1.29 1.93-2.439 2.44\",\n  key: \"1u6qty\"\n}], [\"path\", {\n  d: \"M20.18 6.259c-.51-1.148-1.291-1.929-2.44-2.438\",\n  key: \"1ew6g6\"\n}], [\"path\", {\n  d: \"M20.957 10.23a33 33 0 0 1 0 3.54\",\n  key: \"1l9npr\"\n}], [\"path\", {\n  d: \"M3.043 10.23a34 34 0 0 0 .001 3.541\",\n  key: \"1it6jm\"\n}], [\"path\", {\n  d: \"M6.26 20.179c-1.15-.508-1.93-1.29-2.44-2.438\",\n  key: \"14uchd\"\n}], [\"path\", {\n  d: \"M6.26 3.82c-1.149.51-1.93 1.291-2.44 2.44\",\n  key: \"8k4agb\"\n}]];\nconst SquircleDashed = createLucideIcon(\"squircle-dashed\", __iconNode);\nexport { __iconNode, SquircleDashed as default };\n//# sourceMappingURL=squircle-dashed.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}