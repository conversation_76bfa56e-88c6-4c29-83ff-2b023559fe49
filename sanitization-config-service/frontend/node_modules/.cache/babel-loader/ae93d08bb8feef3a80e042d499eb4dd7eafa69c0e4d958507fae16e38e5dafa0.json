{"ast": null, "code": "var _jsxFileName = \"/Users/<USER>/dev/work/lab/javaagent/sanitization-config-service/frontend/src/App.tsx\";\nimport React from 'react';\nimport { Toaster } from 'react-hot-toast';\nimport SimpleRulesList from './components/SimpleRulesList';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nfunction App() {\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"min-h-screen bg-black\",\n    children: [/*#__PURE__*/_jsxDEV(SimpleRulesList, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 8,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Toaster, {\n      position: \"top-right\",\n      toastOptions: {\n        style: {\n          background: '#1f2937',\n          color: '#f3f4f6',\n          border: '1px solid #374151'\n        },\n        success: {\n          iconTheme: {\n            primary: '#10b981',\n            secondary: '#1f2937'\n          }\n        },\n        error: {\n          iconTheme: {\n            primary: '#ef4444',\n            secondary: '#1f2937'\n          }\n        }\n      }\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 9,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 7,\n    columnNumber: 5\n  }, this);\n}\n_c = App;\nexport default App;\nvar _c;\n$RefreshReg$(_c, \"App\");", "map": {"version": 3, "names": ["React", "Toaster", "SimpleRulesList", "jsxDEV", "_jsxDEV", "App", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "position", "toastOptions", "style", "background", "color", "border", "success", "iconTheme", "primary", "secondary", "error", "_c", "$RefreshReg$"], "sources": ["/Users/<USER>/dev/work/lab/javaagent/sanitization-config-service/frontend/src/App.tsx"], "sourcesContent": ["import React from 'react';\nimport { Toaster } from 'react-hot-toast';\nimport SimpleRulesList from './components/SimpleRulesList';\n\nfunction App() {\n  return (\n    <div className=\"min-h-screen bg-black\">\n      <SimpleRulesList />\n      <Toaster\n        position=\"top-right\"\n        toastOptions={{\n          style: {\n            background: '#1f2937',\n            color: '#f3f4f6',\n            border: '1px solid #374151',\n          },\n          success: {\n            iconTheme: {\n              primary: '#10b981',\n              secondary: '#1f2937',\n            },\n          },\n          error: {\n            iconTheme: {\n              primary: '#ef4444',\n              secondary: '#1f2937',\n            },\n          },\n        }}\n      />\n    </div>\n  );\n}\n\nexport default App;\n"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAASC,OAAO,QAAQ,iBAAiB;AACzC,OAAOC,eAAe,MAAM,8BAA8B;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE3D,SAASC,GAAGA,CAAA,EAAG;EACb,oBACED,OAAA;IAAKE,SAAS,EAAC,uBAAuB;IAAAC,QAAA,gBACpCH,OAAA,CAACF,eAAe;MAAAM,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,eACnBP,OAAA,CAACH,OAAO;MACNW,QAAQ,EAAC,WAAW;MACpBC,YAAY,EAAE;QACZC,KAAK,EAAE;UACLC,UAAU,EAAE,SAAS;UACrBC,KAAK,EAAE,SAAS;UAChBC,MAAM,EAAE;QACV,CAAC;QACDC,OAAO,EAAE;UACPC,SAAS,EAAE;YACTC,OAAO,EAAE,SAAS;YAClBC,SAAS,EAAE;UACb;QACF,CAAC;QACDC,KAAK,EAAE;UACLH,SAAS,EAAE;YACTC,OAAO,EAAE,SAAS;YAClBC,SAAS,EAAE;UACb;QACF;MACF;IAAE;MAAAb,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACC,CAAC;AAEV;AAACY,EAAA,GA5BQlB,GAAG;AA8BZ,eAAeA,GAAG;AAAC,IAAAkB,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}