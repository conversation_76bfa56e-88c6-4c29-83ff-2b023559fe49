{"ast": null, "code": "var _jsxFileName = \"/Users/<USER>/dev/work/lab/javaagent/sanitization-config-service/frontend/src/components/SimpleRulesList.tsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { Search, Settings, RefreshCw, AlertCircle, CheckCircle, XCircle } from 'lucide-react';\nimport { sanitizationApi } from '../services/api';\nimport toast from 'react-hot-toast';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst SimpleRulesList = () => {\n  _s();\n  const [config, setConfig] = useState(null);\n  const [loading, setLoading] = useState(true);\n  const [searchTerm, setSearchTerm] = useState('');\n  const [toggling, setToggling] = useState(false);\n  const fetchRules = async () => {\n    try {\n      setLoading(true);\n      const response = await sanitizationApi.getRules();\n      setConfig(response);\n    } catch (error) {\n      toast.error('获取规则失败');\n      console.error('Error fetching rules:', error);\n    } finally {\n      setLoading(false);\n    }\n  };\n  const handleReload = async () => {\n    try {\n      await sanitizationApi.reloadRules();\n      toast.success('规则重载成功');\n      await fetchRules();\n    } catch (error) {\n      toast.error('规则重载失败');\n      console.error('Error reloading rules:', error);\n    }\n  };\n  const handleToggleGlobal = async () => {\n    if (!config) return;\n    try {\n      setToggling(true);\n      const newEnabled = !config.enabled;\n      const response = await sanitizationApi.toggleGlobalSwitch(newEnabled);\n      setConfig(prev => prev ? {\n        ...prev,\n        enabled: response.enabled\n      } : null);\n      toast.success(newEnabled ? '全局脱敏已启用' : '全局脱敏已禁用');\n    } catch (error) {\n      toast.error('切换全局开关失败');\n      console.error('Error toggling global switch:', error);\n    } finally {\n      setToggling(false);\n    }\n  };\n  useEffect(() => {\n    fetchRules();\n  }, []);\n  const filteredRules = (config === null || config === void 0 ? void 0 : config.rules.filter(rule => rule.name.toLowerCase().includes(searchTerm.toLowerCase()) || rule.description.toLowerCase().includes(searchTerm.toLowerCase()) || rule.id.toLowerCase().includes(searchTerm.toLowerCase()))) || [];\n  const getSeverityColor = severity => {\n    switch (severity) {\n      case 'CRITICAL':\n        return 'bg-red-100 text-red-800';\n      case 'HIGH':\n        return 'bg-orange-100 text-orange-800';\n      case 'MEDIUM':\n        return 'bg-yellow-100 text-yellow-800';\n      case 'LOW':\n        return 'bg-green-100 text-green-800';\n      default:\n        return 'bg-gray-100 text-gray-800';\n    }\n  };\n  const getTypeColor = type => {\n    switch (type) {\n      case 'FIELD_NAME':\n        return 'bg-blue-100 text-blue-800';\n      case 'PATTERN':\n        return 'bg-purple-100 text-purple-800';\n      case 'CONTENT_TYPE':\n        return 'bg-indigo-100 text-indigo-800';\n      default:\n        return 'bg-gray-100 text-gray-800';\n    }\n  };\n  if (loading) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"min-h-screen bg-gray-50 flex items-center justify-center\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"text-center\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"animate-spin rounded-full h-8 w-8 border-b-2 border-indigo-600 mx-auto\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 88,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-gray-600 mt-4\",\n          children: \"\\u52A0\\u8F7D\\u4E2D...\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 89,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 87,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 86,\n      columnNumber: 7\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"min-h-screen bg-black text-white\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"sticky top-0 bg-black/90 backdrop-blur-sm border-b border-gray-800 z-10\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"max-w-6xl mx-auto px-6 py-4\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-center justify-between\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-center space-x-4\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"p-2 bg-blue-600 rounded-full\",\n              children: /*#__PURE__*/_jsxDEV(Settings, {\n                className: \"h-6 w-6 text-white\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 103,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 102,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n                className: \"text-xl font-bold text-white\",\n                children: \"\\u6570\\u636E\\u8131\\u654F\\u89C4\\u5219\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 106,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex items-center space-x-4 mt-1\",\n                children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"text-gray-400 text-sm\",\n                  children: [\"\\u5171 \", filteredRules.length, \" \\u6761\\u89C4\\u5219\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 108,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: `inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${config !== null && config !== void 0 && config.enabled ? 'bg-green-900/50 text-green-400 border border-green-800' : 'bg-red-900/50 text-red-400 border border-red-800'}`,\n                  children: config !== null && config !== void 0 && config.enabled ? '全局已启用' : '全局已禁用'\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 109,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 107,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 105,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 101,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-center space-x-3\",\n            children: [/*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: handleReload,\n              className: \"inline-flex items-center px-4 py-2 rounded-full text-sm font-medium bg-gray-800 hover:bg-gray-700 text-white border border-gray-700 transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 focus:ring-offset-black\",\n              children: [/*#__PURE__*/_jsxDEV(RefreshCw, {\n                className: \"h-4 w-4 mr-2\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 124,\n                columnNumber: 17\n              }, this), \"\\u91CD\\u8F7D\\u89C4\\u5219\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 120,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: handleToggleGlobal,\n              disabled: toggling,\n              className: `inline-flex items-center px-4 py-2 rounded-full text-sm font-medium transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-offset-black ${config !== null && config !== void 0 && config.enabled ? 'bg-red-600 hover:bg-red-700 text-white focus:ring-red-500' : 'bg-green-600 hover:bg-green-700 text-white focus:ring-green-500'} ${toggling ? 'opacity-50 cursor-not-allowed' : ''}`,\n              children: toggling ? '处理中...' : config !== null && config !== void 0 && config.enabled ? '禁用全局脱敏' : '启用全局脱敏'\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 127,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 119,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 100,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 99,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 98,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"max-w-6xl mx-auto px-6 py-6\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"relative\",\n        children: [/*#__PURE__*/_jsxDEV(Search, {\n          className: \"absolute left-4 top-1/2 transform -translate-y-1/2 h-5 w-5 text-gray-500\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 146,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n          type: \"text\",\n          value: searchTerm,\n          onChange: e => setSearchTerm(e.target.value),\n          placeholder: \"\\u641C\\u7D22\\u89C4\\u5219\\u540D\\u79F0\\u3001\\u63CF\\u8FF0\\u6216ID...\",\n          className: \"w-full pl-12 pr-4 py-3 bg-gray-900 border border-gray-700 rounded-full text-white placeholder-gray-500 focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 147,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 145,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 144,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\",\n      children: filteredRules.length === 0 ? /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"text-center py-12\",\n        children: [/*#__PURE__*/_jsxDEV(AlertCircle, {\n          className: \"mx-auto h-12 w-12 text-gray-400\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 161,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n          className: \"mt-2 text-sm font-medium text-gray-900\",\n          children: \"\\u6CA1\\u6709\\u627E\\u5230\\u5339\\u914D\\u7684\\u89C4\\u5219\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 162,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"mt-1 text-sm text-gray-500\",\n          children: \"\\u8BF7\\u5C1D\\u8BD5\\u8C03\\u6574\\u641C\\u7D22\\u6761\\u4EF6\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 163,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 160,\n        columnNumber: 11\n      }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"bg-white shadow overflow-hidden sm:rounded-md\",\n        children: /*#__PURE__*/_jsxDEV(\"ul\", {\n          className: \"divide-y divide-gray-200\",\n          children: filteredRules.map(rule => /*#__PURE__*/_jsxDEV(\"li\", {\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"px-4 py-4 sm:px-6\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex items-center justify-between\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex items-center\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"flex-shrink-0\",\n                    children: /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: `inline-flex items-center justify-center h-8 w-8 rounded-full ${rule.enabled ? 'bg-green-100' : 'bg-gray-100'}`,\n                      children: rule.enabled ? /*#__PURE__*/_jsxDEV(CheckCircle, {\n                        className: \"h-5 w-5 text-green-600\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 178,\n                        columnNumber: 31\n                      }, this) : /*#__PURE__*/_jsxDEV(XCircle, {\n                        className: \"h-5 w-5 text-gray-400\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 180,\n                        columnNumber: 31\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 174,\n                      columnNumber: 27\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 173,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"ml-4\",\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"flex items-center\",\n                      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"text-sm font-medium text-gray-900\",\n                        children: rule.name\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 186,\n                        columnNumber: 29\n                      }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                        className: `ml-2 inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getSeverityColor(rule.severity)}`,\n                        children: rule.severity\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 187,\n                        columnNumber: 29\n                      }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                        className: `ml-2 inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getTypeColor(rule.type)}`,\n                        children: rule.type\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 190,\n                        columnNumber: 29\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 185,\n                      columnNumber: 27\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"mt-1 text-sm text-gray-500\",\n                      children: rule.description\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 194,\n                      columnNumber: 27\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"mt-2 text-xs text-gray-400\",\n                      children: [\"ID: \", rule.id]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 195,\n                      columnNumber: 27\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 184,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 172,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex items-center space-x-4\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"text-sm text-gray-500\",\n                    children: [\"\\u4F18\\u5148\\u7EA7: \", rule.priority]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 199,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: `inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${rule.enabled ? 'bg-green-100 text-green-800' : 'bg-gray-100 text-gray-800'}`,\n                    children: rule.enabled ? '已启用' : '已禁用'\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 202,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 198,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 171,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"mt-4\",\n                children: /*#__PURE__*/_jsxDEV(\"dl\", {\n                  className: \"grid grid-cols-1 gap-x-4 gap-y-2 sm:grid-cols-2\",\n                  children: [rule.fieldNames && rule.fieldNames.length > 0 && /*#__PURE__*/_jsxDEV(\"div\", {\n                    children: [/*#__PURE__*/_jsxDEV(\"dt\", {\n                      className: \"text-sm font-medium text-gray-500\",\n                      children: \"\\u5B57\\u6BB5\\u540D\\u79F0\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 214,\n                      columnNumber: 29\n                    }, this), /*#__PURE__*/_jsxDEV(\"dd\", {\n                      className: \"mt-1 text-sm text-gray-900\",\n                      children: rule.fieldNames.join(', ')\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 215,\n                      columnNumber: 29\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 213,\n                    columnNumber: 27\n                  }, this), rule.pattern && /*#__PURE__*/_jsxDEV(\"div\", {\n                    children: [/*#__PURE__*/_jsxDEV(\"dt\", {\n                      className: \"text-sm font-medium text-gray-500\",\n                      children: \"\\u5339\\u914D\\u6A21\\u5F0F\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 220,\n                      columnNumber: 29\n                    }, this), /*#__PURE__*/_jsxDEV(\"dd\", {\n                      className: \"mt-1 text-sm text-gray-900\",\n                      children: /*#__PURE__*/_jsxDEV(\"code\", {\n                        className: \"bg-gray-100 px-2 py-1 rounded text-xs\",\n                        children: rule.pattern\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 222,\n                        columnNumber: 31\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 221,\n                      columnNumber: 29\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 219,\n                    columnNumber: 27\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    children: [/*#__PURE__*/_jsxDEV(\"dt\", {\n                      className: \"text-sm font-medium text-gray-500\",\n                      children: \"\\u63A9\\u7801\\u503C\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 227,\n                      columnNumber: 27\n                    }, this), /*#__PURE__*/_jsxDEV(\"dd\", {\n                      className: \"mt-1 text-sm text-gray-900\",\n                      children: /*#__PURE__*/_jsxDEV(\"code\", {\n                        className: \"bg-gray-100 px-2 py-1 rounded text-xs\",\n                        children: rule.maskValue\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 229,\n                        columnNumber: 29\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 228,\n                      columnNumber: 27\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 226,\n                    columnNumber: 25\n                  }, this), rule.includeServices && rule.includeServices.length > 0 && /*#__PURE__*/_jsxDEV(\"div\", {\n                    children: [/*#__PURE__*/_jsxDEV(\"dt\", {\n                      className: \"text-sm font-medium text-gray-500\",\n                      children: \"\\u9002\\u7528\\u670D\\u52A1\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 234,\n                      columnNumber: 29\n                    }, this), /*#__PURE__*/_jsxDEV(\"dd\", {\n                      className: \"mt-1 text-sm text-gray-900\",\n                      children: rule.includeServices.join(', ')\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 235,\n                      columnNumber: 29\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 233,\n                    columnNumber: 27\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 211,\n                  columnNumber: 23\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 210,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 170,\n              columnNumber: 19\n            }, this)\n          }, rule.id, false, {\n            fileName: _jsxFileName,\n            lineNumber: 169,\n            columnNumber: 17\n          }, this))\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 167,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 166,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 158,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 96,\n    columnNumber: 5\n  }, this);\n};\n_s(SimpleRulesList, \"uKt2Xt/PrPprlIBDO5rAiYCX0KA=\");\n_c = SimpleRulesList;\nexport default SimpleRulesList;\nvar _c;\n$RefreshReg$(_c, \"SimpleRulesList\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "Search", "Settings", "RefreshCw", "AlertCircle", "CheckCircle", "XCircle", "sanitizationApi", "toast", "jsxDEV", "_jsxDEV", "SimpleRulesList", "_s", "config", "setConfig", "loading", "setLoading", "searchTerm", "setSearchTerm", "toggling", "setToggling", "fetchRules", "response", "getRules", "error", "console", "handleReload", "reloadRules", "success", "handleToggleGlobal", "newEnabled", "enabled", "toggleGlobalSwitch", "prev", "filteredRules", "rules", "filter", "rule", "name", "toLowerCase", "includes", "description", "id", "getSeverityColor", "severity", "getTypeColor", "type", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "length", "onClick", "disabled", "value", "onChange", "e", "target", "placeholder", "map", "priority", "fieldNames", "join", "pattern", "maskValue", "includeServices", "_c", "$RefreshReg$"], "sources": ["/Users/<USER>/dev/work/lab/javaagent/sanitization-config-service/frontend/src/components/SimpleRulesList.tsx"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { Search, Settings, RefreshCw, AlertCircle, CheckCircle, XCircle } from 'lucide-react';\nimport { sanitizationApi } from '../services/api';\nimport { SanitizationConfig } from '../types';\nimport toast from 'react-hot-toast';\n\nconst SimpleRulesList: React.FC = () => {\n  const [config, setConfig] = useState<SanitizationConfig | null>(null);\n  const [loading, setLoading] = useState(true);\n  const [searchTerm, setSearchTerm] = useState('');\n  const [toggling, setToggling] = useState(false);\n\n  const fetchRules = async () => {\n    try {\n      setLoading(true);\n      const response = await sanitizationApi.getRules();\n      setConfig(response);\n    } catch (error) {\n      toast.error('获取规则失败');\n      console.error('Error fetching rules:', error);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const handleReload = async () => {\n    try {\n      await sanitizationApi.reloadRules();\n      toast.success('规则重载成功');\n      await fetchRules();\n    } catch (error) {\n      toast.error('规则重载失败');\n      console.error('Error reloading rules:', error);\n    }\n  };\n\n  const handleToggleGlobal = async () => {\n    if (!config) return;\n\n    try {\n      setToggling(true);\n      const newEnabled = !config.enabled;\n      const response = await sanitizationApi.toggleGlobalSwitch(newEnabled);\n\n      setConfig(prev => prev ? { ...prev, enabled: response.enabled } : null);\n      toast.success(newEnabled ? '全局脱敏已启用' : '全局脱敏已禁用');\n    } catch (error) {\n      toast.error('切换全局开关失败');\n      console.error('Error toggling global switch:', error);\n    } finally {\n      setToggling(false);\n    }\n  };\n\n  useEffect(() => {\n    fetchRules();\n  }, []);\n\n  const filteredRules = config?.rules.filter(rule =>\n    rule.name.toLowerCase().includes(searchTerm.toLowerCase()) ||\n    rule.description.toLowerCase().includes(searchTerm.toLowerCase()) ||\n    rule.id.toLowerCase().includes(searchTerm.toLowerCase())\n  ) || [];\n\n  const getSeverityColor = (severity: string) => {\n    switch (severity) {\n      case 'CRITICAL': return 'bg-red-100 text-red-800';\n      case 'HIGH': return 'bg-orange-100 text-orange-800';\n      case 'MEDIUM': return 'bg-yellow-100 text-yellow-800';\n      case 'LOW': return 'bg-green-100 text-green-800';\n      default: return 'bg-gray-100 text-gray-800';\n    }\n  };\n\n  const getTypeColor = (type: string) => {\n    switch (type) {\n      case 'FIELD_NAME': return 'bg-blue-100 text-blue-800';\n      case 'PATTERN': return 'bg-purple-100 text-purple-800';\n      case 'CONTENT_TYPE': return 'bg-indigo-100 text-indigo-800';\n      default: return 'bg-gray-100 text-gray-800';\n    }\n  };\n\n  if (loading) {\n    return (\n      <div className=\"min-h-screen bg-gray-50 flex items-center justify-center\">\n        <div className=\"text-center\">\n          <div className=\"animate-spin rounded-full h-8 w-8 border-b-2 border-indigo-600 mx-auto\"></div>\n          <p className=\"text-gray-600 mt-4\">加载中...</p>\n        </div>\n      </div>\n    );\n  }\n\n  return (\n    <div className=\"min-h-screen bg-black text-white\">\n      {/* Header */}\n      <div className=\"sticky top-0 bg-black/90 backdrop-blur-sm border-b border-gray-800 z-10\">\n        <div className=\"max-w-6xl mx-auto px-6 py-4\">\n          <div className=\"flex items-center justify-between\">\n            <div className=\"flex items-center space-x-4\">\n              <div className=\"p-2 bg-blue-600 rounded-full\">\n                <Settings className=\"h-6 w-6 text-white\" />\n              </div>\n              <div>\n                <h1 className=\"text-xl font-bold text-white\">数据脱敏规则</h1>\n                <div className=\"flex items-center space-x-4 mt-1\">\n                  <span className=\"text-gray-400 text-sm\">共 {filteredRules.length} 条规则</span>\n                  <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${\n                    config?.enabled\n                      ? 'bg-green-900/50 text-green-400 border border-green-800'\n                      : 'bg-red-900/50 text-red-400 border border-red-800'\n                  }`}>\n                    {config?.enabled ? '全局已启用' : '全局已禁用'}\n                  </span>\n                </div>\n              </div>\n            </div>\n            <div className=\"flex items-center space-x-3\">\n              <button\n                onClick={handleReload}\n                className=\"inline-flex items-center px-4 py-2 rounded-full text-sm font-medium bg-gray-800 hover:bg-gray-700 text-white border border-gray-700 transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 focus:ring-offset-black\"\n              >\n                <RefreshCw className=\"h-4 w-4 mr-2\" />\n                重载规则\n              </button>\n              <button\n                onClick={handleToggleGlobal}\n                disabled={toggling}\n                className={`inline-flex items-center px-4 py-2 rounded-full text-sm font-medium transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-offset-black ${\n                  config?.enabled\n                    ? 'bg-red-600 hover:bg-red-700 text-white focus:ring-red-500'\n                    : 'bg-green-600 hover:bg-green-700 text-white focus:ring-green-500'\n                } ${toggling ? 'opacity-50 cursor-not-allowed' : ''}`}\n              >\n                {toggling ? '处理中...' : (config?.enabled ? '禁用全局脱敏' : '启用全局脱敏')}\n              </button>\n            </div>\n          </div>\n        </div>\n      </div>\n\n      {/* Search */}\n      <div className=\"max-w-6xl mx-auto px-6 py-6\">\n        <div className=\"relative\">\n          <Search className=\"absolute left-4 top-1/2 transform -translate-y-1/2 h-5 w-5 text-gray-500\" />\n          <input\n            type=\"text\"\n            value={searchTerm}\n            onChange={(e) => setSearchTerm(e.target.value)}\n            placeholder=\"搜索规则名称、描述或ID...\"\n            className=\"w-full pl-12 pr-4 py-3 bg-gray-900 border border-gray-700 rounded-full text-white placeholder-gray-500 focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200\"\n          />\n        </div>\n      </div>\n\n      {/* Rules List */}\n      <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n        {filteredRules.length === 0 ? (\n          <div className=\"text-center py-12\">\n            <AlertCircle className=\"mx-auto h-12 w-12 text-gray-400\" />\n            <h3 className=\"mt-2 text-sm font-medium text-gray-900\">没有找到匹配的规则</h3>\n            <p className=\"mt-1 text-sm text-gray-500\">请尝试调整搜索条件</p>\n          </div>\n        ) : (\n          <div className=\"bg-white shadow overflow-hidden sm:rounded-md\">\n            <ul className=\"divide-y divide-gray-200\">\n              {filteredRules.map((rule) => (\n                <li key={rule.id}>\n                  <div className=\"px-4 py-4 sm:px-6\">\n                    <div className=\"flex items-center justify-between\">\n                      <div className=\"flex items-center\">\n                        <div className=\"flex-shrink-0\">\n                          <div className={`inline-flex items-center justify-center h-8 w-8 rounded-full ${\n                            rule.enabled ? 'bg-green-100' : 'bg-gray-100'\n                          }`}>\n                            {rule.enabled ? (\n                              <CheckCircle className=\"h-5 w-5 text-green-600\" />\n                            ) : (\n                              <XCircle className=\"h-5 w-5 text-gray-400\" />\n                            )}\n                          </div>\n                        </div>\n                        <div className=\"ml-4\">\n                          <div className=\"flex items-center\">\n                            <div className=\"text-sm font-medium text-gray-900\">{rule.name}</div>\n                            <span className={`ml-2 inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getSeverityColor(rule.severity)}`}>\n                              {rule.severity}\n                            </span>\n                            <span className={`ml-2 inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getTypeColor(rule.type)}`}>\n                              {rule.type}\n                            </span>\n                          </div>\n                          <div className=\"mt-1 text-sm text-gray-500\">{rule.description}</div>\n                          <div className=\"mt-2 text-xs text-gray-400\">ID: {rule.id}</div>\n                        </div>\n                      </div>\n                      <div className=\"flex items-center space-x-4\">\n                        <div className=\"text-sm text-gray-500\">\n                          优先级: {rule.priority}\n                        </div>\n                        <div className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${\n                          rule.enabled ? 'bg-green-100 text-green-800' : 'bg-gray-100 text-gray-800'\n                        }`}>\n                          {rule.enabled ? '已启用' : '已禁用'}\n                        </div>\n                      </div>\n                    </div>\n\n                    <div className=\"mt-4\">\n                      <dl className=\"grid grid-cols-1 gap-x-4 gap-y-2 sm:grid-cols-2\">\n                        {rule.fieldNames && rule.fieldNames.length > 0 && (\n                          <div>\n                            <dt className=\"text-sm font-medium text-gray-500\">字段名称</dt>\n                            <dd className=\"mt-1 text-sm text-gray-900\">{rule.fieldNames.join(', ')}</dd>\n                          </div>\n                        )}\n                        {rule.pattern && (\n                          <div>\n                            <dt className=\"text-sm font-medium text-gray-500\">匹配模式</dt>\n                            <dd className=\"mt-1 text-sm text-gray-900\">\n                              <code className=\"bg-gray-100 px-2 py-1 rounded text-xs\">{rule.pattern}</code>\n                            </dd>\n                          </div>\n                        )}\n                        <div>\n                          <dt className=\"text-sm font-medium text-gray-500\">掩码值</dt>\n                          <dd className=\"mt-1 text-sm text-gray-900\">\n                            <code className=\"bg-gray-100 px-2 py-1 rounded text-xs\">{rule.maskValue}</code>\n                          </dd>\n                        </div>\n                        {rule.includeServices && rule.includeServices.length > 0 && (\n                          <div>\n                            <dt className=\"text-sm font-medium text-gray-500\">适用服务</dt>\n                            <dd className=\"mt-1 text-sm text-gray-900\">{rule.includeServices.join(', ')}</dd>\n                          </div>\n                        )}\n                      </dl>\n                    </div>\n                  </div>\n                </li>\n              ))}\n            </ul>\n          </div>\n        )}\n      </div>\n    </div>\n  );\n};\n\nexport default SimpleRulesList;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,MAAM,EAAEC,QAAQ,EAAEC,SAAS,EAAEC,WAAW,EAAEC,WAAW,EAAEC,OAAO,QAAQ,cAAc;AAC7F,SAASC,eAAe,QAAQ,iBAAiB;AAEjD,OAAOC,KAAK,MAAM,iBAAiB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEpC,MAAMC,eAAyB,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACtC,MAAM,CAACC,MAAM,EAAEC,SAAS,CAAC,GAAGf,QAAQ,CAA4B,IAAI,CAAC;EACrE,MAAM,CAACgB,OAAO,EAAEC,UAAU,CAAC,GAAGjB,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAM,CAACkB,UAAU,EAAEC,aAAa,CAAC,GAAGnB,QAAQ,CAAC,EAAE,CAAC;EAChD,MAAM,CAACoB,QAAQ,EAAEC,WAAW,CAAC,GAAGrB,QAAQ,CAAC,KAAK,CAAC;EAE/C,MAAMsB,UAAU,GAAG,MAAAA,CAAA,KAAY;IAC7B,IAAI;MACFL,UAAU,CAAC,IAAI,CAAC;MAChB,MAAMM,QAAQ,GAAG,MAAMf,eAAe,CAACgB,QAAQ,CAAC,CAAC;MACjDT,SAAS,CAACQ,QAAQ,CAAC;IACrB,CAAC,CAAC,OAAOE,KAAK,EAAE;MACdhB,KAAK,CAACgB,KAAK,CAAC,QAAQ,CAAC;MACrBC,OAAO,CAACD,KAAK,CAAC,uBAAuB,EAAEA,KAAK,CAAC;IAC/C,CAAC,SAAS;MACRR,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAED,MAAMU,YAAY,GAAG,MAAAA,CAAA,KAAY;IAC/B,IAAI;MACF,MAAMnB,eAAe,CAACoB,WAAW,CAAC,CAAC;MACnCnB,KAAK,CAACoB,OAAO,CAAC,QAAQ,CAAC;MACvB,MAAMP,UAAU,CAAC,CAAC;IACpB,CAAC,CAAC,OAAOG,KAAK,EAAE;MACdhB,KAAK,CAACgB,KAAK,CAAC,QAAQ,CAAC;MACrBC,OAAO,CAACD,KAAK,CAAC,wBAAwB,EAAEA,KAAK,CAAC;IAChD;EACF,CAAC;EAED,MAAMK,kBAAkB,GAAG,MAAAA,CAAA,KAAY;IACrC,IAAI,CAAChB,MAAM,EAAE;IAEb,IAAI;MACFO,WAAW,CAAC,IAAI,CAAC;MACjB,MAAMU,UAAU,GAAG,CAACjB,MAAM,CAACkB,OAAO;MAClC,MAAMT,QAAQ,GAAG,MAAMf,eAAe,CAACyB,kBAAkB,CAACF,UAAU,CAAC;MAErEhB,SAAS,CAACmB,IAAI,IAAIA,IAAI,GAAG;QAAE,GAAGA,IAAI;QAAEF,OAAO,EAAET,QAAQ,CAACS;MAAQ,CAAC,GAAG,IAAI,CAAC;MACvEvB,KAAK,CAACoB,OAAO,CAACE,UAAU,GAAG,SAAS,GAAG,SAAS,CAAC;IACnD,CAAC,CAAC,OAAON,KAAK,EAAE;MACdhB,KAAK,CAACgB,KAAK,CAAC,UAAU,CAAC;MACvBC,OAAO,CAACD,KAAK,CAAC,+BAA+B,EAAEA,KAAK,CAAC;IACvD,CAAC,SAAS;MACRJ,WAAW,CAAC,KAAK,CAAC;IACpB;EACF,CAAC;EAEDpB,SAAS,CAAC,MAAM;IACdqB,UAAU,CAAC,CAAC;EACd,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMa,aAAa,GAAG,CAAArB,MAAM,aAANA,MAAM,uBAANA,MAAM,CAAEsB,KAAK,CAACC,MAAM,CAACC,IAAI,IAC7CA,IAAI,CAACC,IAAI,CAACC,WAAW,CAAC,CAAC,CAACC,QAAQ,CAACvB,UAAU,CAACsB,WAAW,CAAC,CAAC,CAAC,IAC1DF,IAAI,CAACI,WAAW,CAACF,WAAW,CAAC,CAAC,CAACC,QAAQ,CAACvB,UAAU,CAACsB,WAAW,CAAC,CAAC,CAAC,IACjEF,IAAI,CAACK,EAAE,CAACH,WAAW,CAAC,CAAC,CAACC,QAAQ,CAACvB,UAAU,CAACsB,WAAW,CAAC,CAAC,CACzD,CAAC,KAAI,EAAE;EAEP,MAAMI,gBAAgB,GAAIC,QAAgB,IAAK;IAC7C,QAAQA,QAAQ;MACd,KAAK,UAAU;QAAE,OAAO,yBAAyB;MACjD,KAAK,MAAM;QAAE,OAAO,+BAA+B;MACnD,KAAK,QAAQ;QAAE,OAAO,+BAA+B;MACrD,KAAK,KAAK;QAAE,OAAO,6BAA6B;MAChD;QAAS,OAAO,2BAA2B;IAC7C;EACF,CAAC;EAED,MAAMC,YAAY,GAAIC,IAAY,IAAK;IACrC,QAAQA,IAAI;MACV,KAAK,YAAY;QAAE,OAAO,2BAA2B;MACrD,KAAK,SAAS;QAAE,OAAO,+BAA+B;MACtD,KAAK,cAAc;QAAE,OAAO,+BAA+B;MAC3D;QAAS,OAAO,2BAA2B;IAC7C;EACF,CAAC;EAED,IAAI/B,OAAO,EAAE;IACX,oBACEL,OAAA;MAAKqC,SAAS,EAAC,0DAA0D;MAAAC,QAAA,eACvEtC,OAAA;QAAKqC,SAAS,EAAC,aAAa;QAAAC,QAAA,gBAC1BtC,OAAA;UAAKqC,SAAS,EAAC;QAAwE;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eAC9F1C,OAAA;UAAGqC,SAAS,EAAC,oBAAoB;UAAAC,QAAA,EAAC;QAAM;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACzC;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAEV;EAEA,oBACE1C,OAAA;IAAKqC,SAAS,EAAC,kCAAkC;IAAAC,QAAA,gBAE/CtC,OAAA;MAAKqC,SAAS,EAAC,yEAAyE;MAAAC,QAAA,eACtFtC,OAAA;QAAKqC,SAAS,EAAC,6BAA6B;QAAAC,QAAA,eAC1CtC,OAAA;UAAKqC,SAAS,EAAC,mCAAmC;UAAAC,QAAA,gBAChDtC,OAAA;YAAKqC,SAAS,EAAC,6BAA6B;YAAAC,QAAA,gBAC1CtC,OAAA;cAAKqC,SAAS,EAAC,8BAA8B;cAAAC,QAAA,eAC3CtC,OAAA,CAACR,QAAQ;gBAAC6C,SAAS,EAAC;cAAoB;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACxC,CAAC,eACN1C,OAAA;cAAAsC,QAAA,gBACEtC,OAAA;gBAAIqC,SAAS,EAAC,8BAA8B;gBAAAC,QAAA,EAAC;cAAM;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACxD1C,OAAA;gBAAKqC,SAAS,EAAC,kCAAkC;gBAAAC,QAAA,gBAC/CtC,OAAA;kBAAMqC,SAAS,EAAC,uBAAuB;kBAAAC,QAAA,GAAC,SAAE,EAACd,aAAa,CAACmB,MAAM,EAAC,qBAAI;gBAAA;kBAAAJ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,eAC3E1C,OAAA;kBAAMqC,SAAS,EAAE,uEACflC,MAAM,aAANA,MAAM,eAANA,MAAM,CAAEkB,OAAO,GACX,wDAAwD,GACxD,kDAAkD,EACrD;kBAAAiB,QAAA,EACAnC,MAAM,aAANA,MAAM,eAANA,MAAM,CAAEkB,OAAO,GAAG,OAAO,GAAG;gBAAO;kBAAAkB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAChC,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACJ,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eACN1C,OAAA;YAAKqC,SAAS,EAAC,6BAA6B;YAAAC,QAAA,gBAC1CtC,OAAA;cACE4C,OAAO,EAAE5B,YAAa;cACtBqB,SAAS,EAAC,oQAAoQ;cAAAC,QAAA,gBAE9QtC,OAAA,CAACP,SAAS;gBAAC4C,SAAS,EAAC;cAAc;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,4BAExC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eACT1C,OAAA;cACE4C,OAAO,EAAEzB,kBAAmB;cAC5B0B,QAAQ,EAAEpC,QAAS;cACnB4B,SAAS,EAAE,kLACTlC,MAAM,aAANA,MAAM,eAANA,MAAM,CAAEkB,OAAO,GACX,2DAA2D,GAC3D,iEAAiE,IACnEZ,QAAQ,GAAG,+BAA+B,GAAG,EAAE,EAAG;cAAA6B,QAAA,EAErD7B,QAAQ,GAAG,QAAQ,GAAIN,MAAM,aAANA,MAAM,eAANA,MAAM,CAAEkB,OAAO,GAAG,QAAQ,GAAG;YAAS;cAAAkB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACxD,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGN1C,OAAA;MAAKqC,SAAS,EAAC,6BAA6B;MAAAC,QAAA,eAC1CtC,OAAA;QAAKqC,SAAS,EAAC,UAAU;QAAAC,QAAA,gBACvBtC,OAAA,CAACT,MAAM;UAAC8C,SAAS,EAAC;QAA0E;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eAC/F1C,OAAA;UACEoC,IAAI,EAAC,MAAM;UACXU,KAAK,EAAEvC,UAAW;UAClBwC,QAAQ,EAAGC,CAAC,IAAKxC,aAAa,CAACwC,CAAC,CAACC,MAAM,CAACH,KAAK,CAAE;UAC/CI,WAAW,EAAC,mEAAiB;UAC7Bb,SAAS,EAAC;QAA8L;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACzM,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGN1C,OAAA;MAAKqC,SAAS,EAAC,wCAAwC;MAAAC,QAAA,EACpDd,aAAa,CAACmB,MAAM,KAAK,CAAC,gBACzB3C,OAAA;QAAKqC,SAAS,EAAC,mBAAmB;QAAAC,QAAA,gBAChCtC,OAAA,CAACN,WAAW;UAAC2C,SAAS,EAAC;QAAiC;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eAC3D1C,OAAA;UAAIqC,SAAS,EAAC,wCAAwC;UAAAC,QAAA,EAAC;QAAS;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACrE1C,OAAA;UAAGqC,SAAS,EAAC,4BAA4B;UAAAC,QAAA,EAAC;QAAS;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACpD,CAAC,gBAEN1C,OAAA;QAAKqC,SAAS,EAAC,+CAA+C;QAAAC,QAAA,eAC5DtC,OAAA;UAAIqC,SAAS,EAAC,0BAA0B;UAAAC,QAAA,EACrCd,aAAa,CAAC2B,GAAG,CAAExB,IAAI,iBACtB3B,OAAA;YAAAsC,QAAA,eACEtC,OAAA;cAAKqC,SAAS,EAAC,mBAAmB;cAAAC,QAAA,gBAChCtC,OAAA;gBAAKqC,SAAS,EAAC,mCAAmC;gBAAAC,QAAA,gBAChDtC,OAAA;kBAAKqC,SAAS,EAAC,mBAAmB;kBAAAC,QAAA,gBAChCtC,OAAA;oBAAKqC,SAAS,EAAC,eAAe;oBAAAC,QAAA,eAC5BtC,OAAA;sBAAKqC,SAAS,EAAE,gEACdV,IAAI,CAACN,OAAO,GAAG,cAAc,GAAG,aAAa,EAC5C;sBAAAiB,QAAA,EACAX,IAAI,CAACN,OAAO,gBACXrB,OAAA,CAACL,WAAW;wBAAC0C,SAAS,EAAC;sBAAwB;wBAAAE,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE,CAAC,gBAElD1C,OAAA,CAACJ,OAAO;wBAACyC,SAAS,EAAC;sBAAuB;wBAAAE,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE;oBAC7C;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACE;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC,eACN1C,OAAA;oBAAKqC,SAAS,EAAC,MAAM;oBAAAC,QAAA,gBACnBtC,OAAA;sBAAKqC,SAAS,EAAC,mBAAmB;sBAAAC,QAAA,gBAChCtC,OAAA;wBAAKqC,SAAS,EAAC,mCAAmC;wBAAAC,QAAA,EAAEX,IAAI,CAACC;sBAAI;wBAAAW,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAM,CAAC,eACpE1C,OAAA;wBAAMqC,SAAS,EAAE,gFAAgFJ,gBAAgB,CAACN,IAAI,CAACO,QAAQ,CAAC,EAAG;wBAAAI,QAAA,EAChIX,IAAI,CAACO;sBAAQ;wBAAAK,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACV,CAAC,eACP1C,OAAA;wBAAMqC,SAAS,EAAE,gFAAgFF,YAAY,CAACR,IAAI,CAACS,IAAI,CAAC,EAAG;wBAAAE,QAAA,EACxHX,IAAI,CAACS;sBAAI;wBAAAG,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACN,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACJ,CAAC,eACN1C,OAAA;sBAAKqC,SAAS,EAAC,4BAA4B;sBAAAC,QAAA,EAAEX,IAAI,CAACI;oBAAW;sBAAAQ,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC,eACpE1C,OAAA;sBAAKqC,SAAS,EAAC,4BAA4B;sBAAAC,QAAA,GAAC,MAAI,EAACX,IAAI,CAACK,EAAE;oBAAA;sBAAAO,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC5D,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC,eACN1C,OAAA;kBAAKqC,SAAS,EAAC,6BAA6B;kBAAAC,QAAA,gBAC1CtC,OAAA;oBAAKqC,SAAS,EAAC,uBAAuB;oBAAAC,QAAA,GAAC,sBAChC,EAACX,IAAI,CAACyB,QAAQ;kBAAA;oBAAAb,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAChB,CAAC,eACN1C,OAAA;oBAAKqC,SAAS,EAAE,2EACdV,IAAI,CAACN,OAAO,GAAG,6BAA6B,GAAG,2BAA2B,EACzE;oBAAAiB,QAAA,EACAX,IAAI,CAACN,OAAO,GAAG,KAAK,GAAG;kBAAK;oBAAAkB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC1B,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eAEN1C,OAAA;gBAAKqC,SAAS,EAAC,MAAM;gBAAAC,QAAA,eACnBtC,OAAA;kBAAIqC,SAAS,EAAC,iDAAiD;kBAAAC,QAAA,GAC5DX,IAAI,CAAC0B,UAAU,IAAI1B,IAAI,CAAC0B,UAAU,CAACV,MAAM,GAAG,CAAC,iBAC5C3C,OAAA;oBAAAsC,QAAA,gBACEtC,OAAA;sBAAIqC,SAAS,EAAC,mCAAmC;sBAAAC,QAAA,EAAC;oBAAI;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAI,CAAC,eAC3D1C,OAAA;sBAAIqC,SAAS,EAAC,4BAA4B;sBAAAC,QAAA,EAAEX,IAAI,CAAC0B,UAAU,CAACC,IAAI,CAAC,IAAI;oBAAC;sBAAAf,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAK,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACzE,CACN,EACAf,IAAI,CAAC4B,OAAO,iBACXvD,OAAA;oBAAAsC,QAAA,gBACEtC,OAAA;sBAAIqC,SAAS,EAAC,mCAAmC;sBAAAC,QAAA,EAAC;oBAAI;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAI,CAAC,eAC3D1C,OAAA;sBAAIqC,SAAS,EAAC,4BAA4B;sBAAAC,QAAA,eACxCtC,OAAA;wBAAMqC,SAAS,EAAC,uCAAuC;wBAAAC,QAAA,EAAEX,IAAI,CAAC4B;sBAAO;wBAAAhB,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAO;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAC3E,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACF,CACN,eACD1C,OAAA;oBAAAsC,QAAA,gBACEtC,OAAA;sBAAIqC,SAAS,EAAC,mCAAmC;sBAAAC,QAAA,EAAC;oBAAG;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAI,CAAC,eAC1D1C,OAAA;sBAAIqC,SAAS,EAAC,4BAA4B;sBAAAC,QAAA,eACxCtC,OAAA;wBAAMqC,SAAS,EAAC,uCAAuC;wBAAAC,QAAA,EAAEX,IAAI,CAAC6B;sBAAS;wBAAAjB,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAO;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAC7E,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACF,CAAC,EACLf,IAAI,CAAC8B,eAAe,IAAI9B,IAAI,CAAC8B,eAAe,CAACd,MAAM,GAAG,CAAC,iBACtD3C,OAAA;oBAAAsC,QAAA,gBACEtC,OAAA;sBAAIqC,SAAS,EAAC,mCAAmC;sBAAAC,QAAA,EAAC;oBAAI;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAI,CAAC,eAC3D1C,OAAA;sBAAIqC,SAAS,EAAC,4BAA4B;sBAAAC,QAAA,EAAEX,IAAI,CAAC8B,eAAe,CAACH,IAAI,CAAC,IAAI;oBAAC;sBAAAf,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAK,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC9E,CACN;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACC;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACF,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH;UAAC,GAvECf,IAAI,CAACK,EAAE;YAAAO,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAwEZ,CACL;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACA;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACF;IACN;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAACxC,EAAA,CAlPID,eAAyB;AAAAyD,EAAA,GAAzBzD,eAAyB;AAoP/B,eAAeA,eAAe;AAAC,IAAAyD,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}