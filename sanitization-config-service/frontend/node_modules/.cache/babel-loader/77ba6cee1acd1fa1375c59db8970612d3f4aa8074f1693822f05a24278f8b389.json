{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"M18 11h-4a1 1 0 0 0-1 1v5a1 1 0 0 0 1 1h4\",\n  key: \"17ldeb\"\n}], [\"path\", {\n  d: \"M6 7v13a2 2 0 0 0 2 2h8a2 2 0 0 0 2-2V7\",\n  key: \"nc37y6\"\n}], [\"rect\", {\n  width: \"16\",\n  height: \"5\",\n  x: \"4\",\n  y: \"2\",\n  rx: \"1\",\n  key: \"3jeezo\"\n}]];\nconst PillBottle = createLucideIcon(\"pill-bottle\", __iconNode);\nexport { __iconNode, PillBottle as default };\n//# sourceMappingURL=pill-bottle.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}