{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"M19.43 12.935c.357-.967.57-1.955.57-2.935a8 8 0 0 0-16 0c0 4.993 5.539 10.193 7.399 11.799a1 1 0 0 0 1.202 0 32.197 32.197 0 0 0 .813-.728\",\n  key: \"1dq61d\"\n}], [\"circle\", {\n  cx: \"12\",\n  cy: \"10\",\n  r: \"3\",\n  key: \"ilqhr7\"\n}], [\"path\", {\n  d: \"m16 18 2 2 4-4\",\n  key: \"1mkfmb\"\n}]];\nconst MapPinCheck = createLucideIcon(\"map-pin-check\", __iconNode);\nexport { __iconNode, MapPinCheck as default };\n//# sourceMappingURL=map-pin-check.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}