{"ast": null, "code": "var _jsxFileName = \"/Users/<USER>/dev/work/lab/javaagent/sanitization-config-service/frontend/src/components/Dashboard.tsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { Activity, Shield, AlertTriangle, CheckCircle, RefreshCw } from 'lucide-react';\nimport { sanitizationApi } from '../services/api';\nimport { formatTimestamp } from '../utils';\nimport toast from 'react-hot-toast';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst Dashboard = () => {\n  _s();\n  const [health, setHealth] = useState(null);\n  const [metrics, setMetrics] = useState(null);\n  const [loading, setLoading] = useState(true);\n  const [refreshing, setRefreshing] = useState(false);\n  const fetchData = async () => {\n    try {\n      const [healthData, metricsData] = await Promise.all([sanitizationApi.getHealth(), sanitizationApi.getMetrics()]);\n      setHealth(healthData);\n      setMetrics(metricsData);\n    } catch (error) {\n      console.error('Failed to fetch dashboard data:', error);\n      toast.error('Failed to load dashboard data');\n    } finally {\n      setLoading(false);\n      setRefreshing(false);\n    }\n  };\n  const handleRefresh = async () => {\n    setRefreshing(true);\n    await fetchData();\n  };\n  useEffect(() => {\n    fetchData();\n    const interval = setInterval(fetchData, 30000); // Refresh every 30 seconds\n    return () => clearInterval(interval);\n  }, []);\n  if (loading) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"flex items-center justify-center h-64\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"animate-spin rounded-full h-12 w-12 border-b-2 border-primary-600\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 45,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 44,\n      columnNumber: 7\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"space-y-6\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"bg-white shadow rounded-lg p-6\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex items-center justify-between\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n            className: \"text-2xl font-bold text-gray-900 flex items-center\",\n            children: [/*#__PURE__*/_jsxDEV(Shield, {\n              className: \"h-8 w-8 text-blue-600 mr-3\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 57,\n              columnNumber: 15\n            }, this), \"\\u6570\\u636E\\u8131\\u654F\\u63A7\\u5236\\u53F0\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 56,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"text-gray-600 mt-1\",\n            children: \"\\u5B9E\\u65F6\\u76D1\\u63A7\\u548C\\u7BA1\\u7406\\u6570\\u636E\\u8131\\u654F\\u670D\\u52A1\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 60,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 55,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-center space-x-4\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-center\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: `h-3 w-3 rounded-full mr-2 ${(health === null || health === void 0 ? void 0 : health.status) === 'healthy' ? 'bg-green-400 animate-pulse' : 'bg-red-400'}`\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 64,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              className: `text-sm font-medium ${(health === null || health === void 0 ? void 0 : health.status) === 'healthy' ? 'text-green-600' : 'text-red-600'}`,\n              children: (health === null || health === void 0 ? void 0 : health.status) === 'healthy' ? '服务正常' : '服务异常'\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 65,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 63,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: handleRefresh,\n            disabled: refreshing,\n            className: \"inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50\",\n            children: [/*#__PURE__*/_jsxDEV(RefreshCw, {\n              className: `h-4 w-4 mr-2 ${refreshing ? 'animate-spin' : ''}`\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 74,\n              columnNumber: 15\n            }, this), \"\\u5237\\u65B0\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 69,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 62,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 54,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 53,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"bg-white overflow-hidden shadow rounded-lg\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"p-5\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-center\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex-shrink-0\",\n              children: /*#__PURE__*/_jsxDEV(Activity, {\n                className: `h-6 w-6 ${(health === null || health === void 0 ? void 0 : health.status) === 'healthy' ? 'text-green-600' : 'text-red-600'}`\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 88,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 87,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"ml-5 w-0 flex-1\",\n              children: /*#__PURE__*/_jsxDEV(\"dl\", {\n                children: [/*#__PURE__*/_jsxDEV(\"dt\", {\n                  className: \"text-sm font-medium text-gray-500 truncate\",\n                  children: \"\\u670D\\u52A1\\u72B6\\u6001\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 92,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"dd\", {\n                  className: \"text-lg font-medium text-gray-900\",\n                  children: (health === null || health === void 0 ? void 0 : health.status) === 'healthy' ? /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"text-green-600\",\n                    children: \"\\u6B63\\u5E38\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 95,\n                    columnNumber: 23\n                  }, this) : /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"text-red-600\",\n                    children: \"\\u5F02\\u5E38\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 97,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 93,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 91,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 90,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 86,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 85,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 84,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"bg-white overflow-hidden shadow rounded-lg\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"p-5\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-center\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex-shrink-0\",\n              children: /*#__PURE__*/_jsxDEV(Shield, {\n                className: \"h-6 w-6 text-blue-600\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 111,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 110,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"ml-5 w-0 flex-1\",\n              children: /*#__PURE__*/_jsxDEV(\"dl\", {\n                children: [/*#__PURE__*/_jsxDEV(\"dt\", {\n                  className: \"text-sm font-medium text-gray-500 truncate\",\n                  children: \"\\u89C4\\u5219\\u603B\\u6570\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 115,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"dd\", {\n                  className: \"text-lg font-medium text-gray-900\",\n                  children: (metrics === null || metrics === void 0 ? void 0 : metrics.totalRules) || 0\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 116,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 114,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 113,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 109,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 108,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 107,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"bg-white overflow-hidden shadow rounded-lg\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"p-5\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-center\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex-shrink-0\",\n              children: /*#__PURE__*/_jsxDEV(CheckCircle, {\n                className: \"h-6 w-6 text-green-600\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 128,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 127,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"ml-5 w-0 flex-1\",\n              children: /*#__PURE__*/_jsxDEV(\"dl\", {\n                children: [/*#__PURE__*/_jsxDEV(\"dt\", {\n                  className: \"text-sm font-medium text-gray-500 truncate\",\n                  children: \"\\u542F\\u7528\\u89C4\\u5219\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 132,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"dd\", {\n                  className: \"text-lg font-medium text-gray-900\",\n                  children: (metrics === null || metrics === void 0 ? void 0 : metrics.enabledRules) || 0\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 133,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 131,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 130,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 126,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 125,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 124,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"bg-white overflow-hidden shadow rounded-lg\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"p-5\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-center\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex-shrink-0\",\n              children: /*#__PURE__*/_jsxDEV(AlertTriangle, {\n                className: \"h-6 w-6 text-yellow-600\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 145,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 144,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"ml-5 w-0 flex-1\",\n              children: /*#__PURE__*/_jsxDEV(\"dl\", {\n                children: [/*#__PURE__*/_jsxDEV(\"dt\", {\n                  className: \"text-sm font-medium text-gray-500 truncate\",\n                  children: \"\\u7981\\u7528\\u89C4\\u5219\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 149,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"dd\", {\n                  className: \"text-lg font-medium text-gray-900\",\n                  children: (metrics === null || metrics === void 0 ? void 0 : metrics.disabledRules) || 0\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 150,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 148,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 147,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 143,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 142,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 141,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 82,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"grid grid-cols-1 lg:grid-cols-2 gap-6\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"bg-white shadow rounded-lg p-6\",\n        children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n          className: \"text-lg font-medium text-gray-900 mb-4\",\n          children: \"\\u6309\\u7C7B\\u578B\\u5206\\u5E03\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 162,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"space-y-3\",\n          children: (metrics === null || metrics === void 0 ? void 0 : metrics.rulesByType) && Object.entries(metrics.rulesByType).map(([type, count]) => {\n            const typeNames = {\n              'FIELD_NAME': '字段名称',\n              'PATTERN': '正则模式',\n              'CONTENT_TYPE': '内容类型',\n              'CUSTOM': '自定义'\n            };\n            return /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-center justify-between\",\n              children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"text-sm text-gray-600\",\n                children: typeNames[type] || type\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 173,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"text-sm font-medium text-gray-900\",\n                children: count\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 174,\n                columnNumber: 19\n              }, this)]\n            }, type, true, {\n              fileName: _jsxFileName,\n              lineNumber: 172,\n              columnNumber: 17\n            }, this);\n          })\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 163,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 161,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"bg-white shadow rounded-lg p-6\",\n        children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n          className: \"text-lg font-medium text-gray-900 mb-4\",\n          children: \"\\u6309\\u4E25\\u91CD\\u7A0B\\u5EA6\\u5206\\u5E03\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 183,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"space-y-3\",\n          children: (metrics === null || metrics === void 0 ? void 0 : metrics.rulesBySeverity) && Object.entries(metrics.rulesBySeverity).map(([severity, count]) => {\n            const severityNames = {\n              'CRITICAL': '严重',\n              'HIGH': '高',\n              'MEDIUM': '中',\n              'LOW': '低'\n            };\n            return /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-center justify-between\",\n              children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"text-sm text-gray-600\",\n                children: severityNames[severity] || severity\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 194,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"text-sm font-medium text-gray-900\",\n                children: count\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 195,\n                columnNumber: 19\n              }, this)]\n            }, severity, true, {\n              fileName: _jsxFileName,\n              lineNumber: 193,\n              columnNumber: 17\n            }, this);\n          })\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 184,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 182,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 159,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"bg-white shadow rounded-lg p-6\",\n      children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n        className: \"text-lg font-medium text-gray-900 mb-4\",\n        children: \"\\u7CFB\\u7EDF\\u4FE1\\u606F\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 205,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"grid grid-cols-1 md:grid-cols-3 gap-4\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          children: [/*#__PURE__*/_jsxDEV(\"dt\", {\n            className: \"text-sm font-medium text-gray-500\",\n            children: \"\\u670D\\u52A1\\u7248\\u672C\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 208,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"dd\", {\n            className: \"mt-1 text-sm text-gray-900\",\n            children: (health === null || health === void 0 ? void 0 : health.version) || 'N/A'\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 209,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 207,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          children: [/*#__PURE__*/_jsxDEV(\"dt\", {\n            className: \"text-sm font-medium text-gray-500\",\n            children: \"\\u914D\\u7F6E\\u7248\\u672C\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 212,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"dd\", {\n            className: \"mt-1 text-sm text-gray-900\",\n            children: (metrics === null || metrics === void 0 ? void 0 : metrics.configVersion) || 'N/A'\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 213,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 211,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          children: [/*#__PURE__*/_jsxDEV(\"dt\", {\n            className: \"text-sm font-medium text-gray-500\",\n            children: \"\\u6700\\u540E\\u66F4\\u65B0\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 216,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"dd\", {\n            className: \"mt-1 text-sm text-gray-900\",\n            children: metrics !== null && metrics !== void 0 && metrics.lastUpdated ? formatTimestamp(metrics.lastUpdated) : 'N/A'\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 217,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 215,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 206,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 204,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 51,\n    columnNumber: 5\n  }, this);\n};\n_s(Dashboard, \"J+se3TZYZJ495TdSp5YzGnZerlM=\");\n_c = Dashboard;\nexport default Dashboard;\nvar _c;\n$RefreshReg$(_c, \"Dashboard\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "Activity", "Shield", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "CheckCircle", "RefreshCw", "sanitizationApi", "formatTimestamp", "toast", "jsxDEV", "_jsxDEV", "Dashboard", "_s", "health", "setHealth", "metrics", "setMetrics", "loading", "setLoading", "refreshing", "setRefreshing", "fetchData", "healthData", "metricsData", "Promise", "all", "getHealth", "getMetrics", "error", "console", "handleRefresh", "interval", "setInterval", "clearInterval", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "status", "onClick", "disabled", "totalRules", "enabledRules", "disabledRules", "rulesByType", "Object", "entries", "map", "type", "count", "typeNames", "rulesBySeverity", "severity", "severityNames", "version", "configVersion", "lastUpdated", "_c", "$RefreshReg$"], "sources": ["/Users/<USER>/dev/work/lab/javaagent/sanitization-config-service/frontend/src/components/Dashboard.tsx"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { Activity, Shield, AlertTriangle, CheckCircle, RefreshCw } from 'lucide-react';\nimport { sanitizationApi } from '../services/api';\nimport { HealthResponse, MetricsResponse } from '../types';\nimport { formatTimestamp } from '../utils';\nimport toast from 'react-hot-toast';\n\nconst Dashboard: React.FC = () => {\n  const [health, setHealth] = useState<HealthResponse | null>(null);\n  const [metrics, setMetrics] = useState<MetricsResponse | null>(null);\n  const [loading, setLoading] = useState(true);\n  const [refreshing, setRefreshing] = useState(false);\n\n  const fetchData = async () => {\n    try {\n      const [healthData, metricsData] = await Promise.all([\n        sanitizationApi.getHealth(),\n        sanitizationApi.getMetrics(),\n      ]);\n      setHealth(healthData);\n      setMetrics(metricsData);\n    } catch (error) {\n      console.error('Failed to fetch dashboard data:', error);\n      toast.error('Failed to load dashboard data');\n    } finally {\n      setLoading(false);\n      setRefreshing(false);\n    }\n  };\n\n  const handleRefresh = async () => {\n    setRefreshing(true);\n    await fetchData();\n  };\n\n  useEffect(() => {\n    fetchData();\n    const interval = setInterval(fetchData, 30000); // Refresh every 30 seconds\n    return () => clearInterval(interval);\n  }, []);\n\n  if (loading) {\n    return (\n      <div className=\"flex items-center justify-center h-64\">\n        <div className=\"animate-spin rounded-full h-12 w-12 border-b-2 border-primary-600\"></div>\n      </div>\n    );\n  }\n\n  return (\n    <div className=\"space-y-6\">\n      {/* Header */}\n      <div className=\"bg-white shadow rounded-lg p-6\">\n        <div className=\"flex items-center justify-between\">\n          <div>\n            <h1 className=\"text-2xl font-bold text-gray-900 flex items-center\">\n              <Shield className=\"h-8 w-8 text-blue-600 mr-3\" />\n              数据脱敏控制台\n            </h1>\n            <p className=\"text-gray-600 mt-1\">实时监控和管理数据脱敏服务</p>\n          </div>\n          <div className=\"flex items-center space-x-4\">\n            <div className=\"flex items-center\">\n              <div className={`h-3 w-3 rounded-full mr-2 ${health?.status === 'healthy' ? 'bg-green-400 animate-pulse' : 'bg-red-400'}`}></div>\n              <span className={`text-sm font-medium ${health?.status === 'healthy' ? 'text-green-600' : 'text-red-600'}`}>\n                {health?.status === 'healthy' ? '服务正常' : '服务异常'}\n              </span>\n            </div>\n            <button\n              onClick={handleRefresh}\n              disabled={refreshing}\n              className=\"inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50\"\n            >\n              <RefreshCw className={`h-4 w-4 mr-2 ${refreshing ? 'animate-spin' : ''}`} />\n              刷新\n            </button>\n          </div>\n        </div>\n      </div>\n\n      {/* Status Cards */}\n      <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6\">\n        {/* Service Status */}\n        <div className=\"bg-white overflow-hidden shadow rounded-lg\">\n          <div className=\"p-5\">\n            <div className=\"flex items-center\">\n              <div className=\"flex-shrink-0\">\n                <Activity className={`h-6 w-6 ${health?.status === 'healthy' ? 'text-green-600' : 'text-red-600'}`} />\n              </div>\n              <div className=\"ml-5 w-0 flex-1\">\n                <dl>\n                  <dt className=\"text-sm font-medium text-gray-500 truncate\">服务状态</dt>\n                  <dd className=\"text-lg font-medium text-gray-900\">\n                    {health?.status === 'healthy' ? (\n                      <span className=\"text-green-600\">正常</span>\n                    ) : (\n                      <span className=\"text-red-600\">异常</span>\n                    )}\n                  </dd>\n                </dl>\n              </div>\n            </div>\n          </div>\n        </div>\n\n        {/* Total Rules */}\n        <div className=\"bg-white overflow-hidden shadow rounded-lg\">\n          <div className=\"p-5\">\n            <div className=\"flex items-center\">\n              <div className=\"flex-shrink-0\">\n                <Shield className=\"h-6 w-6 text-blue-600\" />\n              </div>\n              <div className=\"ml-5 w-0 flex-1\">\n                <dl>\n                  <dt className=\"text-sm font-medium text-gray-500 truncate\">规则总数</dt>\n                  <dd className=\"text-lg font-medium text-gray-900\">{metrics?.totalRules || 0}</dd>\n                </dl>\n              </div>\n            </div>\n          </div>\n        </div>\n\n        {/* Enabled Rules */}\n        <div className=\"bg-white overflow-hidden shadow rounded-lg\">\n          <div className=\"p-5\">\n            <div className=\"flex items-center\">\n              <div className=\"flex-shrink-0\">\n                <CheckCircle className=\"h-6 w-6 text-green-600\" />\n              </div>\n              <div className=\"ml-5 w-0 flex-1\">\n                <dl>\n                  <dt className=\"text-sm font-medium text-gray-500 truncate\">启用规则</dt>\n                  <dd className=\"text-lg font-medium text-gray-900\">{metrics?.enabledRules || 0}</dd>\n                </dl>\n              </div>\n            </div>\n          </div>\n        </div>\n\n        {/* Disabled Rules */}\n        <div className=\"bg-white overflow-hidden shadow rounded-lg\">\n          <div className=\"p-5\">\n            <div className=\"flex items-center\">\n              <div className=\"flex-shrink-0\">\n                <AlertTriangle className=\"h-6 w-6 text-yellow-600\" />\n              </div>\n              <div className=\"ml-5 w-0 flex-1\">\n                <dl>\n                  <dt className=\"text-sm font-medium text-gray-500 truncate\">禁用规则</dt>\n                  <dd className=\"text-lg font-medium text-gray-900\">{metrics?.disabledRules || 0}</dd>\n                </dl>\n              </div>\n            </div>\n          </div>\n        </div>\n      </div>\n\n      {/* Charts Section */}\n      <div className=\"grid grid-cols-1 lg:grid-cols-2 gap-6\">\n        {/* Rules by Type */}\n        <div className=\"bg-white shadow rounded-lg p-6\">\n          <h3 className=\"text-lg font-medium text-gray-900 mb-4\">按类型分布</h3>\n          <div className=\"space-y-3\">\n            {metrics?.rulesByType && Object.entries(metrics.rulesByType).map(([type, count]) => {\n              const typeNames: Record<string, string> = {\n                'FIELD_NAME': '字段名称',\n                'PATTERN': '正则模式',\n                'CONTENT_TYPE': '内容类型',\n                'CUSTOM': '自定义'\n              };\n              return (\n                <div key={type} className=\"flex items-center justify-between\">\n                  <span className=\"text-sm text-gray-600\">{typeNames[type] || type}</span>\n                  <span className=\"text-sm font-medium text-gray-900\">{count}</span>\n                </div>\n              );\n            })}\n          </div>\n        </div>\n\n        {/* Rules by Severity */}\n        <div className=\"bg-white shadow rounded-lg p-6\">\n          <h3 className=\"text-lg font-medium text-gray-900 mb-4\">按严重程度分布</h3>\n          <div className=\"space-y-3\">\n            {metrics?.rulesBySeverity && Object.entries(metrics.rulesBySeverity).map(([severity, count]) => {\n              const severityNames: Record<string, string> = {\n                'CRITICAL': '严重',\n                'HIGH': '高',\n                'MEDIUM': '中',\n                'LOW': '低'\n              };\n              return (\n                <div key={severity} className=\"flex items-center justify-between\">\n                  <span className=\"text-sm text-gray-600\">{severityNames[severity] || severity}</span>\n                  <span className=\"text-sm font-medium text-gray-900\">{count}</span>\n                </div>\n              );\n            })}\n          </div>\n        </div>\n      </div>\n\n      {/* System Info */}\n      <div className=\"bg-white shadow rounded-lg p-6\">\n        <h3 className=\"text-lg font-medium text-gray-900 mb-4\">系统信息</h3>\n        <div className=\"grid grid-cols-1 md:grid-cols-3 gap-4\">\n          <div>\n            <dt className=\"text-sm font-medium text-gray-500\">服务版本</dt>\n            <dd className=\"mt-1 text-sm text-gray-900\">{health?.version || 'N/A'}</dd>\n          </div>\n          <div>\n            <dt className=\"text-sm font-medium text-gray-500\">配置版本</dt>\n            <dd className=\"mt-1 text-sm text-gray-900\">{metrics?.configVersion || 'N/A'}</dd>\n          </div>\n          <div>\n            <dt className=\"text-sm font-medium text-gray-500\">最后更新</dt>\n            <dd className=\"mt-1 text-sm text-gray-900\">\n              {metrics?.lastUpdated ? formatTimestamp(metrics.lastUpdated) : 'N/A'}\n            </dd>\n          </div>\n        </div>\n      </div>\n    </div>\n  );\n};\n\nexport default Dashboard;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,QAAQ,EAAEC,MAAM,EAAEC,aAAa,EAAEC,WAAW,EAAEC,SAAS,QAAQ,cAAc;AACtF,SAASC,eAAe,QAAQ,iBAAiB;AAEjD,SAASC,eAAe,QAAQ,UAAU;AAC1C,OAAOC,KAAK,MAAM,iBAAiB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEpC,MAAMC,SAAmB,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAChC,MAAM,CAACC,MAAM,EAAEC,SAAS,CAAC,GAAGf,QAAQ,CAAwB,IAAI,CAAC;EACjE,MAAM,CAACgB,OAAO,EAAEC,UAAU,CAAC,GAAGjB,QAAQ,CAAyB,IAAI,CAAC;EACpE,MAAM,CAACkB,OAAO,EAAEC,UAAU,CAAC,GAAGnB,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAM,CAACoB,UAAU,EAAEC,aAAa,CAAC,GAAGrB,QAAQ,CAAC,KAAK,CAAC;EAEnD,MAAMsB,SAAS,GAAG,MAAAA,CAAA,KAAY;IAC5B,IAAI;MACF,MAAM,CAACC,UAAU,EAAEC,WAAW,CAAC,GAAG,MAAMC,OAAO,CAACC,GAAG,CAAC,CAClDnB,eAAe,CAACoB,SAAS,CAAC,CAAC,EAC3BpB,eAAe,CAACqB,UAAU,CAAC,CAAC,CAC7B,CAAC;MACFb,SAAS,CAACQ,UAAU,CAAC;MACrBN,UAAU,CAACO,WAAW,CAAC;IACzB,CAAC,CAAC,OAAOK,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,iCAAiC,EAAEA,KAAK,CAAC;MACvDpB,KAAK,CAACoB,KAAK,CAAC,+BAA+B,CAAC;IAC9C,CAAC,SAAS;MACRV,UAAU,CAAC,KAAK,CAAC;MACjBE,aAAa,CAAC,KAAK,CAAC;IACtB;EACF,CAAC;EAED,MAAMU,aAAa,GAAG,MAAAA,CAAA,KAAY;IAChCV,aAAa,CAAC,IAAI,CAAC;IACnB,MAAMC,SAAS,CAAC,CAAC;EACnB,CAAC;EAEDrB,SAAS,CAAC,MAAM;IACdqB,SAAS,CAAC,CAAC;IACX,MAAMU,QAAQ,GAAGC,WAAW,CAACX,SAAS,EAAE,KAAK,CAAC,CAAC,CAAC;IAChD,OAAO,MAAMY,aAAa,CAACF,QAAQ,CAAC;EACtC,CAAC,EAAE,EAAE,CAAC;EAEN,IAAId,OAAO,EAAE;IACX,oBACEP,OAAA;MAAKwB,SAAS,EAAC,uCAAuC;MAAAC,QAAA,eACpDzB,OAAA;QAAKwB,SAAS,EAAC;MAAmE;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACtF,CAAC;EAEV;EAEA,oBACE7B,OAAA;IAAKwB,SAAS,EAAC,WAAW;IAAAC,QAAA,gBAExBzB,OAAA;MAAKwB,SAAS,EAAC,gCAAgC;MAAAC,QAAA,eAC7CzB,OAAA;QAAKwB,SAAS,EAAC,mCAAmC;QAAAC,QAAA,gBAChDzB,OAAA;UAAAyB,QAAA,gBACEzB,OAAA;YAAIwB,SAAS,EAAC,oDAAoD;YAAAC,QAAA,gBAChEzB,OAAA,CAACR,MAAM;cAACgC,SAAS,EAAC;YAA4B;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,8CAEnD;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACL7B,OAAA;YAAGwB,SAAS,EAAC,oBAAoB;YAAAC,QAAA,EAAC;UAAa;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAChD,CAAC,eACN7B,OAAA;UAAKwB,SAAS,EAAC,6BAA6B;UAAAC,QAAA,gBAC1CzB,OAAA;YAAKwB,SAAS,EAAC,mBAAmB;YAAAC,QAAA,gBAChCzB,OAAA;cAAKwB,SAAS,EAAE,6BAA6B,CAAArB,MAAM,aAANA,MAAM,uBAANA,MAAM,CAAE2B,MAAM,MAAK,SAAS,GAAG,4BAA4B,GAAG,YAAY;YAAG;cAAAJ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eACjI7B,OAAA;cAAMwB,SAAS,EAAE,uBAAuB,CAAArB,MAAM,aAANA,MAAM,uBAANA,MAAM,CAAE2B,MAAM,MAAK,SAAS,GAAG,gBAAgB,GAAG,cAAc,EAAG;cAAAL,QAAA,EACxG,CAAAtB,MAAM,aAANA,MAAM,uBAANA,MAAM,CAAE2B,MAAM,MAAK,SAAS,GAAG,MAAM,GAAG;YAAM;cAAAJ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC3C,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACJ,CAAC,eACN7B,OAAA;YACE+B,OAAO,EAAEX,aAAc;YACvBY,QAAQ,EAAEvB,UAAW;YACrBe,SAAS,EAAC,4OAA4O;YAAAC,QAAA,gBAEtPzB,OAAA,CAACL,SAAS;cAAC6B,SAAS,EAAE,gBAAgBf,UAAU,GAAG,cAAc,GAAG,EAAE;YAAG;cAAAiB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,gBAE9E;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGN7B,OAAA;MAAKwB,SAAS,EAAC,sDAAsD;MAAAC,QAAA,gBAEnEzB,OAAA;QAAKwB,SAAS,EAAC,4CAA4C;QAAAC,QAAA,eACzDzB,OAAA;UAAKwB,SAAS,EAAC,KAAK;UAAAC,QAAA,eAClBzB,OAAA;YAAKwB,SAAS,EAAC,mBAAmB;YAAAC,QAAA,gBAChCzB,OAAA;cAAKwB,SAAS,EAAC,eAAe;cAAAC,QAAA,eAC5BzB,OAAA,CAACT,QAAQ;gBAACiC,SAAS,EAAE,WAAW,CAAArB,MAAM,aAANA,MAAM,uBAANA,MAAM,CAAE2B,MAAM,MAAK,SAAS,GAAG,gBAAgB,GAAG,cAAc;cAAG;gBAAAJ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACnG,CAAC,eACN7B,OAAA;cAAKwB,SAAS,EAAC,iBAAiB;cAAAC,QAAA,eAC9BzB,OAAA;gBAAAyB,QAAA,gBACEzB,OAAA;kBAAIwB,SAAS,EAAC,4CAA4C;kBAAAC,QAAA,EAAC;gBAAI;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eACpE7B,OAAA;kBAAIwB,SAAS,EAAC,mCAAmC;kBAAAC,QAAA,EAC9C,CAAAtB,MAAM,aAANA,MAAM,uBAANA,MAAM,CAAE2B,MAAM,MAAK,SAAS,gBAC3B9B,OAAA;oBAAMwB,SAAS,EAAC,gBAAgB;oBAAAC,QAAA,EAAC;kBAAE;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC,gBAE1C7B,OAAA;oBAAMwB,SAAS,EAAC,cAAc;oBAAAC,QAAA,EAAC;kBAAE;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM;gBACxC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACC,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACF,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAGN7B,OAAA;QAAKwB,SAAS,EAAC,4CAA4C;QAAAC,QAAA,eACzDzB,OAAA;UAAKwB,SAAS,EAAC,KAAK;UAAAC,QAAA,eAClBzB,OAAA;YAAKwB,SAAS,EAAC,mBAAmB;YAAAC,QAAA,gBAChCzB,OAAA;cAAKwB,SAAS,EAAC,eAAe;cAAAC,QAAA,eAC5BzB,OAAA,CAACR,MAAM;gBAACgC,SAAS,EAAC;cAAuB;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACzC,CAAC,eACN7B,OAAA;cAAKwB,SAAS,EAAC,iBAAiB;cAAAC,QAAA,eAC9BzB,OAAA;gBAAAyB,QAAA,gBACEzB,OAAA;kBAAIwB,SAAS,EAAC,4CAA4C;kBAAAC,QAAA,EAAC;gBAAI;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eACpE7B,OAAA;kBAAIwB,SAAS,EAAC,mCAAmC;kBAAAC,QAAA,EAAE,CAAApB,OAAO,aAAPA,OAAO,uBAAPA,OAAO,CAAE4B,UAAU,KAAI;gBAAC;kBAAAP,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC/E;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACF,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAGN7B,OAAA;QAAKwB,SAAS,EAAC,4CAA4C;QAAAC,QAAA,eACzDzB,OAAA;UAAKwB,SAAS,EAAC,KAAK;UAAAC,QAAA,eAClBzB,OAAA;YAAKwB,SAAS,EAAC,mBAAmB;YAAAC,QAAA,gBAChCzB,OAAA;cAAKwB,SAAS,EAAC,eAAe;cAAAC,QAAA,eAC5BzB,OAAA,CAACN,WAAW;gBAAC8B,SAAS,EAAC;cAAwB;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC/C,CAAC,eACN7B,OAAA;cAAKwB,SAAS,EAAC,iBAAiB;cAAAC,QAAA,eAC9BzB,OAAA;gBAAAyB,QAAA,gBACEzB,OAAA;kBAAIwB,SAAS,EAAC,4CAA4C;kBAAAC,QAAA,EAAC;gBAAI;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eACpE7B,OAAA;kBAAIwB,SAAS,EAAC,mCAAmC;kBAAAC,QAAA,EAAE,CAAApB,OAAO,aAAPA,OAAO,uBAAPA,OAAO,CAAE6B,YAAY,KAAI;gBAAC;kBAAAR,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACjF;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACF,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAGN7B,OAAA;QAAKwB,SAAS,EAAC,4CAA4C;QAAAC,QAAA,eACzDzB,OAAA;UAAKwB,SAAS,EAAC,KAAK;UAAAC,QAAA,eAClBzB,OAAA;YAAKwB,SAAS,EAAC,mBAAmB;YAAAC,QAAA,gBAChCzB,OAAA;cAAKwB,SAAS,EAAC,eAAe;cAAAC,QAAA,eAC5BzB,OAAA,CAACP,aAAa;gBAAC+B,SAAS,EAAC;cAAyB;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAClD,CAAC,eACN7B,OAAA;cAAKwB,SAAS,EAAC,iBAAiB;cAAAC,QAAA,eAC9BzB,OAAA;gBAAAyB,QAAA,gBACEzB,OAAA;kBAAIwB,SAAS,EAAC,4CAA4C;kBAAAC,QAAA,EAAC;gBAAI;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eACpE7B,OAAA;kBAAIwB,SAAS,EAAC,mCAAmC;kBAAAC,QAAA,EAAE,CAAApB,OAAO,aAAPA,OAAO,uBAAPA,OAAO,CAAE8B,aAAa,KAAI;gBAAC;kBAAAT,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAClF;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACF,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGN7B,OAAA;MAAKwB,SAAS,EAAC,uCAAuC;MAAAC,QAAA,gBAEpDzB,OAAA;QAAKwB,SAAS,EAAC,gCAAgC;QAAAC,QAAA,gBAC7CzB,OAAA;UAAIwB,SAAS,EAAC,wCAAwC;UAAAC,QAAA,EAAC;QAAK;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACjE7B,OAAA;UAAKwB,SAAS,EAAC,WAAW;UAAAC,QAAA,EACvB,CAAApB,OAAO,aAAPA,OAAO,uBAAPA,OAAO,CAAE+B,WAAW,KAAIC,MAAM,CAACC,OAAO,CAACjC,OAAO,CAAC+B,WAAW,CAAC,CAACG,GAAG,CAAC,CAAC,CAACC,IAAI,EAAEC,KAAK,CAAC,KAAK;YAClF,MAAMC,SAAiC,GAAG;cACxC,YAAY,EAAE,MAAM;cACpB,SAAS,EAAE,MAAM;cACjB,cAAc,EAAE,MAAM;cACtB,QAAQ,EAAE;YACZ,CAAC;YACD,oBACE1C,OAAA;cAAgBwB,SAAS,EAAC,mCAAmC;cAAAC,QAAA,gBAC3DzB,OAAA;gBAAMwB,SAAS,EAAC,uBAAuB;gBAAAC,QAAA,EAAEiB,SAAS,CAACF,IAAI,CAAC,IAAIA;cAAI;gBAAAd,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eACxE7B,OAAA;gBAAMwB,SAAS,EAAC,mCAAmC;gBAAAC,QAAA,EAAEgB;cAAK;gBAAAf,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC;YAAA,GAF1DW,IAAI;cAAAd,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAGT,CAAC;UAEV,CAAC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAGN7B,OAAA;QAAKwB,SAAS,EAAC,gCAAgC;QAAAC,QAAA,gBAC7CzB,OAAA;UAAIwB,SAAS,EAAC,wCAAwC;UAAAC,QAAA,EAAC;QAAO;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACnE7B,OAAA;UAAKwB,SAAS,EAAC,WAAW;UAAAC,QAAA,EACvB,CAAApB,OAAO,aAAPA,OAAO,uBAAPA,OAAO,CAAEsC,eAAe,KAAIN,MAAM,CAACC,OAAO,CAACjC,OAAO,CAACsC,eAAe,CAAC,CAACJ,GAAG,CAAC,CAAC,CAACK,QAAQ,EAAEH,KAAK,CAAC,KAAK;YAC9F,MAAMI,aAAqC,GAAG;cAC5C,UAAU,EAAE,IAAI;cAChB,MAAM,EAAE,GAAG;cACX,QAAQ,EAAE,GAAG;cACb,KAAK,EAAE;YACT,CAAC;YACD,oBACE7C,OAAA;cAAoBwB,SAAS,EAAC,mCAAmC;cAAAC,QAAA,gBAC/DzB,OAAA;gBAAMwB,SAAS,EAAC,uBAAuB;gBAAAC,QAAA,EAAEoB,aAAa,CAACD,QAAQ,CAAC,IAAIA;cAAQ;gBAAAlB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eACpF7B,OAAA;gBAAMwB,SAAS,EAAC,mCAAmC;gBAAAC,QAAA,EAAEgB;cAAK;gBAAAf,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC;YAAA,GAF1De,QAAQ;cAAAlB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAGb,CAAC;UAEV,CAAC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGN7B,OAAA;MAAKwB,SAAS,EAAC,gCAAgC;MAAAC,QAAA,gBAC7CzB,OAAA;QAAIwB,SAAS,EAAC,wCAAwC;QAAAC,QAAA,EAAC;MAAI;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eAChE7B,OAAA;QAAKwB,SAAS,EAAC,uCAAuC;QAAAC,QAAA,gBACpDzB,OAAA;UAAAyB,QAAA,gBACEzB,OAAA;YAAIwB,SAAS,EAAC,mCAAmC;YAAAC,QAAA,EAAC;UAAI;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAC3D7B,OAAA;YAAIwB,SAAS,EAAC,4BAA4B;YAAAC,QAAA,EAAE,CAAAtB,MAAM,aAANA,MAAM,uBAANA,MAAM,CAAE2C,OAAO,KAAI;UAAK;YAAApB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACvE,CAAC,eACN7B,OAAA;UAAAyB,QAAA,gBACEzB,OAAA;YAAIwB,SAAS,EAAC,mCAAmC;YAAAC,QAAA,EAAC;UAAI;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAC3D7B,OAAA;YAAIwB,SAAS,EAAC,4BAA4B;YAAAC,QAAA,EAAE,CAAApB,OAAO,aAAPA,OAAO,uBAAPA,OAAO,CAAE0C,aAAa,KAAI;UAAK;YAAArB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC9E,CAAC,eACN7B,OAAA;UAAAyB,QAAA,gBACEzB,OAAA;YAAIwB,SAAS,EAAC,mCAAmC;YAAAC,QAAA,EAAC;UAAI;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAC3D7B,OAAA;YAAIwB,SAAS,EAAC,4BAA4B;YAAAC,QAAA,EACvCpB,OAAO,aAAPA,OAAO,eAAPA,OAAO,CAAE2C,WAAW,GAAGnD,eAAe,CAACQ,OAAO,CAAC2C,WAAW,CAAC,GAAG;UAAK;YAAAtB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAClE,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAAC3B,EAAA,CAzNID,SAAmB;AAAAgD,EAAA,GAAnBhD,SAAmB;AA2NzB,eAAeA,SAAS;AAAC,IAAAgD,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}