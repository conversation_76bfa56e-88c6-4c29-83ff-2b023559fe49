{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"m10.5 20.5 10-10a4.95 4.95 0 1 0-7-7l-10 10a4.95 4.95 0 1 0 7 7Z\",\n  key: \"wa1lgi\"\n}], [\"path\", {\n  d: \"m8.5 8.5 7 7\",\n  key: \"rvfmvr\"\n}]];\nconst Pill = createLucideIcon(\"pill\", __iconNode);\nexport { __iconNode, Pill as default };\n//# sourceMappingURL=pill.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}