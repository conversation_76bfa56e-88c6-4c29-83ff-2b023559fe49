{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"M21 15V6\",\n  key: \"h1cx4g\"\n}], [\"path\", {\n  d: \"M18.5 18a2.5 2.5 0 1 0 0-5 2.5 2.5 0 0 0 0 5Z\",\n  key: \"8saifv\"\n}], [\"path\", {\n  d: \"M12 12H3\",\n  key: \"18klou\"\n}], [\"path\", {\n  d: \"M16 6H3\",\n  key: \"1wxfjs\"\n}], [\"path\", {\n  d: \"M12 18H3\",\n  key: \"11ftsu\"\n}]];\nconst ListMusic = createLucideIcon(\"list-music\", __iconNode);\nexport { __iconNode, ListMusic as default };\n//# sourceMappingURL=list-music.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}