{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"M12 15v7\",\n  key: \"t2xh3l\"\n}], [\"path\", {\n  d: \"M9 19h6\",\n  key: \"456am0\"\n}], [\"circle\", {\n  cx: \"12\",\n  cy: \"9\",\n  r: \"6\",\n  key: \"1nw4tq\"\n}]];\nconst Venus = createLucideIcon(\"venus\", __iconNode);\nexport { __iconNode, Venus as default };\n//# sourceMappingURL=venus.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}