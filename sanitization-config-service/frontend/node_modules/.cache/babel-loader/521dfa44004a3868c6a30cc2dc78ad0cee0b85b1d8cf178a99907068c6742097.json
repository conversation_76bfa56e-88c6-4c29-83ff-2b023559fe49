{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"rect\", {\n  width: \"20\",\n  height: \"15\",\n  x: \"2\",\n  y: \"4\",\n  rx: \"2\",\n  key: \"2no95f\"\n}], [\"rect\", {\n  width: \"8\",\n  height: \"7\",\n  x: \"6\",\n  y: \"8\",\n  rx: \"1\",\n  key: \"zh9wx\"\n}], [\"path\", {\n  d: \"M18 8v7\",\n  key: \"o5zi4n\"\n}], [\"path\", {\n  d: \"M6 19v2\",\n  key: \"1loha6\"\n}], [\"path\", {\n  d: \"M18 19v2\",\n  key: \"1dawf0\"\n}]];\nconst Microwave = createLucideIcon(\"microwave\", __iconNode);\nexport { __iconNode, Microwave as default };\n//# sourceMappingURL=microwave.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}