{"ast": null, "code": "var _jsxFileName = \"/Users/<USER>/dev/work/lab/javaagent/sanitization-config-service/frontend/src/App.tsx\";\nimport React from 'react';\nimport { Toaster } from 'react-hot-toast';\nimport SimpleRulesList from './components/SimpleRulesList';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nfunction App() {\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"min-h-screen bg-gray-50\",\n    children: [/*#__PURE__*/_jsxDEV(SimpleRulesList, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 8,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Toaster, {\n      position: \"top-right\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 9,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 7,\n    columnNumber: 5\n  }, this);\n}\n_c = App;\nexport default App;\nvar _c;\n$RefreshReg$(_c, \"App\");", "map": {"version": 3, "names": ["React", "Toaster", "SimpleRulesList", "jsxDEV", "_jsxDEV", "App", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "position", "_c", "$RefreshReg$"], "sources": ["/Users/<USER>/dev/work/lab/javaagent/sanitization-config-service/frontend/src/App.tsx"], "sourcesContent": ["import React from 'react';\nimport { Toaster } from 'react-hot-toast';\nimport SimpleRulesList from './components/SimpleRulesList';\n\nfunction App() {\n  return (\n    <div className=\"min-h-screen bg-gray-50\">\n      <SimpleRulesList />\n      <Toaster position=\"top-right\" />\n    </div>\n  );\n}\n\nexport default App;\n"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAASC,OAAO,QAAQ,iBAAiB;AACzC,OAAOC,eAAe,MAAM,8BAA8B;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE3D,SAASC,GAAGA,CAAA,EAAG;EACb,oBACED,OAAA;IAAKE,SAAS,EAAC,yBAAyB;IAAAC,QAAA,gBACtCH,OAAA,CAACF,eAAe;MAAAM,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,eACnBP,OAAA,CAACH,OAAO;MAACW,QAAQ,EAAC;IAAW;MAAAJ,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAC7B,CAAC;AAEV;AAACE,EAAA,GAPQR,GAAG;AASZ,eAAeA,GAAG;AAAC,IAAAQ,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}