{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"m15 12-8.373 8.373a1 1 0 1 1-3-3L12 9\",\n  key: \"eefl8a\"\n}], [\"path\", {\n  d: \"m18 15 4-4\",\n  key: \"16gjal\"\n}], [\"path\", {\n  d: \"m21.5 11.5-1.914-1.914A2 2 0 0 1 19 8.172V7l-2.26-2.26a6 6 0 0 0-4.202-1.756L9 2.96l.92.82A6.18 6.18 0 0 1 12 8.4V10l2 2h1.172a2 2 0 0 1 1.414.586L18.5 14.5\",\n  key: \"b7pghm\"\n}]];\nconst Hammer = createLucideIcon(\"hammer\", __iconNode);\nexport { __iconNode, Hammer as default };\n//# sourceMappingURL=hammer.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}