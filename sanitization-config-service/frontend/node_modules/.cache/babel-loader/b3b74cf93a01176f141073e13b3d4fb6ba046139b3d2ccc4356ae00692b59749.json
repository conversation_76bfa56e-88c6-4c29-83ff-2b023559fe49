{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"line\", {\n  x1: \"2\",\n  x2: \"22\",\n  y1: \"2\",\n  y2: \"22\",\n  key: \"a6p6uj\"\n}], [\"path\", {\n  d: \"M10.41 10.41a2 2 0 1 1-2.83-2.83\",\n  key: \"1bzlo9\"\n}], [\"line\", {\n  x1: \"13.5\",\n  x2: \"6\",\n  y1: \"13.5\",\n  y2: \"21\",\n  key: \"1q0aeu\"\n}], [\"line\", {\n  x1: \"18\",\n  x2: \"21\",\n  y1: \"12\",\n  y2: \"15\",\n  key: \"5mozeu\"\n}], [\"path\", {\n  d: \"M3.59 3.59A1.99 1.99 0 0 0 3 5v14a2 2 0 0 0 2 2h14c.55 0 1.052-.22 1.41-.59\",\n  key: \"mmje98\"\n}], [\"path\", {\n  d: \"M21 15V5a2 2 0 0 0-2-2H9\",\n  key: \"43el77\"\n}]];\nconst ImageOff = createLucideIcon(\"image-off\", __iconNode);\nexport { __iconNode, ImageOff as default };\n//# sourceMappingURL=image-off.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}