{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"M4 10a8 8 0 1 1 8 8H4\",\n  key: \"svv66n\"\n}], [\"path\", {\n  d: \"m8 22-4-4 4-4\",\n  key: \"6g7gki\"\n}]];\nconst IterationCw = createLucideIcon(\"iteration-cw\", __iconNode);\nexport { __iconNode, IterationCw as default };\n//# sourceMappingURL=iteration-cw.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}