{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"m9 12 2 2 4-4\",\n  key: \"dzmm74\"\n}], [\"path\", {\n  d: \"M5 7c0-1.1.9-2 2-2h10a2 2 0 0 1 2 2v12H5V7Z\",\n  key: \"1ezoue\"\n}], [\"path\", {\n  d: \"M22 19H2\",\n  key: \"nuriw5\"\n}]];\nconst Vote = createLucideIcon(\"vote\", __iconNode);\nexport { __iconNode, Vote as default };\n//# sourceMappingURL=vote.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}