{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"M10 21V3h8\",\n  key: \"br2l0g\"\n}], [\"path\", {\n  d: \"M6 16h9\",\n  key: \"2py0wn\"\n}], [\"path\", {\n  d: \"M10 9.5h7\",\n  key: \"13dmhz\"\n}]];\nconst SwissFranc = createLucideIcon(\"swiss-franc\", __iconNode);\nexport { __iconNode, SwissFranc as default };\n//# sourceMappingURL=swiss-franc.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}