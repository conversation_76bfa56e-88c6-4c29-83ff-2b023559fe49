{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"M11 17a4 4 0 0 1-8 0V5a2 2 0 0 1 2-2h4a2 2 0 0 1 2 2Z\",\n  key: \"1ldrpk\"\n}], [\"path\", {\n  d: \"M16.7 13H19a2 2 0 0 1 2 2v4a2 2 0 0 1-2 2H7\",\n  key: \"11i5po\"\n}], [\"path\", {\n  d: \"M 7 17h.01\",\n  key: \"1euzgo\"\n}], [\"path\", {\n  d: \"m11 8 2.3-2.3a2.4 2.4 0 0 1 3.404.004L18.6 7.6a2.4 2.4 0 0 1 .026 3.434L9.9 19.8\",\n  key: \"o2gii7\"\n}]];\nconst SwatchBook = createLucideIcon(\"swatch-book\", __iconNode);\nexport { __iconNode, SwatchBook as default };\n//# sourceMappingURL=swatch-book.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}