{"ast": null, "code": "var _jsxFileName = \"/Users/<USER>/dev/work/lab/javaagent/sanitization-config-service/frontend/src/components/SimpleRulesList.tsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { Search, Settings, RefreshCw, AlertCircle, CheckCircle, XCircle } from 'lucide-react';\nimport { sanitizationApi } from '../services/api';\nimport toast from 'react-hot-toast';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst SimpleRulesList = () => {\n  _s();\n  const [config, setConfig] = useState(null);\n  const [loading, setLoading] = useState(true);\n  const [searchTerm, setSearchTerm] = useState('');\n  const [toggling, setToggling] = useState(false);\n  const fetchRules = async () => {\n    try {\n      setLoading(true);\n      const response = await sanitizationApi.getRules();\n      setConfig(response);\n    } catch (error) {\n      toast.error('获取规则失败');\n      console.error('Error fetching rules:', error);\n    } finally {\n      setLoading(false);\n    }\n  };\n  const handleReload = async () => {\n    try {\n      await sanitizationApi.reloadRules();\n      toast.success('规则重载成功');\n      await fetchRules();\n    } catch (error) {\n      toast.error('规则重载失败');\n      console.error('Error reloading rules:', error);\n    }\n  };\n  const handleToggleGlobal = async () => {\n    if (!config) return;\n    try {\n      setToggling(true);\n      const newEnabled = !config.enabled;\n      const response = await sanitizationApi.toggleGlobalSwitch(newEnabled);\n      setConfig(prev => prev ? {\n        ...prev,\n        enabled: response.enabled\n      } : null);\n      toast.success(newEnabled ? '全局脱敏已启用' : '全局脱敏已禁用');\n    } catch (error) {\n      toast.error('切换全局开关失败');\n      console.error('Error toggling global switch:', error);\n    } finally {\n      setToggling(false);\n    }\n  };\n  useEffect(() => {\n    fetchRules();\n  }, []);\n  const filteredRules = (config === null || config === void 0 ? void 0 : config.rules.filter(rule => rule.name.toLowerCase().includes(searchTerm.toLowerCase()) || rule.description.toLowerCase().includes(searchTerm.toLowerCase()) || rule.id.toLowerCase().includes(searchTerm.toLowerCase()))) || [];\n  const getSeverityColor = severity => {\n    switch (severity) {\n      case 'CRITICAL':\n        return 'bg-red-100 text-red-800';\n      case 'HIGH':\n        return 'bg-orange-100 text-orange-800';\n      case 'MEDIUM':\n        return 'bg-yellow-100 text-yellow-800';\n      case 'LOW':\n        return 'bg-green-100 text-green-800';\n      default:\n        return 'bg-gray-100 text-gray-800';\n    }\n  };\n  const getTypeColor = type => {\n    switch (type) {\n      case 'FIELD_NAME':\n        return 'bg-blue-100 text-blue-800';\n      case 'PATTERN':\n        return 'bg-purple-100 text-purple-800';\n      case 'CONTENT_TYPE':\n        return 'bg-indigo-100 text-indigo-800';\n      default:\n        return 'bg-gray-100 text-gray-800';\n    }\n  };\n  if (loading) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"min-h-screen bg-gray-50 flex items-center justify-center\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"text-center\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"animate-spin rounded-full h-8 w-8 border-b-2 border-indigo-600 mx-auto\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 88,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-gray-600 mt-4\",\n          children: \"\\u52A0\\u8F7D\\u4E2D...\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 89,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 87,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 86,\n      columnNumber: 7\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"min-h-screen bg-gray-50\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"bg-white shadow\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"py-6\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"md:flex md:items-center md:justify-between\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex-1 min-w-0\",\n              children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n                className: \"text-2xl font-bold leading-7 text-gray-900 sm:text-3xl sm:truncate\",\n                children: \"\\u6570\\u636E\\u8131\\u654F\\u89C4\\u5219\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 103,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"mt-1 flex flex-col sm:flex-row sm:flex-wrap sm:mt-0 sm:space-x-6\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"mt-2 flex items-center text-sm text-gray-500\",\n                  children: [/*#__PURE__*/_jsxDEV(Settings, {\n                    className: \"flex-shrink-0 mr-1.5 h-5 w-5 text-gray-400\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 108,\n                    columnNumber: 21\n                  }, this), \"\\u5171 \", filteredRules.length, \" \\u6761\\u89C4\\u5219\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 107,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"mt-2 flex items-center text-sm\",\n                  children: config !== null && config !== void 0 && config.enabled ? /*#__PURE__*/_jsxDEV(_Fragment, {\n                    children: [/*#__PURE__*/_jsxDEV(CheckCircle, {\n                      className: \"flex-shrink-0 mr-1.5 h-5 w-5 text-green-400\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 114,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"text-green-600\",\n                      children: \"\\u5168\\u5C40\\u8131\\u654F\\u5DF2\\u542F\\u7528\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 115,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true) : /*#__PURE__*/_jsxDEV(_Fragment, {\n                    children: [/*#__PURE__*/_jsxDEV(XCircle, {\n                      className: \"flex-shrink-0 mr-1.5 h-5 w-5 text-red-400\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 119,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"text-red-600\",\n                      children: \"\\u5168\\u5C40\\u8131\\u654F\\u5DF2\\u7981\\u7528\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 120,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 111,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 106,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 102,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"mt-4 flex md:mt-0 md:ml-4\",\n              children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                onClick: handleReload,\n                className: \"inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500\",\n                children: [/*#__PURE__*/_jsxDEV(RefreshCw, {\n                  className: \"h-4 w-4 mr-2\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 131,\n                  columnNumber: 19\n                }, this), \"\\u91CD\\u8F7D\\u89C4\\u5219\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 127,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                onClick: handleToggleGlobal,\n                disabled: toggling,\n                className: `ml-3 inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white focus:outline-none focus:ring-2 focus:ring-offset-2 ${config !== null && config !== void 0 && config.enabled ? 'bg-red-600 hover:bg-red-700 focus:ring-red-500' : 'bg-green-600 hover:bg-green-700 focus:ring-green-500'} ${toggling ? 'opacity-50 cursor-not-allowed' : ''}`,\n                children: toggling ? '处理中...' : config !== null && config !== void 0 && config.enabled ? '禁用全局脱敏' : '启用全局脱敏'\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 134,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 126,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 101,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 100,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 99,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 98,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"relative max-w-md\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none\",\n          children: /*#__PURE__*/_jsxDEV(Search, {\n            className: \"h-5 w-5 text-gray-400\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 155,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 154,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n          type: \"text\",\n          value: searchTerm,\n          onChange: e => setSearchTerm(e.target.value),\n          placeholder: \"\\u641C\\u7D22\\u89C4\\u5219...\",\n          className: \"block w-full pl-10 pr-3 py-2 border border-gray-300 rounded-md leading-5 bg-white placeholder-gray-500 focus:outline-none focus:placeholder-gray-400 focus:ring-1 focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 157,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 153,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 152,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\",\n      children: filteredRules.length === 0 ? /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"text-center py-12\",\n        children: [/*#__PURE__*/_jsxDEV(AlertCircle, {\n          className: \"mx-auto h-12 w-12 text-gray-400\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 171,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n          className: \"mt-2 text-sm font-medium text-gray-900\",\n          children: \"\\u6CA1\\u6709\\u627E\\u5230\\u5339\\u914D\\u7684\\u89C4\\u5219\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 172,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"mt-1 text-sm text-gray-500\",\n          children: \"\\u8BF7\\u5C1D\\u8BD5\\u8C03\\u6574\\u641C\\u7D22\\u6761\\u4EF6\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 173,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 170,\n        columnNumber: 11\n      }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"bg-white shadow overflow-hidden sm:rounded-md\",\n        children: /*#__PURE__*/_jsxDEV(\"ul\", {\n          className: \"divide-y divide-gray-200\",\n          children: filteredRules.map(rule => /*#__PURE__*/_jsxDEV(\"li\", {\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"px-4 py-4 sm:px-6\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex items-center justify-between\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex items-center\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"flex-shrink-0\",\n                    children: /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: `inline-flex items-center justify-center h-8 w-8 rounded-full ${rule.enabled ? 'bg-green-100' : 'bg-gray-100'}`,\n                      children: rule.enabled ? /*#__PURE__*/_jsxDEV(CheckCircle, {\n                        className: \"h-5 w-5 text-green-600\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 188,\n                        columnNumber: 31\n                      }, this) : /*#__PURE__*/_jsxDEV(XCircle, {\n                        className: \"h-5 w-5 text-gray-400\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 190,\n                        columnNumber: 31\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 184,\n                      columnNumber: 27\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 183,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"ml-4\",\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"flex items-center\",\n                      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"text-sm font-medium text-gray-900\",\n                        children: rule.name\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 196,\n                        columnNumber: 29\n                      }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                        className: `ml-2 inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getSeverityColor(rule.severity)}`,\n                        children: rule.severity\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 197,\n                        columnNumber: 29\n                      }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                        className: `ml-2 inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getTypeColor(rule.type)}`,\n                        children: rule.type\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 200,\n                        columnNumber: 29\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 195,\n                      columnNumber: 27\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"mt-1 text-sm text-gray-500\",\n                      children: rule.description\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 204,\n                      columnNumber: 27\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"mt-2 text-xs text-gray-400\",\n                      children: [\"ID: \", rule.id]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 205,\n                      columnNumber: 27\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 194,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 182,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex items-center space-x-4\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"text-sm text-gray-500\",\n                    children: [\"\\u4F18\\u5148\\u7EA7: \", rule.priority]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 209,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: `inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${rule.enabled ? 'bg-green-100 text-green-800' : 'bg-gray-100 text-gray-800'}`,\n                    children: rule.enabled ? '已启用' : '已禁用'\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 212,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 208,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 181,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"mt-4\",\n                children: /*#__PURE__*/_jsxDEV(\"dl\", {\n                  className: \"grid grid-cols-1 gap-x-4 gap-y-2 sm:grid-cols-2\",\n                  children: [rule.fieldNames && rule.fieldNames.length > 0 && /*#__PURE__*/_jsxDEV(\"div\", {\n                    children: [/*#__PURE__*/_jsxDEV(\"dt\", {\n                      className: \"text-sm font-medium text-gray-500\",\n                      children: \"\\u5B57\\u6BB5\\u540D\\u79F0\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 224,\n                      columnNumber: 29\n                    }, this), /*#__PURE__*/_jsxDEV(\"dd\", {\n                      className: \"mt-1 text-sm text-gray-900\",\n                      children: rule.fieldNames.join(', ')\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 225,\n                      columnNumber: 29\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 223,\n                    columnNumber: 27\n                  }, this), rule.pattern && /*#__PURE__*/_jsxDEV(\"div\", {\n                    children: [/*#__PURE__*/_jsxDEV(\"dt\", {\n                      className: \"text-sm font-medium text-gray-500\",\n                      children: \"\\u5339\\u914D\\u6A21\\u5F0F\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 230,\n                      columnNumber: 29\n                    }, this), /*#__PURE__*/_jsxDEV(\"dd\", {\n                      className: \"mt-1 text-sm text-gray-900\",\n                      children: /*#__PURE__*/_jsxDEV(\"code\", {\n                        className: \"bg-gray-100 px-2 py-1 rounded text-xs\",\n                        children: rule.pattern\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 232,\n                        columnNumber: 31\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 231,\n                      columnNumber: 29\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 229,\n                    columnNumber: 27\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    children: [/*#__PURE__*/_jsxDEV(\"dt\", {\n                      className: \"text-sm font-medium text-gray-500\",\n                      children: \"\\u63A9\\u7801\\u503C\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 237,\n                      columnNumber: 27\n                    }, this), /*#__PURE__*/_jsxDEV(\"dd\", {\n                      className: \"mt-1 text-sm text-gray-900\",\n                      children: /*#__PURE__*/_jsxDEV(\"code\", {\n                        className: \"bg-gray-100 px-2 py-1 rounded text-xs\",\n                        children: rule.maskValue\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 239,\n                        columnNumber: 29\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 238,\n                      columnNumber: 27\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 236,\n                    columnNumber: 25\n                  }, this), rule.includeServices && rule.includeServices.length > 0 && /*#__PURE__*/_jsxDEV(\"div\", {\n                    children: [/*#__PURE__*/_jsxDEV(\"dt\", {\n                      className: \"text-sm font-medium text-gray-500\",\n                      children: \"\\u9002\\u7528\\u670D\\u52A1\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 244,\n                      columnNumber: 29\n                    }, this), /*#__PURE__*/_jsxDEV(\"dd\", {\n                      className: \"mt-1 text-sm text-gray-900\",\n                      children: rule.includeServices.join(', ')\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 245,\n                      columnNumber: 29\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 243,\n                    columnNumber: 27\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 221,\n                  columnNumber: 23\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 220,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 180,\n              columnNumber: 19\n            }, this)\n          }, rule.id, false, {\n            fileName: _jsxFileName,\n            lineNumber: 179,\n            columnNumber: 17\n          }, this))\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 177,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 176,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 168,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 96,\n    columnNumber: 5\n  }, this);\n};\n_s(SimpleRulesList, \"uKt2Xt/PrPprlIBDO5rAiYCX0KA=\");\n_c = SimpleRulesList;\nexport default SimpleRulesList;\nvar _c;\n$RefreshReg$(_c, \"SimpleRulesList\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "Search", "Settings", "RefreshCw", "AlertCircle", "CheckCircle", "XCircle", "sanitizationApi", "toast", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "SimpleRulesList", "_s", "config", "setConfig", "loading", "setLoading", "searchTerm", "setSearchTerm", "toggling", "setToggling", "fetchRules", "response", "getRules", "error", "console", "handleReload", "reloadRules", "success", "handleToggleGlobal", "newEnabled", "enabled", "toggleGlobalSwitch", "prev", "filteredRules", "rules", "filter", "rule", "name", "toLowerCase", "includes", "description", "id", "getSeverityColor", "severity", "getTypeColor", "type", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "length", "onClick", "disabled", "value", "onChange", "e", "target", "placeholder", "map", "priority", "fieldNames", "join", "pattern", "maskValue", "includeServices", "_c", "$RefreshReg$"], "sources": ["/Users/<USER>/dev/work/lab/javaagent/sanitization-config-service/frontend/src/components/SimpleRulesList.tsx"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { Search, Settings, RefreshCw, AlertCircle, CheckCircle, XCircle } from 'lucide-react';\nimport { sanitizationApi } from '../services/api';\nimport { SanitizationConfig } from '../types';\nimport toast from 'react-hot-toast';\n\nconst SimpleRulesList: React.FC = () => {\n  const [config, setConfig] = useState<SanitizationConfig | null>(null);\n  const [loading, setLoading] = useState(true);\n  const [searchTerm, setSearchTerm] = useState('');\n  const [toggling, setToggling] = useState(false);\n\n  const fetchRules = async () => {\n    try {\n      setLoading(true);\n      const response = await sanitizationApi.getRules();\n      setConfig(response);\n    } catch (error) {\n      toast.error('获取规则失败');\n      console.error('Error fetching rules:', error);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const handleReload = async () => {\n    try {\n      await sanitizationApi.reloadRules();\n      toast.success('规则重载成功');\n      await fetchRules();\n    } catch (error) {\n      toast.error('规则重载失败');\n      console.error('Error reloading rules:', error);\n    }\n  };\n\n  const handleToggleGlobal = async () => {\n    if (!config) return;\n\n    try {\n      setToggling(true);\n      const newEnabled = !config.enabled;\n      const response = await sanitizationApi.toggleGlobalSwitch(newEnabled);\n\n      setConfig(prev => prev ? { ...prev, enabled: response.enabled } : null);\n      toast.success(newEnabled ? '全局脱敏已启用' : '全局脱敏已禁用');\n    } catch (error) {\n      toast.error('切换全局开关失败');\n      console.error('Error toggling global switch:', error);\n    } finally {\n      setToggling(false);\n    }\n  };\n\n  useEffect(() => {\n    fetchRules();\n  }, []);\n\n  const filteredRules = config?.rules.filter(rule =>\n    rule.name.toLowerCase().includes(searchTerm.toLowerCase()) ||\n    rule.description.toLowerCase().includes(searchTerm.toLowerCase()) ||\n    rule.id.toLowerCase().includes(searchTerm.toLowerCase())\n  ) || [];\n\n  const getSeverityColor = (severity: string) => {\n    switch (severity) {\n      case 'CRITICAL': return 'bg-red-100 text-red-800';\n      case 'HIGH': return 'bg-orange-100 text-orange-800';\n      case 'MEDIUM': return 'bg-yellow-100 text-yellow-800';\n      case 'LOW': return 'bg-green-100 text-green-800';\n      default: return 'bg-gray-100 text-gray-800';\n    }\n  };\n\n  const getTypeColor = (type: string) => {\n    switch (type) {\n      case 'FIELD_NAME': return 'bg-blue-100 text-blue-800';\n      case 'PATTERN': return 'bg-purple-100 text-purple-800';\n      case 'CONTENT_TYPE': return 'bg-indigo-100 text-indigo-800';\n      default: return 'bg-gray-100 text-gray-800';\n    }\n  };\n\n  if (loading) {\n    return (\n      <div className=\"min-h-screen bg-gray-50 flex items-center justify-center\">\n        <div className=\"text-center\">\n          <div className=\"animate-spin rounded-full h-8 w-8 border-b-2 border-indigo-600 mx-auto\"></div>\n          <p className=\"text-gray-600 mt-4\">加载中...</p>\n        </div>\n      </div>\n    );\n  }\n\n  return (\n    <div className=\"min-h-screen bg-gray-50\">\n      {/* Header */}\n      <div className=\"bg-white shadow\">\n        <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n          <div className=\"py-6\">\n            <div className=\"md:flex md:items-center md:justify-between\">\n              <div className=\"flex-1 min-w-0\">\n                <h2 className=\"text-2xl font-bold leading-7 text-gray-900 sm:text-3xl sm:truncate\">\n                  数据脱敏规则\n                </h2>\n                <div className=\"mt-1 flex flex-col sm:flex-row sm:flex-wrap sm:mt-0 sm:space-x-6\">\n                  <div className=\"mt-2 flex items-center text-sm text-gray-500\">\n                    <Settings className=\"flex-shrink-0 mr-1.5 h-5 w-5 text-gray-400\" />\n                    共 {filteredRules.length} 条规则\n                  </div>\n                  <div className=\"mt-2 flex items-center text-sm\">\n                    {config?.enabled ? (\n                      <>\n                        <CheckCircle className=\"flex-shrink-0 mr-1.5 h-5 w-5 text-green-400\" />\n                        <span className=\"text-green-600\">全局脱敏已启用</span>\n                      </>\n                    ) : (\n                      <>\n                        <XCircle className=\"flex-shrink-0 mr-1.5 h-5 w-5 text-red-400\" />\n                        <span className=\"text-red-600\">全局脱敏已禁用</span>\n                      </>\n                    )}\n                  </div>\n                </div>\n              </div>\n              <div className=\"mt-4 flex md:mt-0 md:ml-4\">\n                <button\n                  onClick={handleReload}\n                  className=\"inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500\"\n                >\n                  <RefreshCw className=\"h-4 w-4 mr-2\" />\n                  重载规则\n                </button>\n                <button\n                  onClick={handleToggleGlobal}\n                  disabled={toggling}\n                  className={`ml-3 inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white focus:outline-none focus:ring-2 focus:ring-offset-2 ${\n                    config?.enabled\n                      ? 'bg-red-600 hover:bg-red-700 focus:ring-red-500'\n                      : 'bg-green-600 hover:bg-green-700 focus:ring-green-500'\n                  } ${toggling ? 'opacity-50 cursor-not-allowed' : ''}`}\n                >\n                  {toggling ? '处理中...' : (config?.enabled ? '禁用全局脱敏' : '启用全局脱敏')}\n                </button>\n              </div>\n            </div>\n          </div>\n        </div>\n      </div>\n\n      {/* Search */}\n      <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6\">\n        <div className=\"relative max-w-md\">\n          <div className=\"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none\">\n            <Search className=\"h-5 w-5 text-gray-400\" />\n          </div>\n          <input\n            type=\"text\"\n            value={searchTerm}\n            onChange={(e) => setSearchTerm(e.target.value)}\n            placeholder=\"搜索规则...\"\n            className=\"block w-full pl-10 pr-3 py-2 border border-gray-300 rounded-md leading-5 bg-white placeholder-gray-500 focus:outline-none focus:placeholder-gray-400 focus:ring-1 focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm\"\n          />\n        </div>\n      </div>\n\n      {/* Rules List */}\n      <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n        {filteredRules.length === 0 ? (\n          <div className=\"text-center py-12\">\n            <AlertCircle className=\"mx-auto h-12 w-12 text-gray-400\" />\n            <h3 className=\"mt-2 text-sm font-medium text-gray-900\">没有找到匹配的规则</h3>\n            <p className=\"mt-1 text-sm text-gray-500\">请尝试调整搜索条件</p>\n          </div>\n        ) : (\n          <div className=\"bg-white shadow overflow-hidden sm:rounded-md\">\n            <ul className=\"divide-y divide-gray-200\">\n              {filteredRules.map((rule) => (\n                <li key={rule.id}>\n                  <div className=\"px-4 py-4 sm:px-6\">\n                    <div className=\"flex items-center justify-between\">\n                      <div className=\"flex items-center\">\n                        <div className=\"flex-shrink-0\">\n                          <div className={`inline-flex items-center justify-center h-8 w-8 rounded-full ${\n                            rule.enabled ? 'bg-green-100' : 'bg-gray-100'\n                          }`}>\n                            {rule.enabled ? (\n                              <CheckCircle className=\"h-5 w-5 text-green-600\" />\n                            ) : (\n                              <XCircle className=\"h-5 w-5 text-gray-400\" />\n                            )}\n                          </div>\n                        </div>\n                        <div className=\"ml-4\">\n                          <div className=\"flex items-center\">\n                            <div className=\"text-sm font-medium text-gray-900\">{rule.name}</div>\n                            <span className={`ml-2 inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getSeverityColor(rule.severity)}`}>\n                              {rule.severity}\n                            </span>\n                            <span className={`ml-2 inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getTypeColor(rule.type)}`}>\n                              {rule.type}\n                            </span>\n                          </div>\n                          <div className=\"mt-1 text-sm text-gray-500\">{rule.description}</div>\n                          <div className=\"mt-2 text-xs text-gray-400\">ID: {rule.id}</div>\n                        </div>\n                      </div>\n                      <div className=\"flex items-center space-x-4\">\n                        <div className=\"text-sm text-gray-500\">\n                          优先级: {rule.priority}\n                        </div>\n                        <div className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${\n                          rule.enabled ? 'bg-green-100 text-green-800' : 'bg-gray-100 text-gray-800'\n                        }`}>\n                          {rule.enabled ? '已启用' : '已禁用'}\n                        </div>\n                      </div>\n                    </div>\n\n                    <div className=\"mt-4\">\n                      <dl className=\"grid grid-cols-1 gap-x-4 gap-y-2 sm:grid-cols-2\">\n                        {rule.fieldNames && rule.fieldNames.length > 0 && (\n                          <div>\n                            <dt className=\"text-sm font-medium text-gray-500\">字段名称</dt>\n                            <dd className=\"mt-1 text-sm text-gray-900\">{rule.fieldNames.join(', ')}</dd>\n                          </div>\n                        )}\n                        {rule.pattern && (\n                          <div>\n                            <dt className=\"text-sm font-medium text-gray-500\">匹配模式</dt>\n                            <dd className=\"mt-1 text-sm text-gray-900\">\n                              <code className=\"bg-gray-100 px-2 py-1 rounded text-xs\">{rule.pattern}</code>\n                            </dd>\n                          </div>\n                        )}\n                        <div>\n                          <dt className=\"text-sm font-medium text-gray-500\">掩码值</dt>\n                          <dd className=\"mt-1 text-sm text-gray-900\">\n                            <code className=\"bg-gray-100 px-2 py-1 rounded text-xs\">{rule.maskValue}</code>\n                          </dd>\n                        </div>\n                        {rule.includeServices && rule.includeServices.length > 0 && (\n                          <div>\n                            <dt className=\"text-sm font-medium text-gray-500\">适用服务</dt>\n                            <dd className=\"mt-1 text-sm text-gray-900\">{rule.includeServices.join(', ')}</dd>\n                          </div>\n                        )}\n                      </dl>\n                    </div>\n                  </div>\n                </li>\n              ))}\n            </ul>\n          </div>\n        )}\n      </div>\n    </div>\n  );\n};\n\nexport default SimpleRulesList;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,MAAM,EAAEC,QAAQ,EAAEC,SAAS,EAAEC,WAAW,EAAEC,WAAW,EAAEC,OAAO,QAAQ,cAAc;AAC7F,SAASC,eAAe,QAAQ,iBAAiB;AAEjD,OAAOC,KAAK,MAAM,iBAAiB;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAEpC,MAAMC,eAAyB,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACtC,MAAM,CAACC,MAAM,EAAEC,SAAS,CAAC,GAAGjB,QAAQ,CAA4B,IAAI,CAAC;EACrE,MAAM,CAACkB,OAAO,EAAEC,UAAU,CAAC,GAAGnB,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAM,CAACoB,UAAU,EAAEC,aAAa,CAAC,GAAGrB,QAAQ,CAAC,EAAE,CAAC;EAChD,MAAM,CAACsB,QAAQ,EAAEC,WAAW,CAAC,GAAGvB,QAAQ,CAAC,KAAK,CAAC;EAE/C,MAAMwB,UAAU,GAAG,MAAAA,CAAA,KAAY;IAC7B,IAAI;MACFL,UAAU,CAAC,IAAI,CAAC;MAChB,MAAMM,QAAQ,GAAG,MAAMjB,eAAe,CAACkB,QAAQ,CAAC,CAAC;MACjDT,SAAS,CAACQ,QAAQ,CAAC;IACrB,CAAC,CAAC,OAAOE,KAAK,EAAE;MACdlB,KAAK,CAACkB,KAAK,CAAC,QAAQ,CAAC;MACrBC,OAAO,CAACD,KAAK,CAAC,uBAAuB,EAAEA,KAAK,CAAC;IAC/C,CAAC,SAAS;MACRR,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAED,MAAMU,YAAY,GAAG,MAAAA,CAAA,KAAY;IAC/B,IAAI;MACF,MAAMrB,eAAe,CAACsB,WAAW,CAAC,CAAC;MACnCrB,KAAK,CAACsB,OAAO,CAAC,QAAQ,CAAC;MACvB,MAAMP,UAAU,CAAC,CAAC;IACpB,CAAC,CAAC,OAAOG,KAAK,EAAE;MACdlB,KAAK,CAACkB,KAAK,CAAC,QAAQ,CAAC;MACrBC,OAAO,CAACD,KAAK,CAAC,wBAAwB,EAAEA,KAAK,CAAC;IAChD;EACF,CAAC;EAED,MAAMK,kBAAkB,GAAG,MAAAA,CAAA,KAAY;IACrC,IAAI,CAAChB,MAAM,EAAE;IAEb,IAAI;MACFO,WAAW,CAAC,IAAI,CAAC;MACjB,MAAMU,UAAU,GAAG,CAACjB,MAAM,CAACkB,OAAO;MAClC,MAAMT,QAAQ,GAAG,MAAMjB,eAAe,CAAC2B,kBAAkB,CAACF,UAAU,CAAC;MAErEhB,SAAS,CAACmB,IAAI,IAAIA,IAAI,GAAG;QAAE,GAAGA,IAAI;QAAEF,OAAO,EAAET,QAAQ,CAACS;MAAQ,CAAC,GAAG,IAAI,CAAC;MACvEzB,KAAK,CAACsB,OAAO,CAACE,UAAU,GAAG,SAAS,GAAG,SAAS,CAAC;IACnD,CAAC,CAAC,OAAON,KAAK,EAAE;MACdlB,KAAK,CAACkB,KAAK,CAAC,UAAU,CAAC;MACvBC,OAAO,CAACD,KAAK,CAAC,+BAA+B,EAAEA,KAAK,CAAC;IACvD,CAAC,SAAS;MACRJ,WAAW,CAAC,KAAK,CAAC;IACpB;EACF,CAAC;EAEDtB,SAAS,CAAC,MAAM;IACduB,UAAU,CAAC,CAAC;EACd,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMa,aAAa,GAAG,CAAArB,MAAM,aAANA,MAAM,uBAANA,MAAM,CAAEsB,KAAK,CAACC,MAAM,CAACC,IAAI,IAC7CA,IAAI,CAACC,IAAI,CAACC,WAAW,CAAC,CAAC,CAACC,QAAQ,CAACvB,UAAU,CAACsB,WAAW,CAAC,CAAC,CAAC,IAC1DF,IAAI,CAACI,WAAW,CAACF,WAAW,CAAC,CAAC,CAACC,QAAQ,CAACvB,UAAU,CAACsB,WAAW,CAAC,CAAC,CAAC,IACjEF,IAAI,CAACK,EAAE,CAACH,WAAW,CAAC,CAAC,CAACC,QAAQ,CAACvB,UAAU,CAACsB,WAAW,CAAC,CAAC,CACzD,CAAC,KAAI,EAAE;EAEP,MAAMI,gBAAgB,GAAIC,QAAgB,IAAK;IAC7C,QAAQA,QAAQ;MACd,KAAK,UAAU;QAAE,OAAO,yBAAyB;MACjD,KAAK,MAAM;QAAE,OAAO,+BAA+B;MACnD,KAAK,QAAQ;QAAE,OAAO,+BAA+B;MACrD,KAAK,KAAK;QAAE,OAAO,6BAA6B;MAChD;QAAS,OAAO,2BAA2B;IAC7C;EACF,CAAC;EAED,MAAMC,YAAY,GAAIC,IAAY,IAAK;IACrC,QAAQA,IAAI;MACV,KAAK,YAAY;QAAE,OAAO,2BAA2B;MACrD,KAAK,SAAS;QAAE,OAAO,+BAA+B;MACtD,KAAK,cAAc;QAAE,OAAO,+BAA+B;MAC3D;QAAS,OAAO,2BAA2B;IAC7C;EACF,CAAC;EAED,IAAI/B,OAAO,EAAE;IACX,oBACEP,OAAA;MAAKuC,SAAS,EAAC,0DAA0D;MAAAC,QAAA,eACvExC,OAAA;QAAKuC,SAAS,EAAC,aAAa;QAAAC,QAAA,gBAC1BxC,OAAA;UAAKuC,SAAS,EAAC;QAAwE;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eAC9F5C,OAAA;UAAGuC,SAAS,EAAC,oBAAoB;UAAAC,QAAA,EAAC;QAAM;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACzC;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAEV;EAEA,oBACE5C,OAAA;IAAKuC,SAAS,EAAC,yBAAyB;IAAAC,QAAA,gBAEtCxC,OAAA;MAAKuC,SAAS,EAAC,iBAAiB;MAAAC,QAAA,eAC9BxC,OAAA;QAAKuC,SAAS,EAAC,wCAAwC;QAAAC,QAAA,eACrDxC,OAAA;UAAKuC,SAAS,EAAC,MAAM;UAAAC,QAAA,eACnBxC,OAAA;YAAKuC,SAAS,EAAC,4CAA4C;YAAAC,QAAA,gBACzDxC,OAAA;cAAKuC,SAAS,EAAC,gBAAgB;cAAAC,QAAA,gBAC7BxC,OAAA;gBAAIuC,SAAS,EAAC,oEAAoE;gBAAAC,QAAA,EAAC;cAEnF;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACL5C,OAAA;gBAAKuC,SAAS,EAAC,kEAAkE;gBAAAC,QAAA,gBAC/ExC,OAAA;kBAAKuC,SAAS,EAAC,8CAA8C;kBAAAC,QAAA,gBAC3DxC,OAAA,CAACR,QAAQ;oBAAC+C,SAAS,EAAC;kBAA4C;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,WACjE,EAAClB,aAAa,CAACmB,MAAM,EAAC,qBAC1B;gBAAA;kBAAAJ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC,eACN5C,OAAA;kBAAKuC,SAAS,EAAC,gCAAgC;kBAAAC,QAAA,EAC5CnC,MAAM,aAANA,MAAM,eAANA,MAAM,CAAEkB,OAAO,gBACdvB,OAAA,CAAAE,SAAA;oBAAAsC,QAAA,gBACExC,OAAA,CAACL,WAAW;sBAAC4C,SAAS,EAAC;oBAA6C;sBAAAE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE,CAAC,eACvE5C,OAAA;sBAAMuC,SAAS,EAAC,gBAAgB;sBAAAC,QAAA,EAAC;oBAAO;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC;kBAAA,eAC/C,CAAC,gBAEH5C,OAAA,CAAAE,SAAA;oBAAAsC,QAAA,gBACExC,OAAA,CAACJ,OAAO;sBAAC2C,SAAS,EAAC;oBAA2C;sBAAAE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE,CAAC,eACjE5C,OAAA;sBAAMuC,SAAS,EAAC,cAAc;sBAAAC,QAAA,EAAC;oBAAO;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC;kBAAA,eAC7C;gBACH;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACE,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eACN5C,OAAA;cAAKuC,SAAS,EAAC,2BAA2B;cAAAC,QAAA,gBACxCxC,OAAA;gBACE8C,OAAO,EAAE5B,YAAa;gBACtBqB,SAAS,EAAC,sNAAsN;gBAAAC,QAAA,gBAEhOxC,OAAA,CAACP,SAAS;kBAAC8C,SAAS,EAAC;gBAAc;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,4BAExC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eACT5C,OAAA;gBACE8C,OAAO,EAAEzB,kBAAmB;gBAC5B0B,QAAQ,EAAEpC,QAAS;gBACnB4B,SAAS,EAAE,6KACTlC,MAAM,aAANA,MAAM,eAANA,MAAM,CAAEkB,OAAO,GACX,gDAAgD,GAChD,sDAAsD,IACxDZ,QAAQ,GAAG,+BAA+B,GAAG,EAAE,EAAG;gBAAA6B,QAAA,EAErD7B,QAAQ,GAAG,QAAQ,GAAIN,MAAM,aAANA,MAAM,eAANA,MAAM,CAAEkB,OAAO,GAAG,QAAQ,GAAG;cAAS;gBAAAkB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACxD,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGN5C,OAAA;MAAKuC,SAAS,EAAC,6CAA6C;MAAAC,QAAA,eAC1DxC,OAAA;QAAKuC,SAAS,EAAC,mBAAmB;QAAAC,QAAA,gBAChCxC,OAAA;UAAKuC,SAAS,EAAC,sEAAsE;UAAAC,QAAA,eACnFxC,OAAA,CAACT,MAAM;YAACgD,SAAS,EAAC;UAAuB;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACzC,CAAC,eACN5C,OAAA;UACEsC,IAAI,EAAC,MAAM;UACXU,KAAK,EAAEvC,UAAW;UAClBwC,QAAQ,EAAGC,CAAC,IAAKxC,aAAa,CAACwC,CAAC,CAACC,MAAM,CAACH,KAAK,CAAE;UAC/CI,WAAW,EAAC,6BAAS;UACrBb,SAAS,EAAC;QAA4N;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACvO,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGN5C,OAAA;MAAKuC,SAAS,EAAC,wCAAwC;MAAAC,QAAA,EACpDd,aAAa,CAACmB,MAAM,KAAK,CAAC,gBACzB7C,OAAA;QAAKuC,SAAS,EAAC,mBAAmB;QAAAC,QAAA,gBAChCxC,OAAA,CAACN,WAAW;UAAC6C,SAAS,EAAC;QAAiC;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eAC3D5C,OAAA;UAAIuC,SAAS,EAAC,wCAAwC;UAAAC,QAAA,EAAC;QAAS;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACrE5C,OAAA;UAAGuC,SAAS,EAAC,4BAA4B;UAAAC,QAAA,EAAC;QAAS;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACpD,CAAC,gBAEN5C,OAAA;QAAKuC,SAAS,EAAC,+CAA+C;QAAAC,QAAA,eAC5DxC,OAAA;UAAIuC,SAAS,EAAC,0BAA0B;UAAAC,QAAA,EACrCd,aAAa,CAAC2B,GAAG,CAAExB,IAAI,iBACtB7B,OAAA;YAAAwC,QAAA,eACExC,OAAA;cAAKuC,SAAS,EAAC,mBAAmB;cAAAC,QAAA,gBAChCxC,OAAA;gBAAKuC,SAAS,EAAC,mCAAmC;gBAAAC,QAAA,gBAChDxC,OAAA;kBAAKuC,SAAS,EAAC,mBAAmB;kBAAAC,QAAA,gBAChCxC,OAAA;oBAAKuC,SAAS,EAAC,eAAe;oBAAAC,QAAA,eAC5BxC,OAAA;sBAAKuC,SAAS,EAAE,gEACdV,IAAI,CAACN,OAAO,GAAG,cAAc,GAAG,aAAa,EAC5C;sBAAAiB,QAAA,EACAX,IAAI,CAACN,OAAO,gBACXvB,OAAA,CAACL,WAAW;wBAAC4C,SAAS,EAAC;sBAAwB;wBAAAE,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE,CAAC,gBAElD5C,OAAA,CAACJ,OAAO;wBAAC2C,SAAS,EAAC;sBAAuB;wBAAAE,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE;oBAC7C;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACE;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC,eACN5C,OAAA;oBAAKuC,SAAS,EAAC,MAAM;oBAAAC,QAAA,gBACnBxC,OAAA;sBAAKuC,SAAS,EAAC,mBAAmB;sBAAAC,QAAA,gBAChCxC,OAAA;wBAAKuC,SAAS,EAAC,mCAAmC;wBAAAC,QAAA,EAAEX,IAAI,CAACC;sBAAI;wBAAAW,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAM,CAAC,eACpE5C,OAAA;wBAAMuC,SAAS,EAAE,gFAAgFJ,gBAAgB,CAACN,IAAI,CAACO,QAAQ,CAAC,EAAG;wBAAAI,QAAA,EAChIX,IAAI,CAACO;sBAAQ;wBAAAK,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACV,CAAC,eACP5C,OAAA;wBAAMuC,SAAS,EAAE,gFAAgFF,YAAY,CAACR,IAAI,CAACS,IAAI,CAAC,EAAG;wBAAAE,QAAA,EACxHX,IAAI,CAACS;sBAAI;wBAAAG,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACN,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACJ,CAAC,eACN5C,OAAA;sBAAKuC,SAAS,EAAC,4BAA4B;sBAAAC,QAAA,EAAEX,IAAI,CAACI;oBAAW;sBAAAQ,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC,eACpE5C,OAAA;sBAAKuC,SAAS,EAAC,4BAA4B;sBAAAC,QAAA,GAAC,MAAI,EAACX,IAAI,CAACK,EAAE;oBAAA;sBAAAO,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC5D,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC,eACN5C,OAAA;kBAAKuC,SAAS,EAAC,6BAA6B;kBAAAC,QAAA,gBAC1CxC,OAAA;oBAAKuC,SAAS,EAAC,uBAAuB;oBAAAC,QAAA,GAAC,sBAChC,EAACX,IAAI,CAACyB,QAAQ;kBAAA;oBAAAb,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAChB,CAAC,eACN5C,OAAA;oBAAKuC,SAAS,EAAE,2EACdV,IAAI,CAACN,OAAO,GAAG,6BAA6B,GAAG,2BAA2B,EACzE;oBAAAiB,QAAA,EACAX,IAAI,CAACN,OAAO,GAAG,KAAK,GAAG;kBAAK;oBAAAkB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC1B,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eAEN5C,OAAA;gBAAKuC,SAAS,EAAC,MAAM;gBAAAC,QAAA,eACnBxC,OAAA;kBAAIuC,SAAS,EAAC,iDAAiD;kBAAAC,QAAA,GAC5DX,IAAI,CAAC0B,UAAU,IAAI1B,IAAI,CAAC0B,UAAU,CAACV,MAAM,GAAG,CAAC,iBAC5C7C,OAAA;oBAAAwC,QAAA,gBACExC,OAAA;sBAAIuC,SAAS,EAAC,mCAAmC;sBAAAC,QAAA,EAAC;oBAAI;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAI,CAAC,eAC3D5C,OAAA;sBAAIuC,SAAS,EAAC,4BAA4B;sBAAAC,QAAA,EAAEX,IAAI,CAAC0B,UAAU,CAACC,IAAI,CAAC,IAAI;oBAAC;sBAAAf,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAK,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACzE,CACN,EACAf,IAAI,CAAC4B,OAAO,iBACXzD,OAAA;oBAAAwC,QAAA,gBACExC,OAAA;sBAAIuC,SAAS,EAAC,mCAAmC;sBAAAC,QAAA,EAAC;oBAAI;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAI,CAAC,eAC3D5C,OAAA;sBAAIuC,SAAS,EAAC,4BAA4B;sBAAAC,QAAA,eACxCxC,OAAA;wBAAMuC,SAAS,EAAC,uCAAuC;wBAAAC,QAAA,EAAEX,IAAI,CAAC4B;sBAAO;wBAAAhB,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAO;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAC3E,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACF,CACN,eACD5C,OAAA;oBAAAwC,QAAA,gBACExC,OAAA;sBAAIuC,SAAS,EAAC,mCAAmC;sBAAAC,QAAA,EAAC;oBAAG;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAI,CAAC,eAC1D5C,OAAA;sBAAIuC,SAAS,EAAC,4BAA4B;sBAAAC,QAAA,eACxCxC,OAAA;wBAAMuC,SAAS,EAAC,uCAAuC;wBAAAC,QAAA,EAAEX,IAAI,CAAC6B;sBAAS;wBAAAjB,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAO;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAC7E,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACF,CAAC,EACLf,IAAI,CAAC8B,eAAe,IAAI9B,IAAI,CAAC8B,eAAe,CAACd,MAAM,GAAG,CAAC,iBACtD7C,OAAA;oBAAAwC,QAAA,gBACExC,OAAA;sBAAIuC,SAAS,EAAC,mCAAmC;sBAAAC,QAAA,EAAC;oBAAI;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAI,CAAC,eAC3D5C,OAAA;sBAAIuC,SAAS,EAAC,4BAA4B;sBAAAC,QAAA,EAAEX,IAAI,CAAC8B,eAAe,CAACH,IAAI,CAAC,IAAI;oBAAC;sBAAAf,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAK,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC9E,CACN;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACC;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACF,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH;UAAC,GAvECf,IAAI,CAACK,EAAE;YAAAO,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAwEZ,CACL;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACA;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACF;IACN;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAACxC,EAAA,CA5PID,eAAyB;AAAAyD,EAAA,GAAzBzD,eAAyB;AA8P/B,eAAeA,eAAe;AAAC,IAAAyD,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}