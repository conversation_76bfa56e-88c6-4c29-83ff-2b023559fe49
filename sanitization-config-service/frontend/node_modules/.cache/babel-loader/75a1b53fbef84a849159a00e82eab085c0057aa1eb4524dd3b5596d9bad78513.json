{"ast": null, "code": "import axios from 'axios';\nconst API_BASE_URL = process.env.REACT_APP_API_URL || 'http://localhost:8082';\nconst api = axios.create({\n  baseURL: API_BASE_URL,\n  timeout: 10000,\n  headers: {\n    'Content-Type': 'application/json'\n  }\n});\n\n// Request interceptor for adding auth headers if needed\napi.interceptors.request.use(config => {\n  // Add auth token if available\n  const token = localStorage.getItem('auth_token');\n  if (token) {\n    config.headers.Authorization = `Bearer ${token}`;\n  }\n  return config;\n}, error => {\n  return Promise.reject(error);\n});\n\n// Response interceptor for handling errors\napi.interceptors.response.use(response => response, error => {\n  var _error$response;\n  if (((_error$response = error.response) === null || _error$response === void 0 ? void 0 : _error$response.status) === 401) {\n    // Handle unauthorized access\n    localStorage.removeItem('auth_token');\n    window.location.href = '/login';\n  }\n  return Promise.reject(error);\n});\nexport const sanitizationApi = {\n  // Get all sanitization rules\n  getRules: async serviceName => {\n    const headers = serviceName ? {\n      'X-Service-Name': serviceName\n    } : {};\n    const response = await api.get('/api/sanitization/rules', {\n      headers\n    });\n    return response.data;\n  },\n  // Reload rules from configuration file\n  reloadRules: async () => {\n    const response = await api.post('/api/sanitization/rules/reload');\n    return response.data;\n  },\n  // Toggle global sanitization switch\n  toggleGlobalSwitch: async enabled => {\n    const response = await api.post('/api/sanitization/toggle', {\n      enabled\n    });\n    return response.data;\n  },\n  // Get service health\n  getHealth: async () => {\n    const response = await api.get('/health');\n    return response.data;\n  },\n  // Get service metrics\n  getMetrics: async () => {\n    const response = await api.get('/metrics');\n    return response.data;\n  }\n};\nexport default api;", "map": {"version": 3, "names": ["axios", "API_BASE_URL", "process", "env", "REACT_APP_API_URL", "api", "create", "baseURL", "timeout", "headers", "interceptors", "request", "use", "config", "token", "localStorage", "getItem", "Authorization", "error", "Promise", "reject", "response", "_error$response", "status", "removeItem", "window", "location", "href", "sanitizationApi", "getRules", "serviceName", "get", "data", "reloadRules", "post", "toggleGlobalSwitch", "enabled", "getHealth", "getMetrics"], "sources": ["/Users/<USER>/dev/work/lab/javaagent/sanitization-config-service/frontend/src/services/api.ts"], "sourcesContent": ["import axios from 'axios';\nimport { SanitizationConfig, HealthResponse, MetricsResponse, ApiResponse } from '../types';\n\nconst API_BASE_URL = process.env.REACT_APP_API_URL || 'http://localhost:8082';\n\nconst api = axios.create({\n  baseURL: API_BASE_URL,\n  timeout: 10000,\n  headers: {\n    'Content-Type': 'application/json',\n  },\n});\n\n// Request interceptor for adding auth headers if needed\napi.interceptors.request.use(\n  (config) => {\n    // Add auth token if available\n    const token = localStorage.getItem('auth_token');\n    if (token) {\n      config.headers.Authorization = `Bearer ${token}`;\n    }\n    return config;\n  },\n  (error) => {\n    return Promise.reject(error);\n  }\n);\n\n// Response interceptor for handling errors\napi.interceptors.response.use(\n  (response) => response,\n  (error) => {\n    if (error.response?.status === 401) {\n      // Handle unauthorized access\n      localStorage.removeItem('auth_token');\n      window.location.href = '/login';\n    }\n    return Promise.reject(error);\n  }\n);\n\nexport const sanitizationApi = {\n  // Get all sanitization rules\n  getRules: async (serviceName?: string): Promise<SanitizationConfig> => {\n    const headers = serviceName ? { 'X-Service-Name': serviceName } : {};\n    const response = await api.get('/api/sanitization/rules', { headers });\n    return response.data;\n  },\n\n  // Reload rules from configuration file\n  reloadRules: async (): Promise<ApiResponse<any>> => {\n    const response = await api.post('/api/sanitization/rules/reload');\n    return response.data;\n  },\n\n  // Toggle global sanitization switch\n  toggleGlobalSwitch: async (enabled: boolean): Promise<{ enabled: boolean; message: string; timestamp: number }> => {\n    const response = await api.post('/api/sanitization/toggle', { enabled });\n    return response.data;\n  },\n\n  // Get service health\n  getHealth: async (): Promise<HealthResponse> => {\n    const response = await api.get('/health');\n    return response.data;\n  },\n\n  // Get service metrics\n  getMetrics: async (): Promise<MetricsResponse> => {\n    const response = await api.get('/metrics');\n    return response.data;\n  },\n};\n\nexport default api;\n"], "mappings": "AAAA,OAAOA,KAAK,MAAM,OAAO;AAGzB,MAAMC,YAAY,GAAGC,OAAO,CAACC,GAAG,CAACC,iBAAiB,IAAI,uBAAuB;AAE7E,MAAMC,GAAG,GAAGL,KAAK,CAACM,MAAM,CAAC;EACvBC,OAAO,EAAEN,YAAY;EACrBO,OAAO,EAAE,KAAK;EACdC,OAAO,EAAE;IACP,cAAc,EAAE;EAClB;AACF,CAAC,CAAC;;AAEF;AACAJ,GAAG,CAACK,YAAY,CAACC,OAAO,CAACC,GAAG,CACzBC,MAAM,IAAK;EACV;EACA,MAAMC,KAAK,GAAGC,YAAY,CAACC,OAAO,CAAC,YAAY,CAAC;EAChD,IAAIF,KAAK,EAAE;IACTD,MAAM,CAACJ,OAAO,CAACQ,aAAa,GAAG,UAAUH,KAAK,EAAE;EAClD;EACA,OAAOD,MAAM;AACf,CAAC,EACAK,KAAK,IAAK;EACT,OAAOC,OAAO,CAACC,MAAM,CAACF,KAAK,CAAC;AAC9B,CACF,CAAC;;AAED;AACAb,GAAG,CAACK,YAAY,CAACW,QAAQ,CAACT,GAAG,CAC1BS,QAAQ,IAAKA,QAAQ,EACrBH,KAAK,IAAK;EAAA,IAAAI,eAAA;EACT,IAAI,EAAAA,eAAA,GAAAJ,KAAK,CAACG,QAAQ,cAAAC,eAAA,uBAAdA,eAAA,CAAgBC,MAAM,MAAK,GAAG,EAAE;IAClC;IACAR,YAAY,CAACS,UAAU,CAAC,YAAY,CAAC;IACrCC,MAAM,CAACC,QAAQ,CAACC,IAAI,GAAG,QAAQ;EACjC;EACA,OAAOR,OAAO,CAACC,MAAM,CAACF,KAAK,CAAC;AAC9B,CACF,CAAC;AAED,OAAO,MAAMU,eAAe,GAAG;EAC7B;EACAC,QAAQ,EAAE,MAAOC,WAAoB,IAAkC;IACrE,MAAMrB,OAAO,GAAGqB,WAAW,GAAG;MAAE,gBAAgB,EAAEA;IAAY,CAAC,GAAG,CAAC,CAAC;IACpE,MAAMT,QAAQ,GAAG,MAAMhB,GAAG,CAAC0B,GAAG,CAAC,yBAAyB,EAAE;MAAEtB;IAAQ,CAAC,CAAC;IACtE,OAAOY,QAAQ,CAACW,IAAI;EACtB,CAAC;EAED;EACAC,WAAW,EAAE,MAAAA,CAAA,KAAuC;IAClD,MAAMZ,QAAQ,GAAG,MAAMhB,GAAG,CAAC6B,IAAI,CAAC,gCAAgC,CAAC;IACjE,OAAOb,QAAQ,CAACW,IAAI;EACtB,CAAC;EAED;EACAG,kBAAkB,EAAE,MAAOC,OAAgB,IAAwE;IACjH,MAAMf,QAAQ,GAAG,MAAMhB,GAAG,CAAC6B,IAAI,CAAC,0BAA0B,EAAE;MAAEE;IAAQ,CAAC,CAAC;IACxE,OAAOf,QAAQ,CAACW,IAAI;EACtB,CAAC;EAED;EACAK,SAAS,EAAE,MAAAA,CAAA,KAAqC;IAC9C,MAAMhB,QAAQ,GAAG,MAAMhB,GAAG,CAAC0B,GAAG,CAAC,SAAS,CAAC;IACzC,OAAOV,QAAQ,CAACW,IAAI;EACtB,CAAC;EAED;EACAM,UAAU,EAAE,MAAAA,CAAA,KAAsC;IAChD,MAAMjB,QAAQ,GAAG,MAAMhB,GAAG,CAAC0B,GAAG,CAAC,UAAU,CAAC;IAC1C,OAAOV,QAAQ,CAACW,IAAI;EACtB;AACF,CAAC;AAED,eAAe3B,GAAG", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}