{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"M12 19h8\",\n  key: \"baeox8\"\n}], [\"path\", {\n  d: \"m4 17 6-6-6-6\",\n  key: \"1yngyt\"\n}]];\nconst Terminal = createLucideIcon(\"terminal\", __iconNode);\nexport { __iconNode, Terminal as default };\n//# sourceMappingURL=terminal.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}