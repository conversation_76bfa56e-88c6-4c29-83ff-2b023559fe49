{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"polyline\", {\n  points: \"22 12 16 12 14 15 10 15 8 12 2 12\",\n  key: \"o97t9d\"\n}], [\"path\", {\n  d: \"M5.45 5.11 2 12v6a2 2 0 0 0 2 2h16a2 2 0 0 0 2-2v-6l-3.45-6.89A2 2 0 0 0 16.76 4H7.24a2 2 0 0 0-1.79 1.11z\",\n  key: \"oot6mr\"\n}]];\nconst Inbox = createLucideIcon(\"inbox\", __iconNode);\nexport { __iconNode, Inbox as default };\n//# sourceMappingURL=inbox.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}