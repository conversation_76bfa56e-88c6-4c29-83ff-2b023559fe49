{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"M17.97 9.304A8 8 0 0 0 2 10c0 4.69 4.887 9.562 7.022 11.468\",\n  key: \"1fahp3\"\n}], [\"path\", {\n  d: \"M21.378 16.626a1 1 0 0 0-3.004-3.004l-4.01 4.012a2 2 0 0 0-.506.854l-.837 2.87a.5.5 0 0 0 .62.62l2.87-.837a2 2 0 0 0 .854-.506z\",\n  key: \"1817ys\"\n}], [\"circle\", {\n  cx: \"10\",\n  cy: \"10\",\n  r: \"3\",\n  key: \"1ns7v1\"\n}]];\nconst LocationEdit = createLucideIcon(\"location-edit\", __iconNode);\nexport { __iconNode, LocationEdit as default };\n//# sourceMappingURL=location-edit.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}