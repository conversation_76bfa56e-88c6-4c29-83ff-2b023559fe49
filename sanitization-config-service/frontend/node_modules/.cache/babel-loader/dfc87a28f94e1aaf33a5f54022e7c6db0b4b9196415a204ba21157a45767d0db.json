{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"rect\", {\n  width: \"16\",\n  height: \"13\",\n  x: \"6\",\n  y: \"4\",\n  rx: \"2\",\n  key: \"1drq3f\"\n}], [\"path\", {\n  d: \"m22 7-7.1 3.78c-.57.3-1.23.3-1.8 0L6 7\",\n  key: \"xn252p\"\n}], [\"path\", {\n  d: \"M2 8v11c0 1.1.9 2 2 2h14\",\n  key: \"n13cji\"\n}]];\nconst Mails = createLucideIcon(\"mails\", __iconNode);\nexport { __iconNode, Mails as default };\n//# sourceMappingURL=mails.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}