{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"line\", {\n  x1: \"12\",\n  x2: \"12\",\n  y1: \"20\",\n  y2: \"10\",\n  key: \"1vz5eb\"\n}], [\"line\", {\n  x1: \"18\",\n  x2: \"18\",\n  y1: \"20\",\n  y2: \"4\",\n  key: \"cun8e5\"\n}], [\"line\", {\n  x1: \"6\",\n  x2: \"6\",\n  y1: \"20\",\n  y2: \"16\",\n  key: \"hq0ia6\"\n}]];\nconst ChartNoAxesColumnIncreasing = createLucideIcon(\"chart-no-axes-column-increasing\", __iconNode);\nexport { __iconNode, ChartNoAxesColumnIncreasing as default };\n//# sourceMappingURL=chart-no-axes-column-increasing.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}