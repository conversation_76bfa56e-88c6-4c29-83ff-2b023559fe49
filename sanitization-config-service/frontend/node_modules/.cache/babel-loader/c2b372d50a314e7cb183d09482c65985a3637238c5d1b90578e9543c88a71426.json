{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"rect\", {\n  width: \"7\",\n  height: \"9\",\n  x: \"3\",\n  y: \"3\",\n  rx: \"1\",\n  key: \"10lvy0\"\n}], [\"rect\", {\n  width: \"7\",\n  height: \"5\",\n  x: \"14\",\n  y: \"3\",\n  rx: \"1\",\n  key: \"16une8\"\n}], [\"rect\", {\n  width: \"7\",\n  height: \"9\",\n  x: \"14\",\n  y: \"12\",\n  rx: \"1\",\n  key: \"1hutg5\"\n}], [\"rect\", {\n  width: \"7\",\n  height: \"5\",\n  x: \"3\",\n  y: \"16\",\n  rx: \"1\",\n  key: \"ldoo1y\"\n}]];\nconst LayoutDashboard = createLucideIcon(\"layout-dashboard\", __iconNode);\nexport { __iconNode, LayoutDashboard as default };\n//# sourceMappingURL=layout-dashboard.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}