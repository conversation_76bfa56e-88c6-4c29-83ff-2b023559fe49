{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"M8.3 10a.7.7 0 0 1-.626-1.079L11.4 3a.7.7 0 0 1 1.198-.043L16.3 8.9a.7.7 0 0 1-.572 1.1Z\",\n  key: \"1bo67w\"\n}], [\"rect\", {\n  x: \"3\",\n  y: \"14\",\n  width: \"7\",\n  height: \"7\",\n  rx: \"1\",\n  key: \"1bkyp8\"\n}], [\"circle\", {\n  cx: \"17.5\",\n  cy: \"17.5\",\n  r: \"3.5\",\n  key: \"w3z12y\"\n}]];\nconst Shapes = createLucideIcon(\"shapes\", __iconNode);\nexport { __iconNode, Shapes as default };\n//# sourceMappingURL=shapes.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}