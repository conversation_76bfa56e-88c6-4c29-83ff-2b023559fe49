{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"M15 22a1 1 0 0 1-1-1v-4a1 1 0 0 1 .445-.832l3-2a1 1 0 0 1 1.11 0l3 2A1 1 0 0 1 22 17v4a1 1 0 0 1-1 1z\",\n  key: \"1p1rcz\"\n}], [\"path\", {\n  d: \"M18 10a8 8 0 0 0-16 0c0 4.993 5.539 10.193 7.399 11.799a1 1 0 0 0 .601.2\",\n  key: \"mcbcs9\"\n}], [\"path\", {\n  d: \"M18 22v-3\",\n  key: \"1t1ugv\"\n}], [\"circle\", {\n  cx: \"10\",\n  cy: \"10\",\n  r: \"3\",\n  key: \"1ns7v1\"\n}]];\nconst MapPinHouse = createLucideIcon(\"map-pin-house\", __iconNode);\nexport { __iconNode, MapPinHouse as default };\n//# sourceMappingURL=map-pin-house.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}