{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"M16 10a4 4 0 0 1-8 0\",\n  key: \"1ltviw\"\n}], [\"path\", {\n  d: \"M3.103 6.034h17.794\",\n  key: \"awc11p\"\n}], [\"path\", {\n  d: \"M3.4 5.467a2 2 0 0 0-.4 1.2V20a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2V6.667a2 2 0 0 0-.4-1.2l-2-2.667A2 2 0 0 0 17 2H7a2 2 0 0 0-1.6.8z\",\n  key: \"o988cm\"\n}]];\nconst ShoppingBag = createLucideIcon(\"shopping-bag\", __iconNode);\nexport { __iconNode, ShoppingBag as default };\n//# sourceMappingURL=shopping-bag.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}