{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"M12 19v3\",\n  key: \"npa21l\"\n}], [\"path\", {\n  d: \"M19 10v2a7 7 0 0 1-14 0v-2\",\n  key: \"1vc78b\"\n}], [\"rect\", {\n  x: \"9\",\n  y: \"2\",\n  width: \"6\",\n  height: \"13\",\n  rx: \"3\",\n  key: \"s6n7sd\"\n}]];\nconst Mic = createLucideIcon(\"mic\", __iconNode);\nexport { __iconNode, Mic as default };\n//# sourceMappingURL=mic.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}