{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"m18.84 12.25 1.72-1.71h-.02a5.004 5.004 0 0 0-.12-7.07 5.006 5.006 0 0 0-6.95 0l-1.72 1.71\",\n  key: \"yqzxt4\"\n}], [\"path\", {\n  d: \"m5.17 11.75-1.71 1.71a5.004 5.004 0 0 0 .12 7.07 5.006 5.006 0 0 0 6.95 0l1.71-1.71\",\n  key: \"4qinb0\"\n}], [\"line\", {\n  x1: \"8\",\n  x2: \"8\",\n  y1: \"2\",\n  y2: \"5\",\n  key: \"1041cp\"\n}], [\"line\", {\n  x1: \"2\",\n  x2: \"5\",\n  y1: \"8\",\n  y2: \"8\",\n  key: \"14m1p5\"\n}], [\"line\", {\n  x1: \"16\",\n  x2: \"16\",\n  y1: \"19\",\n  y2: \"22\",\n  key: \"rzdirn\"\n}], [\"line\", {\n  x1: \"19\",\n  x2: \"22\",\n  y1: \"16\",\n  y2: \"16\",\n  key: \"ox905f\"\n}]];\nconst Unlink = createLucideIcon(\"unlink\", __iconNode);\nexport { __iconNode, Unlink as default };\n//# sourceMappingURL=unlink.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}