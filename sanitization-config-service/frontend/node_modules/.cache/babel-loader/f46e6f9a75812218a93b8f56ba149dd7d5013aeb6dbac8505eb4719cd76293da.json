{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"M10 22v-6.57\",\n  key: \"1wmca3\"\n}], [\"path\", {\n  d: \"M12 11h.01\",\n  key: \"z322tv\"\n}], [\"path\", {\n  d: \"M12 7h.01\",\n  key: \"1ivr5q\"\n}], [\"path\", {\n  d: \"M14 15.43V22\",\n  key: \"1q2vjd\"\n}], [\"path\", {\n  d: \"M15 16a5 5 0 0 0-6 0\",\n  key: \"o9wqvi\"\n}], [\"path\", {\n  d: \"M16 11h.01\",\n  key: \"xkw8gn\"\n}], [\"path\", {\n  d: \"M16 7h.01\",\n  key: \"1kdx03\"\n}], [\"path\", {\n  d: \"M8 11h.01\",\n  key: \"1dfujw\"\n}], [\"path\", {\n  d: \"M8 7h.01\",\n  key: \"1vti4s\"\n}], [\"rect\", {\n  x: \"4\",\n  y: \"2\",\n  width: \"16\",\n  height: \"20\",\n  rx: \"2\",\n  key: \"1uxh74\"\n}]];\nconst Hotel = createLucideIcon(\"hotel\", __iconNode);\nexport { __iconNode, Hotel as default };\n//# sourceMappingURL=hotel.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}