{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"circle\", {\n  cx: \"12\",\n  cy: \"12\",\n  r: \"4\",\n  key: \"4exip2\"\n}], [\"path\", {\n  d: \"M12 2v2\",\n  key: \"tus03m\"\n}], [\"path\", {\n  d: \"M12 20v2\",\n  key: \"1lh1kg\"\n}], [\"path\", {\n  d: \"m4.93 4.93 1.41 1.41\",\n  key: \"149t6j\"\n}], [\"path\", {\n  d: \"m17.66 17.66 1.41 1.41\",\n  key: \"ptbguv\"\n}], [\"path\", {\n  d: \"M2 12h2\",\n  key: \"1t8f8n\"\n}], [\"path\", {\n  d: \"M20 12h2\",\n  key: \"1q8mjw\"\n}], [\"path\", {\n  d: \"m6.34 17.66-1.41 1.41\",\n  key: \"1m8zz5\"\n}], [\"path\", {\n  d: \"m19.07 4.93-1.41 1.41\",\n  key: \"1shlcs\"\n}]];\nconst Sun = createLucideIcon(\"sun\", __iconNode);\nexport { __iconNode, Sun as default };\n//# sourceMappingURL=sun.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}