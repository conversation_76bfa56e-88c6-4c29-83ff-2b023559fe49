{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"M12 20v-6\",\n  key: \"1rm09r\"\n}], [\"path\", {\n  d: \"M19.656 14H22\",\n  key: \"170xzr\"\n}], [\"path\", {\n  d: \"M2 14h12\",\n  key: \"d8icqz\"\n}], [\"path\", {\n  d: \"m2 2 20 20\",\n  key: \"1ooewy\"\n}], [\"path\", {\n  d: \"M20 20H4a2 2 0 0 1-2-2V6a2 2 0 0 1 2-2\",\n  key: \"s23sx2\"\n}], [\"path\", {\n  d: \"M9.656 4H20a2 2 0 0 1 2 2v10.344\",\n  key: \"ovjcvl\"\n}]];\nconst TouchpadOff = createLucideIcon(\"touchpad-off\", __iconNode);\nexport { __iconNode, TouchpadOff as default };\n//# sourceMappingURL=touchpad-off.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}