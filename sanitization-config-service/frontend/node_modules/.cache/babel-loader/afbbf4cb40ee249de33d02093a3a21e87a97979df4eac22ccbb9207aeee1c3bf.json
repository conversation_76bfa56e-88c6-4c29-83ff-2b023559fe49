{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"M16 8q6 0 6-6-6 0-6 6\",\n  key: \"qsyyc4\"\n}], [\"path\", {\n  d: \"M17.41 3.59a10 10 0 1 0 3 3\",\n  key: \"41m9h7\"\n}], [\"path\", {\n  d: \"M2 2a26.6 26.6 0 0 1 10 20c.9-6.82 1.5-9.5 4-14\",\n  key: \"qiv7li\"\n}]];\nconst Vegan = createLucideIcon(\"vegan\", __iconNode);\nexport { __iconNode, Vegan as default };\n//# sourceMappingURL=vegan.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}