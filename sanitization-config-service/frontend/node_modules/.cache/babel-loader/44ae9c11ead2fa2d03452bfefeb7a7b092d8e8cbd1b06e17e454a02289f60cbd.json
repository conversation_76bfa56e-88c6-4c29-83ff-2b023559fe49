{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"M22 5V2l-5.89 5.89\",\n  key: \"1eenpo\"\n}], [\"circle\", {\n  cx: \"16.6\",\n  cy: \"15.89\",\n  r: \"3\",\n  key: \"xjtalx\"\n}], [\"circle\", {\n  cx: \"8.11\",\n  cy: \"7.4\",\n  r: \"3\",\n  key: \"u2fv6i\"\n}], [\"circle\", {\n  cx: \"12.35\",\n  cy: \"11.65\",\n  r: \"3\",\n  key: \"i6i8g7\"\n}], [\"circle\", {\n  cx: \"13.91\",\n  cy: \"5.85\",\n  r: \"3\",\n  key: \"6ye0dv\"\n}], [\"circle\", {\n  cx: \"18.15\",\n  cy: \"10.09\",\n  r: \"3\",\n  key: \"snx9no\"\n}], [\"circle\", {\n  cx: \"6.56\",\n  cy: \"13.2\",\n  r: \"3\",\n  key: \"17x4xg\"\n}], [\"circle\", {\n  cx: \"10.8\",\n  cy: \"17.44\",\n  r: \"3\",\n  key: \"1hogw9\"\n}], [\"circle\", {\n  cx: \"5\",\n  cy: \"19\",\n  r: \"3\",\n  key: \"1sn6vo\"\n}]];\nconst Grape = createLucideIcon(\"grape\", __iconNode);\nexport { __iconNode, Grape as default };\n//# sourceMappingURL=grape.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}