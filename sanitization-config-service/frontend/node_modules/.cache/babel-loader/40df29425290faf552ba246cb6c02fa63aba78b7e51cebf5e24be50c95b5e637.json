{"ast": null, "code": "var _jsxFileName = \"/Users/<USER>/dev/work/lab/javaagent/sanitization-config-service/frontend/src/components/SimpleRulesList.tsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { Shield, Search, RefreshCw } from 'lucide-react';\nimport { sanitizationApi } from '../services/api';\nimport toast from 'react-hot-toast';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst SimpleRulesList = () => {\n  _s();\n  const [config, setConfig] = useState(null);\n  const [loading, setLoading] = useState(true);\n  const [searchTerm, setSearchTerm] = useState('');\n  const fetchRules = async () => {\n    try {\n      setLoading(true);\n      const response = await sanitizationApi.getRules();\n      setConfig(response.data);\n    } catch (error) {\n      toast.error('获取规则失败');\n      console.error('Error fetching rules:', error);\n    } finally {\n      setLoading(false);\n    }\n  };\n  const handleReload = async () => {\n    try {\n      await sanitizationApi.reloadRules();\n      toast.success('规则重载成功');\n      await fetchRules();\n    } catch (error) {\n      toast.error('规则重载失败');\n      console.error('Error reloading rules:', error);\n    }\n  };\n  useEffect(() => {\n    fetchRules();\n  }, []);\n  const filteredRules = (config === null || config === void 0 ? void 0 : config.rules.filter(rule => rule.name.toLowerCase().includes(searchTerm.toLowerCase()) || rule.description.toLowerCase().includes(searchTerm.toLowerCase()) || rule.id.toLowerCase().includes(searchTerm.toLowerCase()))) || [];\n  const getSeverityColor = severity => {\n    switch (severity) {\n      case 'CRITICAL':\n        return 'bg-red-100 text-red-800';\n      case 'HIGH':\n        return 'bg-orange-100 text-orange-800';\n      case 'MEDIUM':\n        return 'bg-yellow-100 text-yellow-800';\n      case 'LOW':\n        return 'bg-green-100 text-green-800';\n      default:\n        return 'bg-gray-100 text-gray-800';\n    }\n  };\n  const getTypeColor = type => {\n    switch (type) {\n      case 'FIELD_NAME':\n        return 'bg-blue-100 text-blue-800';\n      case 'PATTERN':\n        return 'bg-purple-100 text-purple-800';\n      case 'CONTENT_TYPE':\n        return 'bg-indigo-100 text-indigo-800';\n      default:\n        return 'bg-gray-100 text-gray-800';\n    }\n  };\n  if (loading) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"flex items-center justify-center h-64\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 68,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 67,\n      columnNumber: 7\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"space-y-6\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"bg-white shadow rounded-lg p-6\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex items-center justify-between\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n            className: \"text-2xl font-bold text-gray-900 flex items-center\",\n            children: [/*#__PURE__*/_jsxDEV(Shield, {\n              className: \"h-8 w-8 text-blue-600 mr-3\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 80,\n              columnNumber: 15\n            }, this), \"\\u6570\\u636E\\u8131\\u654F\\u89C4\\u5219\\u7BA1\\u7406\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 79,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"text-gray-600 mt-1\",\n            children: \"\\u7BA1\\u7406\\u548C\\u914D\\u7F6E\\u6570\\u636E\\u8131\\u654F\\u89C4\\u5219\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 83,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 78,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: handleReload,\n          className: \"inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500\",\n          children: [/*#__PURE__*/_jsxDEV(RefreshCw, {\n            className: \"h-4 w-4 mr-2\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 89,\n            columnNumber: 13\n          }, this), \"\\u91CD\\u8F7D\\u89C4\\u5219\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 85,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 77,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 76,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"bg-white shadow rounded-lg p-6\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"relative\",\n        children: [/*#__PURE__*/_jsxDEV(Search, {\n          className: \"absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 98,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n          type: \"text\",\n          value: searchTerm,\n          onChange: e => setSearchTerm(e.target.value),\n          placeholder: \"\\u641C\\u7D22\\u89C4\\u5219\\u540D\\u79F0\\u3001\\u63CF\\u8FF0\\u6216ID...\",\n          className: \"pl-10 block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 sm:text-sm\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 99,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 97,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 96,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"bg-white shadow rounded-lg overflow-hidden\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"px-6 py-4 border-b border-gray-200\",\n        children: /*#__PURE__*/_jsxDEV(\"h3\", {\n          className: \"text-lg font-medium text-gray-900\",\n          children: [\"\\u89C4\\u5219\\u5217\\u8868 (\", filteredRules.length, \" / \", (config === null || config === void 0 ? void 0 : config.rules.length) || 0, \")\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 112,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 111,\n        columnNumber: 9\n      }, this), filteredRules.length === 0 ? /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"text-center py-12\",\n        children: [/*#__PURE__*/_jsxDEV(Shield, {\n          className: \"mx-auto h-12 w-12 text-gray-400\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 119,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n          className: \"mt-2 text-sm font-medium text-gray-900\",\n          children: \"\\u6CA1\\u6709\\u627E\\u5230\\u5339\\u914D\\u7684\\u89C4\\u5219\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 120,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"mt-1 text-sm text-gray-500\",\n          children: \"\\u8BF7\\u5C1D\\u8BD5\\u8C03\\u6574\\u641C\\u7D22\\u6761\\u4EF6\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 121,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 118,\n        columnNumber: 11\n      }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"divide-y divide-gray-200\",\n        children: filteredRules.map(rule => /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"p-6 hover:bg-gray-50\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-start justify-between\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex-1\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex items-center space-x-3\",\n                children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n                  className: \"text-lg font-medium text-gray-900\",\n                  children: rule.name\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 130,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: `inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getSeverityColor(rule.severity)}`,\n                  children: rule.severity\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 131,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: `inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getTypeColor(rule.type)}`,\n                  children: rule.type\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 134,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 129,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"mt-1 text-sm text-gray-600\",\n                children: rule.description\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 138,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"mt-2 text-xs text-gray-400\",\n                children: [\"ID: \", rule.id]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 139,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"mt-3 space-y-2\",\n                children: [rule.fieldNames && rule.fieldNames.length > 0 && /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"text-sm\",\n                  children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"font-medium text-gray-700\",\n                    children: \"\\u5B57\\u6BB5:\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 144,\n                    columnNumber: 27\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"ml-2 text-gray-600\",\n                    children: rule.fieldNames.join(', ')\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 145,\n                    columnNumber: 27\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 143,\n                  columnNumber: 25\n                }, this), rule.pattern && /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"text-sm\",\n                  children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"font-medium text-gray-700\",\n                    children: \"\\u6A21\\u5F0F:\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 150,\n                    columnNumber: 27\n                  }, this), /*#__PURE__*/_jsxDEV(\"code\", {\n                    className: \"ml-2 text-xs bg-gray-100 px-2 py-1 rounded\",\n                    children: rule.pattern\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 151,\n                    columnNumber: 27\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 149,\n                  columnNumber: 25\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"text-sm\",\n                  children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"font-medium text-gray-700\",\n                    children: \"\\u63A9\\u7801:\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 155,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"code\", {\n                    className: \"ml-2 text-xs bg-gray-100 px-2 py-1 rounded\",\n                    children: rule.maskValue\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 156,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 154,\n                  columnNumber: 23\n                }, this), rule.includeServices && rule.includeServices.length > 0 && /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"text-sm\",\n                  children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"font-medium text-gray-700\",\n                    children: \"\\u9002\\u7528\\u670D\\u52A1:\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 160,\n                    columnNumber: 27\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"ml-2 text-gray-600\",\n                    children: rule.includeServices.join(', ')\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 161,\n                    columnNumber: 27\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 159,\n                  columnNumber: 25\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 141,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 128,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"ml-6 flex flex-col items-end space-y-2\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex items-center\",\n                children: rule.enabled ? /*#__PURE__*/_jsxDEV(_Fragment, {\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"w-2 h-2 bg-green-400 rounded-full mr-2 animate-pulse\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 171,\n                    columnNumber: 27\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"text-sm font-medium text-green-600\",\n                    children: \"\\u5DF2\\u542F\\u7528\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 172,\n                    columnNumber: 27\n                  }, this)]\n                }, void 0, true) : /*#__PURE__*/_jsxDEV(_Fragment, {\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"w-2 h-2 bg-gray-400 rounded-full mr-2\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 176,\n                    columnNumber: 27\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"text-sm font-medium text-gray-500\",\n                    children: \"\\u5DF2\\u7981\\u7528\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 177,\n                    columnNumber: 27\n                  }, this)]\n                }, void 0, true)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 168,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"text-sm text-gray-500\",\n                children: [\"\\u4F18\\u5148\\u7EA7: \", rule.priority]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 181,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 167,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 127,\n            columnNumber: 17\n          }, this)\n        }, rule.id, false, {\n          fileName: _jsxFileName,\n          lineNumber: 126,\n          columnNumber: 15\n        }, this))\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 124,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 110,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 74,\n    columnNumber: 5\n  }, this);\n};\n_s(SimpleRulesList, \"cqGUM1RYpLtH9780OoMokKgCKkE=\");\n_c = SimpleRulesList;\nexport default SimpleRulesList;\nvar _c;\n$RefreshReg$(_c, \"SimpleRulesList\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "Shield", "Search", "RefreshCw", "sanitizationApi", "toast", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "SimpleRulesList", "_s", "config", "setConfig", "loading", "setLoading", "searchTerm", "setSearchTerm", "fetchRules", "response", "getRules", "data", "error", "console", "handleReload", "reloadRules", "success", "filteredRules", "rules", "filter", "rule", "name", "toLowerCase", "includes", "description", "id", "getSeverityColor", "severity", "getTypeColor", "type", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "onClick", "value", "onChange", "e", "target", "placeholder", "length", "map", "fieldNames", "join", "pattern", "maskValue", "includeServices", "enabled", "priority", "_c", "$RefreshReg$"], "sources": ["/Users/<USER>/dev/work/lab/javaagent/sanitization-config-service/frontend/src/components/SimpleRulesList.tsx"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { Shield, Search, RefreshCw } from 'lucide-react';\nimport { sanitizationApi } from '../services/api';\nimport { SanitizationConfig } from '../types';\nimport toast from 'react-hot-toast';\n\nconst SimpleRulesList: React.FC = () => {\n  const [config, setConfig] = useState<SanitizationConfig | null>(null);\n  const [loading, setLoading] = useState(true);\n  const [searchTerm, setSearchTerm] = useState('');\n\n  const fetchRules = async () => {\n    try {\n      setLoading(true);\n      const response = await sanitizationApi.getRules();\n      setConfig(response.data);\n    } catch (error) {\n      toast.error('获取规则失败');\n      console.error('Error fetching rules:', error);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const handleReload = async () => {\n    try {\n      await sanitizationApi.reloadRules();\n      toast.success('规则重载成功');\n      await fetchRules();\n    } catch (error) {\n      toast.error('规则重载失败');\n      console.error('Error reloading rules:', error);\n    }\n  };\n\n  useEffect(() => {\n    fetchRules();\n  }, []);\n\n  const filteredRules = config?.rules.filter(rule =>\n    rule.name.toLowerCase().includes(searchTerm.toLowerCase()) ||\n    rule.description.toLowerCase().includes(searchTerm.toLowerCase()) ||\n    rule.id.toLowerCase().includes(searchTerm.toLowerCase())\n  ) || [];\n\n  const getSeverityColor = (severity: string) => {\n    switch (severity) {\n      case 'CRITICAL': return 'bg-red-100 text-red-800';\n      case 'HIGH': return 'bg-orange-100 text-orange-800';\n      case 'MEDIUM': return 'bg-yellow-100 text-yellow-800';\n      case 'LOW': return 'bg-green-100 text-green-800';\n      default: return 'bg-gray-100 text-gray-800';\n    }\n  };\n\n  const getTypeColor = (type: string) => {\n    switch (type) {\n      case 'FIELD_NAME': return 'bg-blue-100 text-blue-800';\n      case 'PATTERN': return 'bg-purple-100 text-purple-800';\n      case 'CONTENT_TYPE': return 'bg-indigo-100 text-indigo-800';\n      default: return 'bg-gray-100 text-gray-800';\n    }\n  };\n\n  if (loading) {\n    return (\n      <div className=\"flex items-center justify-center h-64\">\n        <div className=\"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600\"></div>\n      </div>\n    );\n  }\n\n  return (\n    <div className=\"space-y-6\">\n      {/* Header */}\n      <div className=\"bg-white shadow rounded-lg p-6\">\n        <div className=\"flex items-center justify-between\">\n          <div>\n            <h1 className=\"text-2xl font-bold text-gray-900 flex items-center\">\n              <Shield className=\"h-8 w-8 text-blue-600 mr-3\" />\n              数据脱敏规则管理\n            </h1>\n            <p className=\"text-gray-600 mt-1\">管理和配置数据脱敏规则</p>\n          </div>\n          <button\n            onClick={handleReload}\n            className=\"inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500\"\n          >\n            <RefreshCw className=\"h-4 w-4 mr-2\" />\n            重载规则\n          </button>\n        </div>\n      </div>\n\n      {/* Search */}\n      <div className=\"bg-white shadow rounded-lg p-6\">\n        <div className=\"relative\">\n          <Search className=\"absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400\" />\n          <input\n            type=\"text\"\n            value={searchTerm}\n            onChange={(e) => setSearchTerm(e.target.value)}\n            placeholder=\"搜索规则名称、描述或ID...\"\n            className=\"pl-10 block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 sm:text-sm\"\n          />\n        </div>\n      </div>\n\n      {/* Rules List */}\n      <div className=\"bg-white shadow rounded-lg overflow-hidden\">\n        <div className=\"px-6 py-4 border-b border-gray-200\">\n          <h3 className=\"text-lg font-medium text-gray-900\">\n            规则列表 ({filteredRules.length} / {config?.rules.length || 0})\n          </h3>\n        </div>\n        \n        {filteredRules.length === 0 ? (\n          <div className=\"text-center py-12\">\n            <Shield className=\"mx-auto h-12 w-12 text-gray-400\" />\n            <h3 className=\"mt-2 text-sm font-medium text-gray-900\">没有找到匹配的规则</h3>\n            <p className=\"mt-1 text-sm text-gray-500\">请尝试调整搜索条件</p>\n          </div>\n        ) : (\n          <div className=\"divide-y divide-gray-200\">\n            {filteredRules.map((rule) => (\n              <div key={rule.id} className=\"p-6 hover:bg-gray-50\">\n                <div className=\"flex items-start justify-between\">\n                  <div className=\"flex-1\">\n                    <div className=\"flex items-center space-x-3\">\n                      <h4 className=\"text-lg font-medium text-gray-900\">{rule.name}</h4>\n                      <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getSeverityColor(rule.severity)}`}>\n                        {rule.severity}\n                      </span>\n                      <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getTypeColor(rule.type)}`}>\n                        {rule.type}\n                      </span>\n                    </div>\n                    <p className=\"mt-1 text-sm text-gray-600\">{rule.description}</p>\n                    <div className=\"mt-2 text-xs text-gray-400\">ID: {rule.id}</div>\n                    \n                    <div className=\"mt-3 space-y-2\">\n                      {rule.fieldNames && rule.fieldNames.length > 0 && (\n                        <div className=\"text-sm\">\n                          <span className=\"font-medium text-gray-700\">字段:</span>\n                          <span className=\"ml-2 text-gray-600\">{rule.fieldNames.join(', ')}</span>\n                        </div>\n                      )}\n                      {rule.pattern && (\n                        <div className=\"text-sm\">\n                          <span className=\"font-medium text-gray-700\">模式:</span>\n                          <code className=\"ml-2 text-xs bg-gray-100 px-2 py-1 rounded\">{rule.pattern}</code>\n                        </div>\n                      )}\n                      <div className=\"text-sm\">\n                        <span className=\"font-medium text-gray-700\">掩码:</span>\n                        <code className=\"ml-2 text-xs bg-gray-100 px-2 py-1 rounded\">{rule.maskValue}</code>\n                      </div>\n                      {rule.includeServices && rule.includeServices.length > 0 && (\n                        <div className=\"text-sm\">\n                          <span className=\"font-medium text-gray-700\">适用服务:</span>\n                          <span className=\"ml-2 text-gray-600\">{rule.includeServices.join(', ')}</span>\n                        </div>\n                      )}\n                    </div>\n                  </div>\n                  \n                  <div className=\"ml-6 flex flex-col items-end space-y-2\">\n                    <div className=\"flex items-center\">\n                      {rule.enabled ? (\n                        <>\n                          <div className=\"w-2 h-2 bg-green-400 rounded-full mr-2 animate-pulse\"></div>\n                          <span className=\"text-sm font-medium text-green-600\">已启用</span>\n                        </>\n                      ) : (\n                        <>\n                          <div className=\"w-2 h-2 bg-gray-400 rounded-full mr-2\"></div>\n                          <span className=\"text-sm font-medium text-gray-500\">已禁用</span>\n                        </>\n                      )}\n                    </div>\n                    <div className=\"text-sm text-gray-500\">\n                      优先级: {rule.priority}\n                    </div>\n                  </div>\n                </div>\n              </div>\n            ))}\n          </div>\n        )}\n      </div>\n    </div>\n  );\n};\n\nexport default SimpleRulesList;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,MAAM,EAAEC,MAAM,EAAEC,SAAS,QAAQ,cAAc;AACxD,SAASC,eAAe,QAAQ,iBAAiB;AAEjD,OAAOC,KAAK,MAAM,iBAAiB;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAEpC,MAAMC,eAAyB,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACtC,MAAM,CAACC,MAAM,EAAEC,SAAS,CAAC,GAAGd,QAAQ,CAA4B,IAAI,CAAC;EACrE,MAAM,CAACe,OAAO,EAAEC,UAAU,CAAC,GAAGhB,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAM,CAACiB,UAAU,EAAEC,aAAa,CAAC,GAAGlB,QAAQ,CAAC,EAAE,CAAC;EAEhD,MAAMmB,UAAU,GAAG,MAAAA,CAAA,KAAY;IAC7B,IAAI;MACFH,UAAU,CAAC,IAAI,CAAC;MAChB,MAAMI,QAAQ,GAAG,MAAMf,eAAe,CAACgB,QAAQ,CAAC,CAAC;MACjDP,SAAS,CAACM,QAAQ,CAACE,IAAI,CAAC;IAC1B,CAAC,CAAC,OAAOC,KAAK,EAAE;MACdjB,KAAK,CAACiB,KAAK,CAAC,QAAQ,CAAC;MACrBC,OAAO,CAACD,KAAK,CAAC,uBAAuB,EAAEA,KAAK,CAAC;IAC/C,CAAC,SAAS;MACRP,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAED,MAAMS,YAAY,GAAG,MAAAA,CAAA,KAAY;IAC/B,IAAI;MACF,MAAMpB,eAAe,CAACqB,WAAW,CAAC,CAAC;MACnCpB,KAAK,CAACqB,OAAO,CAAC,QAAQ,CAAC;MACvB,MAAMR,UAAU,CAAC,CAAC;IACpB,CAAC,CAAC,OAAOI,KAAK,EAAE;MACdjB,KAAK,CAACiB,KAAK,CAAC,QAAQ,CAAC;MACrBC,OAAO,CAACD,KAAK,CAAC,wBAAwB,EAAEA,KAAK,CAAC;IAChD;EACF,CAAC;EAEDtB,SAAS,CAAC,MAAM;IACdkB,UAAU,CAAC,CAAC;EACd,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMS,aAAa,GAAG,CAAAf,MAAM,aAANA,MAAM,uBAANA,MAAM,CAAEgB,KAAK,CAACC,MAAM,CAACC,IAAI,IAC7CA,IAAI,CAACC,IAAI,CAACC,WAAW,CAAC,CAAC,CAACC,QAAQ,CAACjB,UAAU,CAACgB,WAAW,CAAC,CAAC,CAAC,IAC1DF,IAAI,CAACI,WAAW,CAACF,WAAW,CAAC,CAAC,CAACC,QAAQ,CAACjB,UAAU,CAACgB,WAAW,CAAC,CAAC,CAAC,IACjEF,IAAI,CAACK,EAAE,CAACH,WAAW,CAAC,CAAC,CAACC,QAAQ,CAACjB,UAAU,CAACgB,WAAW,CAAC,CAAC,CACzD,CAAC,KAAI,EAAE;EAEP,MAAMI,gBAAgB,GAAIC,QAAgB,IAAK;IAC7C,QAAQA,QAAQ;MACd,KAAK,UAAU;QAAE,OAAO,yBAAyB;MACjD,KAAK,MAAM;QAAE,OAAO,+BAA+B;MACnD,KAAK,QAAQ;QAAE,OAAO,+BAA+B;MACrD,KAAK,KAAK;QAAE,OAAO,6BAA6B;MAChD;QAAS,OAAO,2BAA2B;IAC7C;EACF,CAAC;EAED,MAAMC,YAAY,GAAIC,IAAY,IAAK;IACrC,QAAQA,IAAI;MACV,KAAK,YAAY;QAAE,OAAO,2BAA2B;MACrD,KAAK,SAAS;QAAE,OAAO,+BAA+B;MACtD,KAAK,cAAc;QAAE,OAAO,+BAA+B;MAC3D;QAAS,OAAO,2BAA2B;IAC7C;EACF,CAAC;EAED,IAAIzB,OAAO,EAAE;IACX,oBACEP,OAAA;MAAKiC,SAAS,EAAC,uCAAuC;MAAAC,QAAA,eACpDlC,OAAA;QAAKiC,SAAS,EAAC;MAA8D;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACjF,CAAC;EAEV;EAEA,oBACEtC,OAAA;IAAKiC,SAAS,EAAC,WAAW;IAAAC,QAAA,gBAExBlC,OAAA;MAAKiC,SAAS,EAAC,gCAAgC;MAAAC,QAAA,eAC7ClC,OAAA;QAAKiC,SAAS,EAAC,mCAAmC;QAAAC,QAAA,gBAChDlC,OAAA;UAAAkC,QAAA,gBACElC,OAAA;YAAIiC,SAAS,EAAC,oDAAoD;YAAAC,QAAA,gBAChElC,OAAA,CAACN,MAAM;cAACuC,SAAS,EAAC;YAA4B;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,oDAEnD;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACLtC,OAAA;YAAGiC,SAAS,EAAC,oBAAoB;YAAAC,QAAA,EAAC;UAAW;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC9C,CAAC,eACNtC,OAAA;UACEuC,OAAO,EAAEtB,YAAa;UACtBgB,SAAS,EAAC,wNAAwN;UAAAC,QAAA,gBAElOlC,OAAA,CAACJ,SAAS;YAACqC,SAAS,EAAC;UAAc;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,4BAExC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGNtC,OAAA;MAAKiC,SAAS,EAAC,gCAAgC;MAAAC,QAAA,eAC7ClC,OAAA;QAAKiC,SAAS,EAAC,UAAU;QAAAC,QAAA,gBACvBlC,OAAA,CAACL,MAAM;UAACsC,SAAS,EAAC;QAA0E;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eAC/FtC,OAAA;UACEgC,IAAI,EAAC,MAAM;UACXQ,KAAK,EAAE/B,UAAW;UAClBgC,QAAQ,EAAGC,CAAC,IAAKhC,aAAa,CAACgC,CAAC,CAACC,MAAM,CAACH,KAAK,CAAE;UAC/CI,WAAW,EAAC,mEAAiB;UAC7BX,SAAS,EAAC;QAA8G;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACzH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGNtC,OAAA;MAAKiC,SAAS,EAAC,4CAA4C;MAAAC,QAAA,gBACzDlC,OAAA;QAAKiC,SAAS,EAAC,oCAAoC;QAAAC,QAAA,eACjDlC,OAAA;UAAIiC,SAAS,EAAC,mCAAmC;UAAAC,QAAA,GAAC,4BAC1C,EAACd,aAAa,CAACyB,MAAM,EAAC,KAAG,EAAC,CAAAxC,MAAM,aAANA,MAAM,uBAANA,MAAM,CAAEgB,KAAK,CAACwB,MAAM,KAAI,CAAC,EAAC,GAC5D;QAAA;UAAAV,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACF,CAAC,EAELlB,aAAa,CAACyB,MAAM,KAAK,CAAC,gBACzB7C,OAAA;QAAKiC,SAAS,EAAC,mBAAmB;QAAAC,QAAA,gBAChClC,OAAA,CAACN,MAAM;UAACuC,SAAS,EAAC;QAAiC;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eACtDtC,OAAA;UAAIiC,SAAS,EAAC,wCAAwC;UAAAC,QAAA,EAAC;QAAS;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACrEtC,OAAA;UAAGiC,SAAS,EAAC,4BAA4B;UAAAC,QAAA,EAAC;QAAS;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACpD,CAAC,gBAENtC,OAAA;QAAKiC,SAAS,EAAC,0BAA0B;QAAAC,QAAA,EACtCd,aAAa,CAAC0B,GAAG,CAAEvB,IAAI,iBACtBvB,OAAA;UAAmBiC,SAAS,EAAC,sBAAsB;UAAAC,QAAA,eACjDlC,OAAA;YAAKiC,SAAS,EAAC,kCAAkC;YAAAC,QAAA,gBAC/ClC,OAAA;cAAKiC,SAAS,EAAC,QAAQ;cAAAC,QAAA,gBACrBlC,OAAA;gBAAKiC,SAAS,EAAC,6BAA6B;gBAAAC,QAAA,gBAC1ClC,OAAA;kBAAIiC,SAAS,EAAC,mCAAmC;kBAAAC,QAAA,EAAEX,IAAI,CAACC;gBAAI;kBAAAW,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC,eAClEtC,OAAA;kBAAMiC,SAAS,EAAE,2EAA2EJ,gBAAgB,CAACN,IAAI,CAACO,QAAQ,CAAC,EAAG;kBAAAI,QAAA,EAC3HX,IAAI,CAACO;gBAAQ;kBAAAK,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACV,CAAC,eACPtC,OAAA;kBAAMiC,SAAS,EAAE,2EAA2EF,YAAY,CAACR,IAAI,CAACS,IAAI,CAAC,EAAG;kBAAAE,QAAA,EACnHX,IAAI,CAACS;gBAAI;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACN,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACJ,CAAC,eACNtC,OAAA;gBAAGiC,SAAS,EAAC,4BAA4B;gBAAAC,QAAA,EAAEX,IAAI,CAACI;cAAW;gBAAAQ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eAChEtC,OAAA;gBAAKiC,SAAS,EAAC,4BAA4B;gBAAAC,QAAA,GAAC,MAAI,EAACX,IAAI,CAACK,EAAE;cAAA;gBAAAO,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eAE/DtC,OAAA;gBAAKiC,SAAS,EAAC,gBAAgB;gBAAAC,QAAA,GAC5BX,IAAI,CAACwB,UAAU,IAAIxB,IAAI,CAACwB,UAAU,CAACF,MAAM,GAAG,CAAC,iBAC5C7C,OAAA;kBAAKiC,SAAS,EAAC,SAAS;kBAAAC,QAAA,gBACtBlC,OAAA;oBAAMiC,SAAS,EAAC,2BAA2B;oBAAAC,QAAA,EAAC;kBAAG;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC,eACtDtC,OAAA;oBAAMiC,SAAS,EAAC,oBAAoB;oBAAAC,QAAA,EAAEX,IAAI,CAACwB,UAAU,CAACC,IAAI,CAAC,IAAI;kBAAC;oBAAAb,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACrE,CACN,EACAf,IAAI,CAAC0B,OAAO,iBACXjD,OAAA;kBAAKiC,SAAS,EAAC,SAAS;kBAAAC,QAAA,gBACtBlC,OAAA;oBAAMiC,SAAS,EAAC,2BAA2B;oBAAAC,QAAA,EAAC;kBAAG;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC,eACtDtC,OAAA;oBAAMiC,SAAS,EAAC,4CAA4C;oBAAAC,QAAA,EAAEX,IAAI,CAAC0B;kBAAO;oBAAAd,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC/E,CACN,eACDtC,OAAA;kBAAKiC,SAAS,EAAC,SAAS;kBAAAC,QAAA,gBACtBlC,OAAA;oBAAMiC,SAAS,EAAC,2BAA2B;oBAAAC,QAAA,EAAC;kBAAG;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC,eACtDtC,OAAA;oBAAMiC,SAAS,EAAC,4CAA4C;oBAAAC,QAAA,EAAEX,IAAI,CAAC2B;kBAAS;oBAAAf,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACjF,CAAC,EACLf,IAAI,CAAC4B,eAAe,IAAI5B,IAAI,CAAC4B,eAAe,CAACN,MAAM,GAAG,CAAC,iBACtD7C,OAAA;kBAAKiC,SAAS,EAAC,SAAS;kBAAAC,QAAA,gBACtBlC,OAAA;oBAAMiC,SAAS,EAAC,2BAA2B;oBAAAC,QAAA,EAAC;kBAAK;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC,eACxDtC,OAAA;oBAAMiC,SAAS,EAAC,oBAAoB;oBAAAC,QAAA,EAAEX,IAAI,CAAC4B,eAAe,CAACH,IAAI,CAAC,IAAI;kBAAC;oBAAAb,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC1E,CACN;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACE,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eAENtC,OAAA;cAAKiC,SAAS,EAAC,wCAAwC;cAAAC,QAAA,gBACrDlC,OAAA;gBAAKiC,SAAS,EAAC,mBAAmB;gBAAAC,QAAA,EAC/BX,IAAI,CAAC6B,OAAO,gBACXpD,OAAA,CAAAE,SAAA;kBAAAgC,QAAA,gBACElC,OAAA;oBAAKiC,SAAS,EAAC;kBAAsD;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC,eAC5EtC,OAAA;oBAAMiC,SAAS,EAAC,oCAAoC;oBAAAC,QAAA,EAAC;kBAAG;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC;gBAAA,eAC/D,CAAC,gBAEHtC,OAAA,CAAAE,SAAA;kBAAAgC,QAAA,gBACElC,OAAA;oBAAKiC,SAAS,EAAC;kBAAuC;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC,eAC7DtC,OAAA;oBAAMiC,SAAS,EAAC,mCAAmC;oBAAAC,QAAA,EAAC;kBAAG;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC;gBAAA,eAC9D;cACH;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACE,CAAC,eACNtC,OAAA;gBAAKiC,SAAS,EAAC,uBAAuB;gBAAAC,QAAA,GAAC,sBAChC,EAACX,IAAI,CAAC8B,QAAQ;cAAA;gBAAAlB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAChB,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC,GA3DEf,IAAI,CAACK,EAAE;UAAAO,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OA4DZ,CACN;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CACN;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAAClC,EAAA,CA1LID,eAAyB;AAAAmD,EAAA,GAAzBnD,eAAyB;AA4L/B,eAAeA,eAAe;AAAC,IAAAmD,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}