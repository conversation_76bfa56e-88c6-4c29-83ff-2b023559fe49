{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"m5 19-2 2V5a2 2 0 0 1 2-2h14a2 2 0 0 1 2 2v10a2 2 0 0 1-2 2\",\n  key: \"1xuzuj\"\n}], [\"path\", {\n  d: \"M9 10h6\",\n  key: \"9gxzsh\"\n}], [\"path\", {\n  d: \"M12 7v6\",\n  key: \"lw1j43\"\n}], [\"path\", {\n  d: \"M9 17h6\",\n  key: \"r8uit2\"\n}]];\nconst MessageSquareDiff = createLucideIcon(\"message-square-diff\", __iconNode);\nexport { __iconNode, MessageSquareDiff as default };\n//# sourceMappingURL=message-square-diff.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}