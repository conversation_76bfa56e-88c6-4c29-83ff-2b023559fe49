{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"circle\", {\n  cx: \"12\",\n  cy: \"13\",\n  r: \"8\",\n  key: \"3y4lt7\"\n}], [\"path\", {\n  d: \"M5 3 2 6\",\n  key: \"18tl5t\"\n}], [\"path\", {\n  d: \"m22 6-3-3\",\n  key: \"1opdir\"\n}], [\"path\", {\n  d: \"M6.38 18.7 4 21\",\n  key: \"17xu3x\"\n}], [\"path\", {\n  d: \"M17.64 18.67 20 21\",\n  key: \"kv2oe2\"\n}], [\"path\", {\n  d: \"m9 13 2 2 4-4\",\n  key: \"6343dt\"\n}]];\nconst AlarmClockCheck = createLucideIcon(\"alarm-clock-check\", __iconNode);\nexport { __iconNode, AlarmClockCheck as default };\n//# sourceMappingURL=alarm-clock-check.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}