{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"M21 6H3\",\n  key: \"1jwq7v\"\n}], [\"path\", {\n  d: \"M7 12H3\",\n  key: \"13ou7f\"\n}], [\"path\", {\n  d: \"M7 18H3\",\n  key: \"1sijw9\"\n}], [\"path\", {\n  d: \"M12 18a5 5 0 0 0 9-3 4.5 4.5 0 0 0-4.5-4.5c-1.33 0-2.54.54-3.41 1.41L11 14\",\n  key: \"qth677\"\n}], [\"path\", {\n  d: \"M11 10v4h4\",\n  key: \"172dkj\"\n}]];\nconst ListRestart = createLucideIcon(\"list-restart\", __iconNode);\nexport { __iconNode, ListRestart as default };\n//# sourceMappingURL=list-restart.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}