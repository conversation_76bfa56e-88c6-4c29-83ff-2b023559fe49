{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"m2 2 20 20\",\n  key: \"1ooewy\"\n}], [\"path\", {\n  d: \"M5 5a1 1 0 0 0-1 1v7c0 5 3.5 7.5 7.67 8.94a1 1 0 0 0 .67.01c2.35-.82 4.48-1.97 5.9-3.71\",\n  key: \"1jlk70\"\n}], [\"path\", {\n  d: \"M9.309 3.652A12.252 12.252 0 0 0 11.24 2.28a1.17 1.17 0 0 1 1.52 0C14.51 3.81 17 5 19 5a1 1 0 0 1 1 1v7a9.784 9.784 0 0 1-.08 1.264\",\n  key: \"18rp1v\"\n}]];\nconst ShieldOff = createLucideIcon(\"shield-off\", __iconNode);\nexport { __iconNode, ShieldOff as default };\n//# sourceMappingURL=shield-off.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}