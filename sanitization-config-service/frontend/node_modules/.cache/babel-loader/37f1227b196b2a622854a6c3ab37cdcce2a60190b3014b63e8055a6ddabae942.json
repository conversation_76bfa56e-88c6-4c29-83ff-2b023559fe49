{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"rect\", {\n  width: \"16\",\n  height: \"6\",\n  x: \"2\",\n  y: \"2\",\n  rx: \"2\",\n  key: \"jcyz7m\"\n}], [\"path\", {\n  d: \"M10 16v-2a2 2 0 0 1 2-2h8a2 2 0 0 0 2-2V7a2 2 0 0 0-2-2h-2\",\n  key: \"1b9h7c\"\n}], [\"rect\", {\n  width: \"4\",\n  height: \"6\",\n  x: \"8\",\n  y: \"16\",\n  rx: \"1\",\n  key: \"d6e7yl\"\n}]];\nconst PaintRoller = createLucideIcon(\"paint-roller\", __iconNode);\nexport { __iconNode, PaintRoller as default };\n//# sourceMappingURL=paint-roller.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}