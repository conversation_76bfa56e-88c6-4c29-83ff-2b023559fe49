{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"rect\", {\n  width: \"7\",\n  height: \"7\",\n  x: \"3\",\n  y: \"3\",\n  rx: \"1\",\n  key: \"1g98yp\"\n}], [\"rect\", {\n  width: \"7\",\n  height: \"7\",\n  x: \"3\",\n  y: \"14\",\n  rx: \"1\",\n  key: \"1bb6yr\"\n}], [\"path\", {\n  d: \"M14 4h7\",\n  key: \"3xa0d5\"\n}], [\"path\", {\n  d: \"M14 9h7\",\n  key: \"1icrd9\"\n}], [\"path\", {\n  d: \"M14 15h7\",\n  key: \"1mj8o2\"\n}], [\"path\", {\n  d: \"M14 20h7\",\n  key: \"11slyb\"\n}]];\nconst LayoutList = createLucideIcon(\"layout-list\", __iconNode);\nexport { __iconNode, LayoutList as default };\n//# sourceMappingURL=layout-list.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}