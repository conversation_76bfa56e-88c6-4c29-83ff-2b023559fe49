{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"M16 3h5v5\",\n  key: \"1806ms\"\n}], [\"path\", {\n  d: \"M17 21h2a2 2 0 0 0 2-2\",\n  key: \"130fy9\"\n}], [\"path\", {\n  d: \"M21 12v3\",\n  key: \"1wzk3p\"\n}], [\"path\", {\n  d: \"m21 3-5 5\",\n  key: \"1g5oa7\"\n}], [\"path\", {\n  d: \"M3 7V5a2 2 0 0 1 2-2\",\n  key: \"kk3yz1\"\n}], [\"path\", {\n  d: \"m5 21 4.144-4.144a1.21 1.21 0 0 1 1.712 0L13 19\",\n  key: \"fyekpt\"\n}], [\"path\", {\n  d: \"M9 3h3\",\n  key: \"d52fa\"\n}], [\"rect\", {\n  x: \"3\",\n  y: \"11\",\n  width: \"10\",\n  height: \"10\",\n  rx: \"1\",\n  key: \"1wpmix\"\n}]];\nconst ImageUpscale = createLucideIcon(\"image-upscale\", __iconNode);\nexport { __iconNode, ImageUpscale as default };\n//# sourceMappingURL=image-upscale.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}