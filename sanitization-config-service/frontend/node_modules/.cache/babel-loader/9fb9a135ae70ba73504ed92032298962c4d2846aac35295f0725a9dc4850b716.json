{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"circle\", {\n  cx: \"15\",\n  cy: \"12\",\n  r: \"3\",\n  key: \"1afu0r\"\n}], [\"rect\", {\n  width: \"20\",\n  height: \"14\",\n  x: \"2\",\n  y: \"5\",\n  rx: \"7\",\n  key: \"g7kal2\"\n}]];\nconst ToggleRight = createLucideIcon(\"toggle-right\", __iconNode);\nexport { __iconNode, ToggleRight as default };\n//# sourceMappingURL=toggle-right.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}