{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"M17 14V2\",\n  key: \"8ymqnk\"\n}], [\"path\", {\n  d: \"M9 18.12 10 14H4.17a2 2 0 0 1-1.92-2.56l2.33-8A2 2 0 0 1 6.5 2H20a2 2 0 0 1 2 2v8a2 2 0 0 1-2 2h-2.76a2 2 0 0 0-1.79 1.11L12 22a3.13 3.13 0 0 1-3-3.88Z\",\n  key: \"m61m77\"\n}]];\nconst ThumbsDown = createLucideIcon(\"thumbs-down\", __iconNode);\nexport { __iconNode, ThumbsDown as default };\n//# sourceMappingURL=thumbs-down.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}