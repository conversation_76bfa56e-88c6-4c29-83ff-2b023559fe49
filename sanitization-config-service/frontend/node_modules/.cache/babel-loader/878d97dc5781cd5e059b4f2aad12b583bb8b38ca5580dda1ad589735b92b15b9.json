{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"M16 10h2\",\n  key: \"8sgtl7\"\n}], [\"path\", {\n  d: \"M16 14h2\",\n  key: \"epxaof\"\n}], [\"path\", {\n  d: \"M6.17 15a3 3 0 0 1 5.66 0\",\n  key: \"n6f512\"\n}], [\"circle\", {\n  cx: \"9\",\n  cy: \"11\",\n  r: \"2\",\n  key: \"yxgjnd\"\n}], [\"rect\", {\n  x: \"2\",\n  y: \"5\",\n  width: \"20\",\n  height: \"14\",\n  rx: \"2\",\n  key: \"qneu4z\"\n}]];\nconst IdCard = createLucideIcon(\"id-card\", __iconNode);\nexport { __iconNode, IdCard as default };\n//# sourceMappingURL=id-card.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}