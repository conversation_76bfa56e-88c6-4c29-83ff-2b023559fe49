{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"M8 2h8\",\n  key: \"1ssgc1\"\n}], [\"path\", {\n  d: \"M9 2v2.789a4 4 0 0 1-.672 2.219l-.656.984A4 4 0 0 0 7 10.212V20a2 2 0 0 0 2 2h6a2 2 0 0 0 2-2v-9.789a4 4 0 0 0-.672-2.219l-.656-.984A4 4 0 0 1 15 4.788V2\",\n  key: \"qtp12x\"\n}], [\"path\", {\n  d: \"M7 15a6.472 6.472 0 0 1 5 0 6.47 6.47 0 0 0 5 0\",\n  key: \"ygeh44\"\n}]];\nconst Milk = createLucideIcon(\"milk\", __iconNode);\nexport { __iconNode, Milk as default };\n//# sourceMappingURL=milk.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}