{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"circle\", {\n  cx: \"6\",\n  cy: \"19\",\n  r: \"3\",\n  key: \"1kj8tv\"\n}], [\"path\", {\n  d: \"M9 19h8.5c.4 0 .9-.1 1.3-.2\",\n  key: \"1effex\"\n}], [\"path\", {\n  d: \"M5.2 5.2A3.5 3.53 0 0 0 6.5 12H12\",\n  key: \"k9y2ds\"\n}], [\"path\", {\n  d: \"m2 2 20 20\",\n  key: \"1ooewy\"\n}], [\"path\", {\n  d: \"M21 15.3a3.5 3.5 0 0 0-3.3-3.3\",\n  key: \"11nlu2\"\n}], [\"path\", {\n  d: \"M15 5h-4.3\",\n  key: \"6537je\"\n}], [\"circle\", {\n  cx: \"18\",\n  cy: \"5\",\n  r: \"3\",\n  key: \"gq8acd\"\n}]];\nconst RouteOff = createLucideIcon(\"route-off\", __iconNode);\nexport { __iconNode, RouteOff as default };\n//# sourceMappingURL=route-off.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}