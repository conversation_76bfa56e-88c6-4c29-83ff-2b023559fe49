{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"circle\", {\n  cx: \"6\",\n  cy: \"12\",\n  r: \"4\",\n  key: \"1ehtga\"\n}], [\"circle\", {\n  cx: \"18\",\n  cy: \"12\",\n  r: \"4\",\n  key: \"4vafl8\"\n}], [\"line\", {\n  x1: \"6\",\n  x2: \"18\",\n  y1: \"16\",\n  y2: \"16\",\n  key: \"pmt8us\"\n}]];\nconst Voicemail = createLucideIcon(\"voicemail\", __iconNode);\nexport { __iconNode, Voicemail as default };\n//# sourceMappingURL=voicemail.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}