{"ast": null, "code": "var _jsxFileName = \"/Users/<USER>/dev/work/lab/javaagent/sanitization-config-service/frontend/src/components/Dashboard.tsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { Activity, Shield, AlertTriangle, CheckCircle, RefreshCw } from 'lucide-react';\nimport { sanitizationApi } from '../services/api';\nimport { formatTimestamp } from '../utils';\nimport toast from 'react-hot-toast';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst Dashboard = () => {\n  _s();\n  const [health, setHealth] = useState(null);\n  const [metrics, setMetrics] = useState(null);\n  const [loading, setLoading] = useState(true);\n  const [refreshing, setRefreshing] = useState(false);\n  const fetchData = async () => {\n    try {\n      const [healthData, metricsData] = await Promise.all([sanitizationApi.getHealth(), sanitizationApi.getMetrics()]);\n      setHealth(healthData);\n      setMetrics(metricsData);\n    } catch (error) {\n      console.error('Failed to fetch dashboard data:', error);\n      toast.error('Failed to load dashboard data');\n    } finally {\n      setLoading(false);\n      setRefreshing(false);\n    }\n  };\n  const handleRefresh = async () => {\n    setRefreshing(true);\n    await fetchData();\n  };\n  useEffect(() => {\n    fetchData();\n    const interval = setInterval(fetchData, 30000); // Refresh every 30 seconds\n    return () => clearInterval(interval);\n  }, []);\n  if (loading) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"flex items-center justify-center h-64\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"animate-spin rounded-full h-12 w-12 border-b-2 border-primary-600\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 45,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 44,\n      columnNumber: 7\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"space-y-6\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"flex items-center justify-between\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n          className: \"text-2xl font-bold text-gray-900\",\n          children: \"Dashboard\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 55,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-gray-600\",\n          children: \"Sanitization service overview and metrics\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 56,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 54,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n        onClick: handleRefresh,\n        disabled: refreshing,\n        className: \"inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 disabled:opacity-50\",\n        children: [/*#__PURE__*/_jsxDEV(RefreshCw, {\n          className: `h-4 w-4 mr-2 ${refreshing ? 'animate-spin' : ''}`\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 63,\n          columnNumber: 11\n        }, this), \"Refresh\"]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 58,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 53,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"bg-white overflow-hidden shadow rounded-lg\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"p-5\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-center\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex-shrink-0\",\n              children: /*#__PURE__*/_jsxDEV(Activity, {\n                className: \"h-6 w-6 text-green-600\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 75,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 74,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"ml-5 w-0 flex-1\",\n              children: /*#__PURE__*/_jsxDEV(\"dl\", {\n                children: [/*#__PURE__*/_jsxDEV(\"dt\", {\n                  className: \"text-sm font-medium text-gray-500 truncate\",\n                  children: \"Service Status\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 79,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"dd\", {\n                  className: \"text-lg font-medium text-gray-900\",\n                  children: (health === null || health === void 0 ? void 0 : health.status) === 'healthy' ? /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"text-green-600\",\n                    children: \"Healthy\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 82,\n                    columnNumber: 23\n                  }, this) : /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"text-red-600\",\n                    children: \"Unhealthy\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 84,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 80,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 78,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 77,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 73,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 72,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 71,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"bg-white overflow-hidden shadow rounded-lg\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"p-5\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-center\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex-shrink-0\",\n              children: /*#__PURE__*/_jsxDEV(Shield, {\n                className: \"h-6 w-6 text-blue-600\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 98,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 97,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"ml-5 w-0 flex-1\",\n              children: /*#__PURE__*/_jsxDEV(\"dl\", {\n                children: [/*#__PURE__*/_jsxDEV(\"dt\", {\n                  className: \"text-sm font-medium text-gray-500 truncate\",\n                  children: \"Total Rules\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 102,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"dd\", {\n                  className: \"text-lg font-medium text-gray-900\",\n                  children: (metrics === null || metrics === void 0 ? void 0 : metrics.totalRules) || 0\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 103,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 101,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 100,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 96,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 95,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 94,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"bg-white overflow-hidden shadow rounded-lg\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"p-5\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-center\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex-shrink-0\",\n              children: /*#__PURE__*/_jsxDEV(CheckCircle, {\n                className: \"h-6 w-6 text-green-600\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 115,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 114,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"ml-5 w-0 flex-1\",\n              children: /*#__PURE__*/_jsxDEV(\"dl\", {\n                children: [/*#__PURE__*/_jsxDEV(\"dt\", {\n                  className: \"text-sm font-medium text-gray-500 truncate\",\n                  children: \"Enabled Rules\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 119,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"dd\", {\n                  className: \"text-lg font-medium text-gray-900\",\n                  children: (metrics === null || metrics === void 0 ? void 0 : metrics.enabledRules) || 0\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 120,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 118,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 117,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 113,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 112,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 111,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"bg-white overflow-hidden shadow rounded-lg\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"p-5\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-center\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex-shrink-0\",\n              children: /*#__PURE__*/_jsxDEV(AlertTriangle, {\n                className: \"h-6 w-6 text-yellow-600\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 132,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 131,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"ml-5 w-0 flex-1\",\n              children: /*#__PURE__*/_jsxDEV(\"dl\", {\n                children: [/*#__PURE__*/_jsxDEV(\"dt\", {\n                  className: \"text-sm font-medium text-gray-500 truncate\",\n                  children: \"Disabled Rules\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 136,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"dd\", {\n                  className: \"text-lg font-medium text-gray-900\",\n                  children: (metrics === null || metrics === void 0 ? void 0 : metrics.disabledRules) || 0\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 137,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 135,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 134,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 130,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 129,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 128,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 69,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"grid grid-cols-1 lg:grid-cols-2 gap-6\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"bg-white shadow rounded-lg p-6\",\n        children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n          className: \"text-lg font-medium text-gray-900 mb-4\",\n          children: \"Rules by Type\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 149,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"space-y-3\",\n          children: (metrics === null || metrics === void 0 ? void 0 : metrics.rulesByType) && Object.entries(metrics.rulesByType).map(([type, count]) => /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-center justify-between\",\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"text-sm text-gray-600\",\n              children: type.replace('_', ' ')\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 153,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"text-sm font-medium text-gray-900\",\n              children: count\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 154,\n              columnNumber: 17\n            }, this)]\n          }, type, true, {\n            fileName: _jsxFileName,\n            lineNumber: 152,\n            columnNumber: 15\n          }, this))\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 150,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 148,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"bg-white shadow rounded-lg p-6\",\n        children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n          className: \"text-lg font-medium text-gray-900 mb-4\",\n          children: \"Rules by Severity\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 162,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"space-y-3\",\n          children: (metrics === null || metrics === void 0 ? void 0 : metrics.rulesBySeverity) && Object.entries(metrics.rulesBySeverity).map(([severity, count]) => /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-center justify-between\",\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"text-sm text-gray-600\",\n              children: severity\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 166,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"text-sm font-medium text-gray-900\",\n              children: count\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 167,\n              columnNumber: 17\n            }, this)]\n          }, severity, true, {\n            fileName: _jsxFileName,\n            lineNumber: 165,\n            columnNumber: 15\n          }, this))\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 163,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 161,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 146,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"bg-white shadow rounded-lg p-6\",\n      children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n        className: \"text-lg font-medium text-gray-900 mb-4\",\n        children: \"System Information\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 176,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"grid grid-cols-1 md:grid-cols-3 gap-4\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          children: [/*#__PURE__*/_jsxDEV(\"dt\", {\n            className: \"text-sm font-medium text-gray-500\",\n            children: \"Version\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 179,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"dd\", {\n            className: \"mt-1 text-sm text-gray-900\",\n            children: (health === null || health === void 0 ? void 0 : health.version) || 'N/A'\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 180,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 178,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          children: [/*#__PURE__*/_jsxDEV(\"dt\", {\n            className: \"text-sm font-medium text-gray-500\",\n            children: \"Config Version\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 183,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"dd\", {\n            className: \"mt-1 text-sm text-gray-900\",\n            children: (metrics === null || metrics === void 0 ? void 0 : metrics.configVersion) || 'N/A'\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 184,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 182,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          children: [/*#__PURE__*/_jsxDEV(\"dt\", {\n            className: \"text-sm font-medium text-gray-500\",\n            children: \"Last Updated\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 187,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"dd\", {\n            className: \"mt-1 text-sm text-gray-900\",\n            children: metrics !== null && metrics !== void 0 && metrics.lastUpdated ? formatTimestamp(metrics.lastUpdated) : 'N/A'\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 188,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 186,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 177,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 175,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 51,\n    columnNumber: 5\n  }, this);\n};\n_s(Dashboard, \"J+se3TZYZJ495TdSp5YzGnZerlM=\");\n_c = Dashboard;\nexport default Dashboard;\nvar _c;\n$RefreshReg$(_c, \"Dashboard\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "Activity", "Shield", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "CheckCircle", "RefreshCw", "sanitizationApi", "formatTimestamp", "toast", "jsxDEV", "_jsxDEV", "Dashboard", "_s", "health", "setHealth", "metrics", "setMetrics", "loading", "setLoading", "refreshing", "setRefreshing", "fetchData", "healthData", "metricsData", "Promise", "all", "getHealth", "getMetrics", "error", "console", "handleRefresh", "interval", "setInterval", "clearInterval", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "onClick", "disabled", "status", "totalRules", "enabledRules", "disabledRules", "rulesByType", "Object", "entries", "map", "type", "count", "replace", "rulesBySeverity", "severity", "version", "configVersion", "lastUpdated", "_c", "$RefreshReg$"], "sources": ["/Users/<USER>/dev/work/lab/javaagent/sanitization-config-service/frontend/src/components/Dashboard.tsx"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { Activity, Shield, AlertTriangle, CheckCircle, RefreshCw } from 'lucide-react';\nimport { sanitizationApi } from '../services/api';\nimport { HealthResponse, MetricsResponse } from '../types';\nimport { formatTimestamp } from '../utils';\nimport toast from 'react-hot-toast';\n\nconst Dashboard: React.FC = () => {\n  const [health, setHealth] = useState<HealthResponse | null>(null);\n  const [metrics, setMetrics] = useState<MetricsResponse | null>(null);\n  const [loading, setLoading] = useState(true);\n  const [refreshing, setRefreshing] = useState(false);\n\n  const fetchData = async () => {\n    try {\n      const [healthData, metricsData] = await Promise.all([\n        sanitizationApi.getHealth(),\n        sanitizationApi.getMetrics(),\n      ]);\n      setHealth(healthData);\n      setMetrics(metricsData);\n    } catch (error) {\n      console.error('Failed to fetch dashboard data:', error);\n      toast.error('Failed to load dashboard data');\n    } finally {\n      setLoading(false);\n      setRefreshing(false);\n    }\n  };\n\n  const handleRefresh = async () => {\n    setRefreshing(true);\n    await fetchData();\n  };\n\n  useEffect(() => {\n    fetchData();\n    const interval = setInterval(fetchData, 30000); // Refresh every 30 seconds\n    return () => clearInterval(interval);\n  }, []);\n\n  if (loading) {\n    return (\n      <div className=\"flex items-center justify-center h-64\">\n        <div className=\"animate-spin rounded-full h-12 w-12 border-b-2 border-primary-600\"></div>\n      </div>\n    );\n  }\n\n  return (\n    <div className=\"space-y-6\">\n      {/* Header */}\n      <div className=\"flex items-center justify-between\">\n        <div>\n          <h1 className=\"text-2xl font-bold text-gray-900\">Dashboard</h1>\n          <p className=\"text-gray-600\">Sanitization service overview and metrics</p>\n        </div>\n        <button\n          onClick={handleRefresh}\n          disabled={refreshing}\n          className=\"inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 disabled:opacity-50\"\n        >\n          <RefreshCw className={`h-4 w-4 mr-2 ${refreshing ? 'animate-spin' : ''}`} />\n          Refresh\n        </button>\n      </div>\n\n      {/* Status Cards */}\n      <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6\">\n        {/* Service Status */}\n        <div className=\"bg-white overflow-hidden shadow rounded-lg\">\n          <div className=\"p-5\">\n            <div className=\"flex items-center\">\n              <div className=\"flex-shrink-0\">\n                <Activity className=\"h-6 w-6 text-green-600\" />\n              </div>\n              <div className=\"ml-5 w-0 flex-1\">\n                <dl>\n                  <dt className=\"text-sm font-medium text-gray-500 truncate\">Service Status</dt>\n                  <dd className=\"text-lg font-medium text-gray-900\">\n                    {health?.status === 'healthy' ? (\n                      <span className=\"text-green-600\">Healthy</span>\n                    ) : (\n                      <span className=\"text-red-600\">Unhealthy</span>\n                    )}\n                  </dd>\n                </dl>\n              </div>\n            </div>\n          </div>\n        </div>\n\n        {/* Total Rules */}\n        <div className=\"bg-white overflow-hidden shadow rounded-lg\">\n          <div className=\"p-5\">\n            <div className=\"flex items-center\">\n              <div className=\"flex-shrink-0\">\n                <Shield className=\"h-6 w-6 text-blue-600\" />\n              </div>\n              <div className=\"ml-5 w-0 flex-1\">\n                <dl>\n                  <dt className=\"text-sm font-medium text-gray-500 truncate\">Total Rules</dt>\n                  <dd className=\"text-lg font-medium text-gray-900\">{metrics?.totalRules || 0}</dd>\n                </dl>\n              </div>\n            </div>\n          </div>\n        </div>\n\n        {/* Enabled Rules */}\n        <div className=\"bg-white overflow-hidden shadow rounded-lg\">\n          <div className=\"p-5\">\n            <div className=\"flex items-center\">\n              <div className=\"flex-shrink-0\">\n                <CheckCircle className=\"h-6 w-6 text-green-600\" />\n              </div>\n              <div className=\"ml-5 w-0 flex-1\">\n                <dl>\n                  <dt className=\"text-sm font-medium text-gray-500 truncate\">Enabled Rules</dt>\n                  <dd className=\"text-lg font-medium text-gray-900\">{metrics?.enabledRules || 0}</dd>\n                </dl>\n              </div>\n            </div>\n          </div>\n        </div>\n\n        {/* Disabled Rules */}\n        <div className=\"bg-white overflow-hidden shadow rounded-lg\">\n          <div className=\"p-5\">\n            <div className=\"flex items-center\">\n              <div className=\"flex-shrink-0\">\n                <AlertTriangle className=\"h-6 w-6 text-yellow-600\" />\n              </div>\n              <div className=\"ml-5 w-0 flex-1\">\n                <dl>\n                  <dt className=\"text-sm font-medium text-gray-500 truncate\">Disabled Rules</dt>\n                  <dd className=\"text-lg font-medium text-gray-900\">{metrics?.disabledRules || 0}</dd>\n                </dl>\n              </div>\n            </div>\n          </div>\n        </div>\n      </div>\n\n      {/* Charts Section */}\n      <div className=\"grid grid-cols-1 lg:grid-cols-2 gap-6\">\n        {/* Rules by Type */}\n        <div className=\"bg-white shadow rounded-lg p-6\">\n          <h3 className=\"text-lg font-medium text-gray-900 mb-4\">Rules by Type</h3>\n          <div className=\"space-y-3\">\n            {metrics?.rulesByType && Object.entries(metrics.rulesByType).map(([type, count]) => (\n              <div key={type} className=\"flex items-center justify-between\">\n                <span className=\"text-sm text-gray-600\">{type.replace('_', ' ')}</span>\n                <span className=\"text-sm font-medium text-gray-900\">{count}</span>\n              </div>\n            ))}\n          </div>\n        </div>\n\n        {/* Rules by Severity */}\n        <div className=\"bg-white shadow rounded-lg p-6\">\n          <h3 className=\"text-lg font-medium text-gray-900 mb-4\">Rules by Severity</h3>\n          <div className=\"space-y-3\">\n            {metrics?.rulesBySeverity && Object.entries(metrics.rulesBySeverity).map(([severity, count]) => (\n              <div key={severity} className=\"flex items-center justify-between\">\n                <span className=\"text-sm text-gray-600\">{severity}</span>\n                <span className=\"text-sm font-medium text-gray-900\">{count}</span>\n              </div>\n            ))}\n          </div>\n        </div>\n      </div>\n\n      {/* System Info */}\n      <div className=\"bg-white shadow rounded-lg p-6\">\n        <h3 className=\"text-lg font-medium text-gray-900 mb-4\">System Information</h3>\n        <div className=\"grid grid-cols-1 md:grid-cols-3 gap-4\">\n          <div>\n            <dt className=\"text-sm font-medium text-gray-500\">Version</dt>\n            <dd className=\"mt-1 text-sm text-gray-900\">{health?.version || 'N/A'}</dd>\n          </div>\n          <div>\n            <dt className=\"text-sm font-medium text-gray-500\">Config Version</dt>\n            <dd className=\"mt-1 text-sm text-gray-900\">{metrics?.configVersion || 'N/A'}</dd>\n          </div>\n          <div>\n            <dt className=\"text-sm font-medium text-gray-500\">Last Updated</dt>\n            <dd className=\"mt-1 text-sm text-gray-900\">\n              {metrics?.lastUpdated ? formatTimestamp(metrics.lastUpdated) : 'N/A'}\n            </dd>\n          </div>\n        </div>\n      </div>\n    </div>\n  );\n};\n\nexport default Dashboard;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,QAAQ,EAAEC,MAAM,EAAEC,aAAa,EAAEC,WAAW,EAAEC,SAAS,QAAQ,cAAc;AACtF,SAASC,eAAe,QAAQ,iBAAiB;AAEjD,SAASC,eAAe,QAAQ,UAAU;AAC1C,OAAOC,KAAK,MAAM,iBAAiB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEpC,MAAMC,SAAmB,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAChC,MAAM,CAACC,MAAM,EAAEC,SAAS,CAAC,GAAGf,QAAQ,CAAwB,IAAI,CAAC;EACjE,MAAM,CAACgB,OAAO,EAAEC,UAAU,CAAC,GAAGjB,QAAQ,CAAyB,IAAI,CAAC;EACpE,MAAM,CAACkB,OAAO,EAAEC,UAAU,CAAC,GAAGnB,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAM,CAACoB,UAAU,EAAEC,aAAa,CAAC,GAAGrB,QAAQ,CAAC,KAAK,CAAC;EAEnD,MAAMsB,SAAS,GAAG,MAAAA,CAAA,KAAY;IAC5B,IAAI;MACF,MAAM,CAACC,UAAU,EAAEC,WAAW,CAAC,GAAG,MAAMC,OAAO,CAACC,GAAG,CAAC,CAClDnB,eAAe,CAACoB,SAAS,CAAC,CAAC,EAC3BpB,eAAe,CAACqB,UAAU,CAAC,CAAC,CAC7B,CAAC;MACFb,SAAS,CAACQ,UAAU,CAAC;MACrBN,UAAU,CAACO,WAAW,CAAC;IACzB,CAAC,CAAC,OAAOK,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,iCAAiC,EAAEA,KAAK,CAAC;MACvDpB,KAAK,CAACoB,KAAK,CAAC,+BAA+B,CAAC;IAC9C,CAAC,SAAS;MACRV,UAAU,CAAC,KAAK,CAAC;MACjBE,aAAa,CAAC,KAAK,CAAC;IACtB;EACF,CAAC;EAED,MAAMU,aAAa,GAAG,MAAAA,CAAA,KAAY;IAChCV,aAAa,CAAC,IAAI,CAAC;IACnB,MAAMC,SAAS,CAAC,CAAC;EACnB,CAAC;EAEDrB,SAAS,CAAC,MAAM;IACdqB,SAAS,CAAC,CAAC;IACX,MAAMU,QAAQ,GAAGC,WAAW,CAACX,SAAS,EAAE,KAAK,CAAC,CAAC,CAAC;IAChD,OAAO,MAAMY,aAAa,CAACF,QAAQ,CAAC;EACtC,CAAC,EAAE,EAAE,CAAC;EAEN,IAAId,OAAO,EAAE;IACX,oBACEP,OAAA;MAAKwB,SAAS,EAAC,uCAAuC;MAAAC,QAAA,eACpDzB,OAAA;QAAKwB,SAAS,EAAC;MAAmE;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACtF,CAAC;EAEV;EAEA,oBACE7B,OAAA;IAAKwB,SAAS,EAAC,WAAW;IAAAC,QAAA,gBAExBzB,OAAA;MAAKwB,SAAS,EAAC,mCAAmC;MAAAC,QAAA,gBAChDzB,OAAA;QAAAyB,QAAA,gBACEzB,OAAA;UAAIwB,SAAS,EAAC,kCAAkC;UAAAC,QAAA,EAAC;QAAS;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAC/D7B,OAAA;UAAGwB,SAAS,EAAC,eAAe;UAAAC,QAAA,EAAC;QAAyC;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACvE,CAAC,eACN7B,OAAA;QACE8B,OAAO,EAAEV,aAAc;QACvBW,QAAQ,EAAEtB,UAAW;QACrBe,SAAS,EAAC,qPAAqP;QAAAC,QAAA,gBAE/PzB,OAAA,CAACL,SAAS;UAAC6B,SAAS,EAAE,gBAAgBf,UAAU,GAAG,cAAc,GAAG,EAAE;QAAG;UAAAiB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,WAE9E;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACN,CAAC,eAGN7B,OAAA;MAAKwB,SAAS,EAAC,sDAAsD;MAAAC,QAAA,gBAEnEzB,OAAA;QAAKwB,SAAS,EAAC,4CAA4C;QAAAC,QAAA,eACzDzB,OAAA;UAAKwB,SAAS,EAAC,KAAK;UAAAC,QAAA,eAClBzB,OAAA;YAAKwB,SAAS,EAAC,mBAAmB;YAAAC,QAAA,gBAChCzB,OAAA;cAAKwB,SAAS,EAAC,eAAe;cAAAC,QAAA,eAC5BzB,OAAA,CAACT,QAAQ;gBAACiC,SAAS,EAAC;cAAwB;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC5C,CAAC,eACN7B,OAAA;cAAKwB,SAAS,EAAC,iBAAiB;cAAAC,QAAA,eAC9BzB,OAAA;gBAAAyB,QAAA,gBACEzB,OAAA;kBAAIwB,SAAS,EAAC,4CAA4C;kBAAAC,QAAA,EAAC;gBAAc;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eAC9E7B,OAAA;kBAAIwB,SAAS,EAAC,mCAAmC;kBAAAC,QAAA,EAC9C,CAAAtB,MAAM,aAANA,MAAM,uBAANA,MAAM,CAAE6B,MAAM,MAAK,SAAS,gBAC3BhC,OAAA;oBAAMwB,SAAS,EAAC,gBAAgB;oBAAAC,QAAA,EAAC;kBAAO;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC,gBAE/C7B,OAAA;oBAAMwB,SAAS,EAAC,cAAc;oBAAAC,QAAA,EAAC;kBAAS;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM;gBAC/C;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACC,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACF,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAGN7B,OAAA;QAAKwB,SAAS,EAAC,4CAA4C;QAAAC,QAAA,eACzDzB,OAAA;UAAKwB,SAAS,EAAC,KAAK;UAAAC,QAAA,eAClBzB,OAAA;YAAKwB,SAAS,EAAC,mBAAmB;YAAAC,QAAA,gBAChCzB,OAAA;cAAKwB,SAAS,EAAC,eAAe;cAAAC,QAAA,eAC5BzB,OAAA,CAACR,MAAM;gBAACgC,SAAS,EAAC;cAAuB;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACzC,CAAC,eACN7B,OAAA;cAAKwB,SAAS,EAAC,iBAAiB;cAAAC,QAAA,eAC9BzB,OAAA;gBAAAyB,QAAA,gBACEzB,OAAA;kBAAIwB,SAAS,EAAC,4CAA4C;kBAAAC,QAAA,EAAC;gBAAW;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eAC3E7B,OAAA;kBAAIwB,SAAS,EAAC,mCAAmC;kBAAAC,QAAA,EAAE,CAAApB,OAAO,aAAPA,OAAO,uBAAPA,OAAO,CAAE4B,UAAU,KAAI;gBAAC;kBAAAP,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC/E;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACF,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAGN7B,OAAA;QAAKwB,SAAS,EAAC,4CAA4C;QAAAC,QAAA,eACzDzB,OAAA;UAAKwB,SAAS,EAAC,KAAK;UAAAC,QAAA,eAClBzB,OAAA;YAAKwB,SAAS,EAAC,mBAAmB;YAAAC,QAAA,gBAChCzB,OAAA;cAAKwB,SAAS,EAAC,eAAe;cAAAC,QAAA,eAC5BzB,OAAA,CAACN,WAAW;gBAAC8B,SAAS,EAAC;cAAwB;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC/C,CAAC,eACN7B,OAAA;cAAKwB,SAAS,EAAC,iBAAiB;cAAAC,QAAA,eAC9BzB,OAAA;gBAAAyB,QAAA,gBACEzB,OAAA;kBAAIwB,SAAS,EAAC,4CAA4C;kBAAAC,QAAA,EAAC;gBAAa;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eAC7E7B,OAAA;kBAAIwB,SAAS,EAAC,mCAAmC;kBAAAC,QAAA,EAAE,CAAApB,OAAO,aAAPA,OAAO,uBAAPA,OAAO,CAAE6B,YAAY,KAAI;gBAAC;kBAAAR,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACjF;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACF,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAGN7B,OAAA;QAAKwB,SAAS,EAAC,4CAA4C;QAAAC,QAAA,eACzDzB,OAAA;UAAKwB,SAAS,EAAC,KAAK;UAAAC,QAAA,eAClBzB,OAAA;YAAKwB,SAAS,EAAC,mBAAmB;YAAAC,QAAA,gBAChCzB,OAAA;cAAKwB,SAAS,EAAC,eAAe;cAAAC,QAAA,eAC5BzB,OAAA,CAACP,aAAa;gBAAC+B,SAAS,EAAC;cAAyB;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAClD,CAAC,eACN7B,OAAA;cAAKwB,SAAS,EAAC,iBAAiB;cAAAC,QAAA,eAC9BzB,OAAA;gBAAAyB,QAAA,gBACEzB,OAAA;kBAAIwB,SAAS,EAAC,4CAA4C;kBAAAC,QAAA,EAAC;gBAAc;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eAC9E7B,OAAA;kBAAIwB,SAAS,EAAC,mCAAmC;kBAAAC,QAAA,EAAE,CAAApB,OAAO,aAAPA,OAAO,uBAAPA,OAAO,CAAE8B,aAAa,KAAI;gBAAC;kBAAAT,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAClF;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACF,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGN7B,OAAA;MAAKwB,SAAS,EAAC,uCAAuC;MAAAC,QAAA,gBAEpDzB,OAAA;QAAKwB,SAAS,EAAC,gCAAgC;QAAAC,QAAA,gBAC7CzB,OAAA;UAAIwB,SAAS,EAAC,wCAAwC;UAAAC,QAAA,EAAC;QAAa;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACzE7B,OAAA;UAAKwB,SAAS,EAAC,WAAW;UAAAC,QAAA,EACvB,CAAApB,OAAO,aAAPA,OAAO,uBAAPA,OAAO,CAAE+B,WAAW,KAAIC,MAAM,CAACC,OAAO,CAACjC,OAAO,CAAC+B,WAAW,CAAC,CAACG,GAAG,CAAC,CAAC,CAACC,IAAI,EAAEC,KAAK,CAAC,kBAC7EzC,OAAA;YAAgBwB,SAAS,EAAC,mCAAmC;YAAAC,QAAA,gBAC3DzB,OAAA;cAAMwB,SAAS,EAAC,uBAAuB;cAAAC,QAAA,EAAEe,IAAI,CAACE,OAAO,CAAC,GAAG,EAAE,GAAG;YAAC;cAAAhB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eACvE7B,OAAA;cAAMwB,SAAS,EAAC,mCAAmC;cAAAC,QAAA,EAAEgB;YAAK;cAAAf,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC;UAAA,GAF1DW,IAAI;YAAAd,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAGT,CACN;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAGN7B,OAAA;QAAKwB,SAAS,EAAC,gCAAgC;QAAAC,QAAA,gBAC7CzB,OAAA;UAAIwB,SAAS,EAAC,wCAAwC;UAAAC,QAAA,EAAC;QAAiB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAC7E7B,OAAA;UAAKwB,SAAS,EAAC,WAAW;UAAAC,QAAA,EACvB,CAAApB,OAAO,aAAPA,OAAO,uBAAPA,OAAO,CAAEsC,eAAe,KAAIN,MAAM,CAACC,OAAO,CAACjC,OAAO,CAACsC,eAAe,CAAC,CAACJ,GAAG,CAAC,CAAC,CAACK,QAAQ,EAAEH,KAAK,CAAC,kBACzFzC,OAAA;YAAoBwB,SAAS,EAAC,mCAAmC;YAAAC,QAAA,gBAC/DzB,OAAA;cAAMwB,SAAS,EAAC,uBAAuB;cAAAC,QAAA,EAAEmB;YAAQ;cAAAlB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eACzD7B,OAAA;cAAMwB,SAAS,EAAC,mCAAmC;cAAAC,QAAA,EAAEgB;YAAK;cAAAf,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC;UAAA,GAF1De,QAAQ;YAAAlB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAGb,CACN;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGN7B,OAAA;MAAKwB,SAAS,EAAC,gCAAgC;MAAAC,QAAA,gBAC7CzB,OAAA;QAAIwB,SAAS,EAAC,wCAAwC;QAAAC,QAAA,EAAC;MAAkB;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eAC9E7B,OAAA;QAAKwB,SAAS,EAAC,uCAAuC;QAAAC,QAAA,gBACpDzB,OAAA;UAAAyB,QAAA,gBACEzB,OAAA;YAAIwB,SAAS,EAAC,mCAAmC;YAAAC,QAAA,EAAC;UAAO;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAC9D7B,OAAA;YAAIwB,SAAS,EAAC,4BAA4B;YAAAC,QAAA,EAAE,CAAAtB,MAAM,aAANA,MAAM,uBAANA,MAAM,CAAE0C,OAAO,KAAI;UAAK;YAAAnB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACvE,CAAC,eACN7B,OAAA;UAAAyB,QAAA,gBACEzB,OAAA;YAAIwB,SAAS,EAAC,mCAAmC;YAAAC,QAAA,EAAC;UAAc;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACrE7B,OAAA;YAAIwB,SAAS,EAAC,4BAA4B;YAAAC,QAAA,EAAE,CAAApB,OAAO,aAAPA,OAAO,uBAAPA,OAAO,CAAEyC,aAAa,KAAI;UAAK;YAAApB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC9E,CAAC,eACN7B,OAAA;UAAAyB,QAAA,gBACEzB,OAAA;YAAIwB,SAAS,EAAC,mCAAmC;YAAAC,QAAA,EAAC;UAAY;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACnE7B,OAAA;YAAIwB,SAAS,EAAC,4BAA4B;YAAAC,QAAA,EACvCpB,OAAO,aAAPA,OAAO,eAAPA,OAAO,CAAE0C,WAAW,GAAGlD,eAAe,CAACQ,OAAO,CAAC0C,WAAW,CAAC,GAAG;UAAK;YAAArB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAClE,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAAC3B,EAAA,CA5LID,SAAmB;AAAA+C,EAAA,GAAnB/C,SAAmB;AA8LzB,eAAeA,SAAS;AAAC,IAAA+C,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}