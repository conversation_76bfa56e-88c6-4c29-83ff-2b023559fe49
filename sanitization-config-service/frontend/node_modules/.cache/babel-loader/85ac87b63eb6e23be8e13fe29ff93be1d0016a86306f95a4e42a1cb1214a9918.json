{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"polygon\", {\n  points: \"6 3 20 12 6 21 6 3\",\n  key: \"1oa8hb\"\n}]];\nconst Play = createLucideIcon(\"play\", __iconNode);\nexport { __iconNode, Play as default };\n//# sourceMappingURL=play.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}