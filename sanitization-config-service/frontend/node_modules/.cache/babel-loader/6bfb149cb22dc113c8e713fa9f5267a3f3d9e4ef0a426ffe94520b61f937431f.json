{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"rect\", {\n  width: \"20\",\n  height: \"8\",\n  x: \"2\",\n  y: \"2\",\n  rx: \"2\",\n  ry: \"2\",\n  key: \"ngkwjq\"\n}], [\"rect\", {\n  width: \"20\",\n  height: \"8\",\n  x: \"2\",\n  y: \"14\",\n  rx: \"2\",\n  ry: \"2\",\n  key: \"iecqi9\"\n}], [\"line\", {\n  x1: \"6\",\n  x2: \"6.01\",\n  y1: \"6\",\n  y2: \"6\",\n  key: \"16zg32\"\n}], [\"line\", {\n  x1: \"6\",\n  x2: \"6.01\",\n  y1: \"18\",\n  y2: \"18\",\n  key: \"nzw8ys\"\n}]];\nconst Server = createLucideIcon(\"server\", __iconNode);\nexport { __iconNode, Server as default };\n//# sourceMappingURL=server.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}