{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"M12 22v-5.172a2 2 0 0 0-.586-1.414L9.5 13.5\",\n  key: \"1p17fm\"\n}], [\"path\", {\n  d: \"M14.5 14.5 12 17\",\n  key: \"dy5w4y\"\n}], [\"path\", {\n  d: \"M17 8.8A6 6 0 0 1 13.8 20H10A6.5 6.5 0 0 1 7 8a5 5 0 0 1 10 0z\",\n  key: \"6z7b3o\"\n}]];\nconst Shrub = createLucideIcon(\"shrub\", __iconNode);\nexport { __iconNode, Shrub as default };\n//# sourceMappingURL=shrub.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}