{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"circle\", {\n  cx: \"12\",\n  cy: \"12\",\n  r: \"10\",\n  key: \"1mglay\"\n}], [\"path\", {\n  d: \"M12 16v-4\",\n  key: \"1dtifu\"\n}], [\"path\", {\n  d: \"M12 8h.01\",\n  key: \"e9boi3\"\n}]];\nconst Info = createLucideIcon(\"info\", __iconNode);\nexport { __iconNode, Info as default };\n//# sourceMappingURL=info.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}