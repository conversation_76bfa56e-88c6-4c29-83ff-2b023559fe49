{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"M13 22H4a2 2 0 0 1 0-4h12\",\n  key: \"bt3f23\"\n}], [\"path\", {\n  d: \"M13.236 18a3 3 0 0 0-2.2-5\",\n  key: \"1tbvmo\"\n}], [\"path\", {\n  d: \"M16 9h.01\",\n  key: \"1bdo4e\"\n}], [\"path\", {\n  d: \"M16.82 3.94a3 3 0 1 1 3.237 4.868l1.815 2.587a1.5 1.5 0 0 1-1.5 2.1l-2.872-.453a3 3 0 0 0-3.5 3\",\n  key: \"9ch7kn\"\n}], [\"path\", {\n  d: \"M17 4.988a3 3 0 1 0-5.2 2.052A7 7 0 0 0 4 14.015 4 4 0 0 0 8 18\",\n  key: \"3s7e9i\"\n}]];\nconst Rat = createLucideIcon(\"rat\", __iconNode);\nexport { __iconNode, Rat as default };\n//# sourceMappingURL=rat.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}