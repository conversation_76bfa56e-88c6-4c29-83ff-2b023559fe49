{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"M12 2v20\",\n  key: \"t6zp3m\"\n}], [\"path\", {\n  d: \"M2 5h20\",\n  key: \"1fs1ex\"\n}], [\"path\", {\n  d: \"M3 3v2\",\n  key: \"9imdir\"\n}], [\"path\", {\n  d: \"M7 3v2\",\n  key: \"n0os7\"\n}], [\"path\", {\n  d: \"M17 3v2\",\n  key: \"1l2re6\"\n}], [\"path\", {\n  d: \"M21 3v2\",\n  key: \"1duuac\"\n}], [\"path\", {\n  d: \"m19 5-7 7-7-7\",\n  key: \"133zxf\"\n}]];\nconst UtilityPole = createLucideIcon(\"utility-pole\", __iconNode);\nexport { __iconNode, UtilityPole as default };\n//# sourceMappingURL=utility-pole.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}