{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"M11 20A7 7 0 0 1 9.8 6.1C15.5 5 17 4.48 19 2c1 2 2 4.18 2 8 0 5.5-4.78 10-10 10Z\",\n  key: \"nnexq3\"\n}], [\"path\", {\n  d: \"M2 21c0-3 1.85-5.36 5.08-6C9.5 14.52 12 13 13 12\",\n  key: \"mt58a7\"\n}]];\nconst Leaf = createLucideIcon(\"leaf\", __iconNode);\nexport { __iconNode, Leaf as default };\n//# sourceMappingURL=leaf.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}