{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"M8 18L12 22L16 18\",\n  key: \"cskvfv\"\n}], [\"path\", {\n  d: \"M12 2V22\",\n  key: \"r89rzk\"\n}]];\nconst MoveDown = createLucideIcon(\"move-down\", __iconNode);\nexport { __iconNode, MoveDown as default };\n//# sourceMappingURL=move-down.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}