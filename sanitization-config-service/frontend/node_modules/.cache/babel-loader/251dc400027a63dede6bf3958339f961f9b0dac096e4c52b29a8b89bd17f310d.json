{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"M9 18V5l12-2v13\",\n  key: \"1jmyc2\"\n}], [\"circle\", {\n  cx: \"6\",\n  cy: \"18\",\n  r: \"3\",\n  key: \"fqmcym\"\n}], [\"circle\", {\n  cx: \"18\",\n  cy: \"16\",\n  r: \"3\",\n  key: \"1hluhg\"\n}]];\nconst Music = createLucideIcon(\"music\", __iconNode);\nexport { __iconNode, Music as default };\n//# sourceMappingURL=music.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}