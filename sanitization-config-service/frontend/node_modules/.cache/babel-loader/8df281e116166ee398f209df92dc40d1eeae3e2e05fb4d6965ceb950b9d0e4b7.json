{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"circle\", {\n  cx: \"12\",\n  cy: \"17\",\n  r: \"1\",\n  key: \"1ixnty\"\n}], [\"path\", {\n  d: \"M21 7v6h-6\",\n  key: \"3ptur4\"\n}], [\"path\", {\n  d: \"M3 17a9 9 0 0 1 9-9 9 9 0 0 1 6 2.3l3 2.7\",\n  key: \"1kgawr\"\n}]];\nconst RedoDot = createLucideIcon(\"redo-dot\", __iconNode);\nexport { __iconNode, RedoDot as default };\n//# sourceMappingURL=redo-dot.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}