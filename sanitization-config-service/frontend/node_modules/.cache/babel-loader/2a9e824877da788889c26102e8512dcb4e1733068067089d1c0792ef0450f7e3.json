{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"M11.5 15H7a4 4 0 0 0-4 4v2\",\n  key: \"15lzij\"\n}], [\"path\", {\n  d: \"M21.378 16.626a1 1 0 0 0-3.004-3.004l-4.01 4.012a2 2 0 0 0-.506.854l-.837 2.87a.5.5 0 0 0 .62.62l2.87-.837a2 2 0 0 0 .854-.506z\",\n  key: \"1817ys\"\n}], [\"circle\", {\n  cx: \"10\",\n  cy: \"7\",\n  r: \"4\",\n  key: \"e45bow\"\n}]];\nconst UserPen = createLucideIcon(\"user-pen\", __iconNode);\nexport { __iconNode, UserPen as default };\n//# sourceMappingURL=user-pen.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}