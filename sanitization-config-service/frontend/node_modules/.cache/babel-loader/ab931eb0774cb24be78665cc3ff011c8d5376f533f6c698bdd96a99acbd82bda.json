{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"M9 10h.01\",\n  key: \"qbtxuw\"\n}], [\"path\", {\n  d: \"M15 10h.01\",\n  key: \"1qmjsl\"\n}], [\"path\", {\n  d: \"M12 2a8 8 0 0 0-8 8v12l3-3 2.5 2.5L12 19l2.5 2.5L17 19l3 3V10a8 8 0 0 0-8-8z\",\n  key: \"uwwb07\"\n}]];\nconst Ghost = createLucideIcon(\"ghost\", __iconNode);\nexport { __iconNode, Ghost as default };\n//# sourceMappingURL=ghost.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}