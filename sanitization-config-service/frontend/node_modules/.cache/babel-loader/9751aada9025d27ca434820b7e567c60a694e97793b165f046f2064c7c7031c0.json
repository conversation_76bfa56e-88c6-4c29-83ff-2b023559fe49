{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"line\", {\n  x1: \"10\",\n  x2: \"14\",\n  y1: \"2\",\n  y2: \"2\",\n  key: \"14vaq8\"\n}], [\"line\", {\n  x1: \"12\",\n  x2: \"15\",\n  y1: \"14\",\n  y2: \"11\",\n  key: \"17fdiu\"\n}], [\"circle\", {\n  cx: \"12\",\n  cy: \"14\",\n  r: \"8\",\n  key: \"1e1u0o\"\n}]];\nconst Timer = createLucideIcon(\"timer\", __iconNode);\nexport { __iconNode, Timer as default };\n//# sourceMappingURL=timer.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}