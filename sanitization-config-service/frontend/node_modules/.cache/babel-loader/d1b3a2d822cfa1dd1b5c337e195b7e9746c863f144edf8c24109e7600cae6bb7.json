{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"M20 18v-2a4 4 0 0 0-4-4H4\",\n  key: \"5vmcpk\"\n}], [\"path\", {\n  d: \"m9 17-5-5 5-5\",\n  key: \"nvlc11\"\n}]];\nconst Reply = createLucideIcon(\"reply\", __iconNode);\nexport { __iconNode, Reply as default };\n//# sourceMappingURL=reply.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}