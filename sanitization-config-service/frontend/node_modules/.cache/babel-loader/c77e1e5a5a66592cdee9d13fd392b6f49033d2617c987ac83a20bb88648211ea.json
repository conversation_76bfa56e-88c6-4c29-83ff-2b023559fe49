{"ast": null, "code": "var _jsxFileName = \"/Users/<USER>/dev/work/lab/javaagent/sanitization-config-service/frontend/src/components/SimpleRulesList.tsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { Shield, Search, RefreshCw, Power } from 'lucide-react';\nimport { sanitizationApi } from '../services/api';\nimport toast from 'react-hot-toast';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst SimpleRulesList = () => {\n  _s();\n  const [config, setConfig] = useState(null);\n  const [loading, setLoading] = useState(true);\n  const [searchTerm, setSearchTerm] = useState('');\n  const [toggling, setToggling] = useState(false);\n  const fetchRules = async () => {\n    try {\n      setLoading(true);\n      const response = await sanitizationApi.getRules();\n      setConfig(response);\n    } catch (error) {\n      toast.error('获取规则失败');\n      console.error('Error fetching rules:', error);\n    } finally {\n      setLoading(false);\n    }\n  };\n  const handleReload = async () => {\n    try {\n      await sanitizationApi.reloadRules();\n      toast.success('规则重载成功');\n      await fetchRules();\n    } catch (error) {\n      toast.error('规则重载失败');\n      console.error('Error reloading rules:', error);\n    }\n  };\n  const handleToggleGlobal = async () => {\n    if (!config) return;\n    try {\n      setToggling(true);\n      const newEnabled = !config.enabled;\n      const response = await sanitizationApi.toggleGlobalSwitch(newEnabled);\n      setConfig(prev => prev ? {\n        ...prev,\n        enabled: response.enabled\n      } : null);\n      toast.success(newEnabled ? '全局脱敏已启用' : '全局脱敏已禁用');\n    } catch (error) {\n      toast.error('切换全局开关失败');\n      console.error('Error toggling global switch:', error);\n    } finally {\n      setToggling(false);\n    }\n  };\n  useEffect(() => {\n    fetchRules();\n  }, []);\n  const filteredRules = (config === null || config === void 0 ? void 0 : config.rules.filter(rule => rule.name.toLowerCase().includes(searchTerm.toLowerCase()) || rule.description.toLowerCase().includes(searchTerm.toLowerCase()) || rule.id.toLowerCase().includes(searchTerm.toLowerCase()))) || [];\n  const getSeverityColor = severity => {\n    switch (severity) {\n      case 'CRITICAL':\n        return 'bg-red-100 text-red-800';\n      case 'HIGH':\n        return 'bg-orange-100 text-orange-800';\n      case 'MEDIUM':\n        return 'bg-yellow-100 text-yellow-800';\n      case 'LOW':\n        return 'bg-green-100 text-green-800';\n      default:\n        return 'bg-gray-100 text-gray-800';\n    }\n  };\n  const getTypeColor = type => {\n    switch (type) {\n      case 'FIELD_NAME':\n        return 'bg-blue-100 text-blue-800';\n      case 'PATTERN':\n        return 'bg-purple-100 text-purple-800';\n      case 'CONTENT_TYPE':\n        return 'bg-indigo-100 text-indigo-800';\n      default:\n        return 'bg-gray-100 text-gray-800';\n    }\n  };\n  if (loading) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"flex items-center justify-center h-64\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 87,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 86,\n      columnNumber: 7\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"min-h-screen bg-black text-white\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"sticky top-0 bg-black/80 backdrop-blur-md border-b border-gray-800 z-10\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"max-w-6xl mx-auto px-4 py-4\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-center justify-between\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-center space-x-4\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"p-2 bg-blue-600 rounded-full\",\n              children: /*#__PURE__*/_jsxDEV(Shield, {\n                className: \"h-6 w-6 text-white\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 100,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 99,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n                className: \"text-xl font-bold text-white\",\n                children: \"\\u6570\\u636E\\u8131\\u654F\\u89C4\\u5219\\u7BA1\\u7406\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 103,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex items-center space-x-2 mt-1\",\n                children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"text-gray-400 text-sm\",\n                  children: \"\\u7BA1\\u7406\\u548C\\u914D\\u7F6E\\u6570\\u636E\\u8131\\u654F\\u89C4\\u5219\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 107,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: `inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium ${config !== null && config !== void 0 && config.enabled ? 'bg-green-900/50 text-green-400 border border-green-800' : 'bg-red-900/50 text-red-400 border border-red-800'}`,\n                  children: config !== null && config !== void 0 && config.enabled ? '已启用' : '已禁用'\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 108,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 106,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 102,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 98,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-center space-x-3\",\n            children: [/*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: handleToggleGlobal,\n              disabled: toggling,\n              className: `inline-flex items-center px-4 py-2 rounded-full text-sm font-medium transition-all duration-200 hover:scale-105 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-offset-black ${config !== null && config !== void 0 && config.enabled ? 'bg-red-600 hover:bg-red-700 text-white focus:ring-red-500' : 'bg-green-600 hover:bg-green-700 text-white focus:ring-green-500'} ${toggling ? 'opacity-50 cursor-not-allowed scale-100' : ''}`,\n              children: [/*#__PURE__*/_jsxDEV(Power, {\n                className: \"h-4 w-4 mr-2\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 128,\n                columnNumber: 17\n              }, this), toggling ? '切换中...' : config !== null && config !== void 0 && config.enabled ? '禁用全局脱敏' : '启用全局脱敏']\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 119,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: handleReload,\n              className: \"inline-flex items-center px-4 py-2 rounded-full text-sm font-medium bg-blue-600 hover:bg-blue-700 text-white transition-all duration-200 hover:scale-105 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 focus:ring-offset-black\",\n              children: [/*#__PURE__*/_jsxDEV(RefreshCw, {\n                className: \"h-4 w-4 mr-2\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 135,\n                columnNumber: 17\n              }, this), \"\\u91CD\\u8F7D\\u89C4\\u5219\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 131,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 118,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 97,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 96,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 95,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"max-w-6xl mx-auto px-4 py-6\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"relative\",\n        children: [/*#__PURE__*/_jsxDEV(Search, {\n          className: \"absolute left-4 top-1/2 transform -translate-y-1/2 h-5 w-5 text-gray-500\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 146,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n          type: \"text\",\n          value: searchTerm,\n          onChange: e => setSearchTerm(e.target.value),\n          placeholder: \"\\u641C\\u7D22\\u89C4\\u5219\\u540D\\u79F0\\u3001\\u63CF\\u8FF0\\u6216ID...\",\n          className: \"w-full pl-12 pr-4 py-3 bg-gray-900 border border-gray-700 rounded-full text-white placeholder-gray-500 focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 147,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 145,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 144,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"max-w-6xl mx-auto px-4\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"mb-4\",\n        children: /*#__PURE__*/_jsxDEV(\"h3\", {\n          className: \"text-lg font-semibold text-white\",\n          children: [\"\\u89C4\\u5219\\u5217\\u8868 (\", filteredRules.length, \" / \", (config === null || config === void 0 ? void 0 : config.rules.length) || 0, \")\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 160,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 159,\n        columnNumber: 9\n      }, this), filteredRules.length === 0 ? /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"text-center py-16\",\n        children: [/*#__PURE__*/_jsxDEV(Shield, {\n          className: \"mx-auto h-16 w-16 text-gray-600\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 167,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n          className: \"mt-4 text-lg font-medium text-gray-300\",\n          children: \"\\u6CA1\\u6709\\u627E\\u5230\\u5339\\u914D\\u7684\\u89C4\\u5219\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 168,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"mt-2 text-sm text-gray-500\",\n          children: \"\\u8BF7\\u5C1D\\u8BD5\\u8C03\\u6574\\u641C\\u7D22\\u6761\\u4EF6\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 169,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 166,\n        columnNumber: 11\n      }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"space-y-4\",\n        children: filteredRules.map(rule => /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"bg-gray-900 border border-gray-800 rounded-2xl p-6 hover:border-gray-700 transition-all duration-200 hover:shadow-lg\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-start justify-between\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex-1\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex items-center space-x-3 mb-2\",\n                children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n                  className: \"text-lg font-semibold text-white\",\n                  children: rule.name\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 178,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: `inline-flex items-center px-3 py-1 rounded-full text-xs font-medium ${getSeverityColor(rule.severity)}`,\n                  children: rule.severity\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 179,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: `inline-flex items-center px-3 py-1 rounded-full text-xs font-medium ${getTypeColor(rule.type)}`,\n                  children: rule.type\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 182,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 177,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"text-sm text-gray-300 mb-2\",\n                children: rule.description\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 186,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"text-xs text-gray-500 mb-3\",\n                children: [\"ID: \", rule.id]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 187,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"space-y-3\",\n                children: [rule.fieldNames && rule.fieldNames.length > 0 && /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"text-sm\",\n                  children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"font-medium text-gray-400\",\n                    children: \"\\u5B57\\u6BB5:\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 192,\n                    columnNumber: 27\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"ml-2 text-gray-300\",\n                    children: rule.fieldNames.join(', ')\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 193,\n                    columnNumber: 27\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 191,\n                  columnNumber: 25\n                }, this), rule.pattern && /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"text-sm\",\n                  children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"font-medium text-gray-400\",\n                    children: \"\\u6A21\\u5F0F:\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 198,\n                    columnNumber: 27\n                  }, this), /*#__PURE__*/_jsxDEV(\"code\", {\n                    className: \"ml-2 text-xs bg-gray-800 text-blue-400 px-2 py-1 rounded-md border border-gray-700\",\n                    children: rule.pattern\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 199,\n                    columnNumber: 27\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 197,\n                  columnNumber: 25\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"text-sm\",\n                  children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"font-medium text-gray-400\",\n                    children: \"\\u63A9\\u7801:\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 203,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"code\", {\n                    className: \"ml-2 text-xs bg-gray-800 text-green-400 px-2 py-1 rounded-md border border-gray-700\",\n                    children: rule.maskValue\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 204,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 202,\n                  columnNumber: 23\n                }, this), rule.includeServices && rule.includeServices.length > 0 && /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"text-sm\",\n                  children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"font-medium text-gray-400\",\n                    children: \"\\u9002\\u7528\\u670D\\u52A1:\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 208,\n                    columnNumber: 27\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"ml-2 text-gray-300\",\n                    children: rule.includeServices.join(', ')\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 209,\n                    columnNumber: 27\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 207,\n                  columnNumber: 25\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 189,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 176,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"ml-6 flex flex-col items-end space-y-3\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex items-center\",\n                children: rule.enabled ? /*#__PURE__*/_jsxDEV(_Fragment, {\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"w-2 h-2 bg-green-400 rounded-full mr-2 animate-pulse\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 219,\n                    columnNumber: 27\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"text-sm font-medium text-green-400\",\n                    children: \"\\u5DF2\\u542F\\u7528\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 220,\n                    columnNumber: 27\n                  }, this)]\n                }, void 0, true) : /*#__PURE__*/_jsxDEV(_Fragment, {\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"w-2 h-2 bg-gray-600 rounded-full mr-2\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 224,\n                    columnNumber: 27\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"text-sm font-medium text-gray-500\",\n                    children: \"\\u5DF2\\u7981\\u7528\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 225,\n                    columnNumber: 27\n                  }, this)]\n                }, void 0, true)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 216,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"text-sm text-gray-500 bg-gray-800 px-2 py-1 rounded-md\",\n                children: [\"\\u4F18\\u5148\\u7EA7: \", rule.priority]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 229,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 215,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 175,\n            columnNumber: 17\n          }, this)\n        }, rule.id, false, {\n          fileName: _jsxFileName,\n          lineNumber: 174,\n          columnNumber: 15\n        }, this))\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 172,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 158,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 93,\n    columnNumber: 5\n  }, this);\n};\n_s(SimpleRulesList, \"uKt2Xt/PrPprlIBDO5rAiYCX0KA=\");\n_c = SimpleRulesList;\nexport default SimpleRulesList;\nvar _c;\n$RefreshReg$(_c, \"SimpleRulesList\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "Shield", "Search", "RefreshCw", "Power", "sanitizationApi", "toast", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "SimpleRulesList", "_s", "config", "setConfig", "loading", "setLoading", "searchTerm", "setSearchTerm", "toggling", "setToggling", "fetchRules", "response", "getRules", "error", "console", "handleReload", "reloadRules", "success", "handleToggleGlobal", "newEnabled", "enabled", "toggleGlobalSwitch", "prev", "filteredRules", "rules", "filter", "rule", "name", "toLowerCase", "includes", "description", "id", "getSeverityColor", "severity", "getTypeColor", "type", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "onClick", "disabled", "value", "onChange", "e", "target", "placeholder", "length", "map", "fieldNames", "join", "pattern", "maskValue", "includeServices", "priority", "_c", "$RefreshReg$"], "sources": ["/Users/<USER>/dev/work/lab/javaagent/sanitization-config-service/frontend/src/components/SimpleRulesList.tsx"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { Shield, Search, RefreshCw, Power } from 'lucide-react';\nimport { sanitizationApi } from '../services/api';\nimport { SanitizationConfig } from '../types';\nimport toast from 'react-hot-toast';\n\nconst SimpleRulesList: React.FC = () => {\n  const [config, setConfig] = useState<SanitizationConfig | null>(null);\n  const [loading, setLoading] = useState(true);\n  const [searchTerm, setSearchTerm] = useState('');\n  const [toggling, setToggling] = useState(false);\n\n  const fetchRules = async () => {\n    try {\n      setLoading(true);\n      const response = await sanitizationApi.getRules();\n      setConfig(response);\n    } catch (error) {\n      toast.error('获取规则失败');\n      console.error('Error fetching rules:', error);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const handleReload = async () => {\n    try {\n      await sanitizationApi.reloadRules();\n      toast.success('规则重载成功');\n      await fetchRules();\n    } catch (error) {\n      toast.error('规则重载失败');\n      console.error('Error reloading rules:', error);\n    }\n  };\n\n  const handleToggleGlobal = async () => {\n    if (!config) return;\n\n    try {\n      setToggling(true);\n      const newEnabled = !config.enabled;\n      const response = await sanitizationApi.toggleGlobalSwitch(newEnabled);\n\n      setConfig(prev => prev ? { ...prev, enabled: response.enabled } : null);\n      toast.success(newEnabled ? '全局脱敏已启用' : '全局脱敏已禁用');\n    } catch (error) {\n      toast.error('切换全局开关失败');\n      console.error('Error toggling global switch:', error);\n    } finally {\n      setToggling(false);\n    }\n  };\n\n  useEffect(() => {\n    fetchRules();\n  }, []);\n\n  const filteredRules = config?.rules.filter(rule =>\n    rule.name.toLowerCase().includes(searchTerm.toLowerCase()) ||\n    rule.description.toLowerCase().includes(searchTerm.toLowerCase()) ||\n    rule.id.toLowerCase().includes(searchTerm.toLowerCase())\n  ) || [];\n\n  const getSeverityColor = (severity: string) => {\n    switch (severity) {\n      case 'CRITICAL': return 'bg-red-100 text-red-800';\n      case 'HIGH': return 'bg-orange-100 text-orange-800';\n      case 'MEDIUM': return 'bg-yellow-100 text-yellow-800';\n      case 'LOW': return 'bg-green-100 text-green-800';\n      default: return 'bg-gray-100 text-gray-800';\n    }\n  };\n\n  const getTypeColor = (type: string) => {\n    switch (type) {\n      case 'FIELD_NAME': return 'bg-blue-100 text-blue-800';\n      case 'PATTERN': return 'bg-purple-100 text-purple-800';\n      case 'CONTENT_TYPE': return 'bg-indigo-100 text-indigo-800';\n      default: return 'bg-gray-100 text-gray-800';\n    }\n  };\n\n  if (loading) {\n    return (\n      <div className=\"flex items-center justify-center h-64\">\n        <div className=\"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600\"></div>\n      </div>\n    );\n  }\n\n  return (\n    <div className=\"min-h-screen bg-black text-white\">\n      {/* Header */}\n      <div className=\"sticky top-0 bg-black/80 backdrop-blur-md border-b border-gray-800 z-10\">\n        <div className=\"max-w-6xl mx-auto px-4 py-4\">\n          <div className=\"flex items-center justify-between\">\n            <div className=\"flex items-center space-x-4\">\n              <div className=\"p-2 bg-blue-600 rounded-full\">\n                <Shield className=\"h-6 w-6 text-white\" />\n              </div>\n              <div>\n                <h1 className=\"text-xl font-bold text-white\">\n                  数据脱敏规则管理\n                </h1>\n                <div className=\"flex items-center space-x-2 mt-1\">\n                  <span className=\"text-gray-400 text-sm\">管理和配置数据脱敏规则</span>\n                  <span className={`inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium ${\n                    config?.enabled\n                      ? 'bg-green-900/50 text-green-400 border border-green-800'\n                      : 'bg-red-900/50 text-red-400 border border-red-800'\n                  }`}>\n                    {config?.enabled ? '已启用' : '已禁用'}\n                  </span>\n                </div>\n              </div>\n            </div>\n            <div className=\"flex items-center space-x-3\">\n              <button\n                onClick={handleToggleGlobal}\n                disabled={toggling}\n                className={`inline-flex items-center px-4 py-2 rounded-full text-sm font-medium transition-all duration-200 hover:scale-105 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-offset-black ${\n                  config?.enabled\n                    ? 'bg-red-600 hover:bg-red-700 text-white focus:ring-red-500'\n                    : 'bg-green-600 hover:bg-green-700 text-white focus:ring-green-500'\n                } ${toggling ? 'opacity-50 cursor-not-allowed scale-100' : ''}`}\n              >\n                <Power className=\"h-4 w-4 mr-2\" />\n                {toggling ? '切换中...' : (config?.enabled ? '禁用全局脱敏' : '启用全局脱敏')}\n              </button>\n              <button\n                onClick={handleReload}\n                className=\"inline-flex items-center px-4 py-2 rounded-full text-sm font-medium bg-blue-600 hover:bg-blue-700 text-white transition-all duration-200 hover:scale-105 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 focus:ring-offset-black\"\n              >\n                <RefreshCw className=\"h-4 w-4 mr-2\" />\n                重载规则\n              </button>\n            </div>\n          </div>\n        </div>\n      </div>\n\n      {/* Search */}\n      <div className=\"max-w-6xl mx-auto px-4 py-6\">\n        <div className=\"relative\">\n          <Search className=\"absolute left-4 top-1/2 transform -translate-y-1/2 h-5 w-5 text-gray-500\" />\n          <input\n            type=\"text\"\n            value={searchTerm}\n            onChange={(e) => setSearchTerm(e.target.value)}\n            placeholder=\"搜索规则名称、描述或ID...\"\n            className=\"w-full pl-12 pr-4 py-3 bg-gray-900 border border-gray-700 rounded-full text-white placeholder-gray-500 focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200\"\n          />\n        </div>\n      </div>\n\n      {/* Rules List */}\n      <div className=\"max-w-6xl mx-auto px-4\">\n        <div className=\"mb-4\">\n          <h3 className=\"text-lg font-semibold text-white\">\n            规则列表 ({filteredRules.length} / {config?.rules.length || 0})\n          </h3>\n        </div>\n        \n        {filteredRules.length === 0 ? (\n          <div className=\"text-center py-16\">\n            <Shield className=\"mx-auto h-16 w-16 text-gray-600\" />\n            <h3 className=\"mt-4 text-lg font-medium text-gray-300\">没有找到匹配的规则</h3>\n            <p className=\"mt-2 text-sm text-gray-500\">请尝试调整搜索条件</p>\n          </div>\n        ) : (\n          <div className=\"space-y-4\">\n            {filteredRules.map((rule) => (\n              <div key={rule.id} className=\"bg-gray-900 border border-gray-800 rounded-2xl p-6 hover:border-gray-700 transition-all duration-200 hover:shadow-lg\">\n                <div className=\"flex items-start justify-between\">\n                  <div className=\"flex-1\">\n                    <div className=\"flex items-center space-x-3 mb-2\">\n                      <h4 className=\"text-lg font-semibold text-white\">{rule.name}</h4>\n                      <span className={`inline-flex items-center px-3 py-1 rounded-full text-xs font-medium ${getSeverityColor(rule.severity)}`}>\n                        {rule.severity}\n                      </span>\n                      <span className={`inline-flex items-center px-3 py-1 rounded-full text-xs font-medium ${getTypeColor(rule.type)}`}>\n                        {rule.type}\n                      </span>\n                    </div>\n                    <p className=\"text-sm text-gray-300 mb-2\">{rule.description}</p>\n                    <div className=\"text-xs text-gray-500 mb-3\">ID: {rule.id}</div>\n                    \n                    <div className=\"space-y-3\">\n                      {rule.fieldNames && rule.fieldNames.length > 0 && (\n                        <div className=\"text-sm\">\n                          <span className=\"font-medium text-gray-400\">字段:</span>\n                          <span className=\"ml-2 text-gray-300\">{rule.fieldNames.join(', ')}</span>\n                        </div>\n                      )}\n                      {rule.pattern && (\n                        <div className=\"text-sm\">\n                          <span className=\"font-medium text-gray-400\">模式:</span>\n                          <code className=\"ml-2 text-xs bg-gray-800 text-blue-400 px-2 py-1 rounded-md border border-gray-700\">{rule.pattern}</code>\n                        </div>\n                      )}\n                      <div className=\"text-sm\">\n                        <span className=\"font-medium text-gray-400\">掩码:</span>\n                        <code className=\"ml-2 text-xs bg-gray-800 text-green-400 px-2 py-1 rounded-md border border-gray-700\">{rule.maskValue}</code>\n                      </div>\n                      {rule.includeServices && rule.includeServices.length > 0 && (\n                        <div className=\"text-sm\">\n                          <span className=\"font-medium text-gray-400\">适用服务:</span>\n                          <span className=\"ml-2 text-gray-300\">{rule.includeServices.join(', ')}</span>\n                        </div>\n                      )}\n                    </div>\n                  </div>\n                  \n                  <div className=\"ml-6 flex flex-col items-end space-y-3\">\n                    <div className=\"flex items-center\">\n                      {rule.enabled ? (\n                        <>\n                          <div className=\"w-2 h-2 bg-green-400 rounded-full mr-2 animate-pulse\"></div>\n                          <span className=\"text-sm font-medium text-green-400\">已启用</span>\n                        </>\n                      ) : (\n                        <>\n                          <div className=\"w-2 h-2 bg-gray-600 rounded-full mr-2\"></div>\n                          <span className=\"text-sm font-medium text-gray-500\">已禁用</span>\n                        </>\n                      )}\n                    </div>\n                    <div className=\"text-sm text-gray-500 bg-gray-800 px-2 py-1 rounded-md\">\n                      优先级: {rule.priority}\n                    </div>\n                  </div>\n                </div>\n              </div>\n            ))}\n          </div>\n        )}\n      </div>\n    </div>\n  );\n};\n\nexport default SimpleRulesList;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,MAAM,EAAEC,MAAM,EAAEC,SAAS,EAAEC,KAAK,QAAQ,cAAc;AAC/D,SAASC,eAAe,QAAQ,iBAAiB;AAEjD,OAAOC,KAAK,MAAM,iBAAiB;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAEpC,MAAMC,eAAyB,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACtC,MAAM,CAACC,MAAM,EAAEC,SAAS,CAAC,GAAGf,QAAQ,CAA4B,IAAI,CAAC;EACrE,MAAM,CAACgB,OAAO,EAAEC,UAAU,CAAC,GAAGjB,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAM,CAACkB,UAAU,EAAEC,aAAa,CAAC,GAAGnB,QAAQ,CAAC,EAAE,CAAC;EAChD,MAAM,CAACoB,QAAQ,EAAEC,WAAW,CAAC,GAAGrB,QAAQ,CAAC,KAAK,CAAC;EAE/C,MAAMsB,UAAU,GAAG,MAAAA,CAAA,KAAY;IAC7B,IAAI;MACFL,UAAU,CAAC,IAAI,CAAC;MAChB,MAAMM,QAAQ,GAAG,MAAMjB,eAAe,CAACkB,QAAQ,CAAC,CAAC;MACjDT,SAAS,CAACQ,QAAQ,CAAC;IACrB,CAAC,CAAC,OAAOE,KAAK,EAAE;MACdlB,KAAK,CAACkB,KAAK,CAAC,QAAQ,CAAC;MACrBC,OAAO,CAACD,KAAK,CAAC,uBAAuB,EAAEA,KAAK,CAAC;IAC/C,CAAC,SAAS;MACRR,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAED,MAAMU,YAAY,GAAG,MAAAA,CAAA,KAAY;IAC/B,IAAI;MACF,MAAMrB,eAAe,CAACsB,WAAW,CAAC,CAAC;MACnCrB,KAAK,CAACsB,OAAO,CAAC,QAAQ,CAAC;MACvB,MAAMP,UAAU,CAAC,CAAC;IACpB,CAAC,CAAC,OAAOG,KAAK,EAAE;MACdlB,KAAK,CAACkB,KAAK,CAAC,QAAQ,CAAC;MACrBC,OAAO,CAACD,KAAK,CAAC,wBAAwB,EAAEA,KAAK,CAAC;IAChD;EACF,CAAC;EAED,MAAMK,kBAAkB,GAAG,MAAAA,CAAA,KAAY;IACrC,IAAI,CAAChB,MAAM,EAAE;IAEb,IAAI;MACFO,WAAW,CAAC,IAAI,CAAC;MACjB,MAAMU,UAAU,GAAG,CAACjB,MAAM,CAACkB,OAAO;MAClC,MAAMT,QAAQ,GAAG,MAAMjB,eAAe,CAAC2B,kBAAkB,CAACF,UAAU,CAAC;MAErEhB,SAAS,CAACmB,IAAI,IAAIA,IAAI,GAAG;QAAE,GAAGA,IAAI;QAAEF,OAAO,EAAET,QAAQ,CAACS;MAAQ,CAAC,GAAG,IAAI,CAAC;MACvEzB,KAAK,CAACsB,OAAO,CAACE,UAAU,GAAG,SAAS,GAAG,SAAS,CAAC;IACnD,CAAC,CAAC,OAAON,KAAK,EAAE;MACdlB,KAAK,CAACkB,KAAK,CAAC,UAAU,CAAC;MACvBC,OAAO,CAACD,KAAK,CAAC,+BAA+B,EAAEA,KAAK,CAAC;IACvD,CAAC,SAAS;MACRJ,WAAW,CAAC,KAAK,CAAC;IACpB;EACF,CAAC;EAEDpB,SAAS,CAAC,MAAM;IACdqB,UAAU,CAAC,CAAC;EACd,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMa,aAAa,GAAG,CAAArB,MAAM,aAANA,MAAM,uBAANA,MAAM,CAAEsB,KAAK,CAACC,MAAM,CAACC,IAAI,IAC7CA,IAAI,CAACC,IAAI,CAACC,WAAW,CAAC,CAAC,CAACC,QAAQ,CAACvB,UAAU,CAACsB,WAAW,CAAC,CAAC,CAAC,IAC1DF,IAAI,CAACI,WAAW,CAACF,WAAW,CAAC,CAAC,CAACC,QAAQ,CAACvB,UAAU,CAACsB,WAAW,CAAC,CAAC,CAAC,IACjEF,IAAI,CAACK,EAAE,CAACH,WAAW,CAAC,CAAC,CAACC,QAAQ,CAACvB,UAAU,CAACsB,WAAW,CAAC,CAAC,CACzD,CAAC,KAAI,EAAE;EAEP,MAAMI,gBAAgB,GAAIC,QAAgB,IAAK;IAC7C,QAAQA,QAAQ;MACd,KAAK,UAAU;QAAE,OAAO,yBAAyB;MACjD,KAAK,MAAM;QAAE,OAAO,+BAA+B;MACnD,KAAK,QAAQ;QAAE,OAAO,+BAA+B;MACrD,KAAK,KAAK;QAAE,OAAO,6BAA6B;MAChD;QAAS,OAAO,2BAA2B;IAC7C;EACF,CAAC;EAED,MAAMC,YAAY,GAAIC,IAAY,IAAK;IACrC,QAAQA,IAAI;MACV,KAAK,YAAY;QAAE,OAAO,2BAA2B;MACrD,KAAK,SAAS;QAAE,OAAO,+BAA+B;MACtD,KAAK,cAAc;QAAE,OAAO,+BAA+B;MAC3D;QAAS,OAAO,2BAA2B;IAC7C;EACF,CAAC;EAED,IAAI/B,OAAO,EAAE;IACX,oBACEP,OAAA;MAAKuC,SAAS,EAAC,uCAAuC;MAAAC,QAAA,eACpDxC,OAAA;QAAKuC,SAAS,EAAC;MAA8D;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACjF,CAAC;EAEV;EAEA,oBACE5C,OAAA;IAAKuC,SAAS,EAAC,kCAAkC;IAAAC,QAAA,gBAE/CxC,OAAA;MAAKuC,SAAS,EAAC,yEAAyE;MAAAC,QAAA,eACtFxC,OAAA;QAAKuC,SAAS,EAAC,6BAA6B;QAAAC,QAAA,eAC1CxC,OAAA;UAAKuC,SAAS,EAAC,mCAAmC;UAAAC,QAAA,gBAChDxC,OAAA;YAAKuC,SAAS,EAAC,6BAA6B;YAAAC,QAAA,gBAC1CxC,OAAA;cAAKuC,SAAS,EAAC,8BAA8B;cAAAC,QAAA,eAC3CxC,OAAA,CAACP,MAAM;gBAAC8C,SAAS,EAAC;cAAoB;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACtC,CAAC,eACN5C,OAAA;cAAAwC,QAAA,gBACExC,OAAA;gBAAIuC,SAAS,EAAC,8BAA8B;gBAAAC,QAAA,EAAC;cAE7C;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACL5C,OAAA;gBAAKuC,SAAS,EAAC,kCAAkC;gBAAAC,QAAA,gBAC/CxC,OAAA;kBAAMuC,SAAS,EAAC,uBAAuB;kBAAAC,QAAA,EAAC;gBAAW;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,eAC1D5C,OAAA;kBAAMuC,SAAS,EAAE,yEACflC,MAAM,aAANA,MAAM,eAANA,MAAM,CAAEkB,OAAO,GACX,wDAAwD,GACxD,kDAAkD,EACrD;kBAAAiB,QAAA,EACAnC,MAAM,aAANA,MAAM,eAANA,MAAM,CAAEkB,OAAO,GAAG,KAAK,GAAG;gBAAK;kBAAAkB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC5B,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACJ,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eACN5C,OAAA;YAAKuC,SAAS,EAAC,6BAA6B;YAAAC,QAAA,gBAC1CxC,OAAA;cACE6C,OAAO,EAAExB,kBAAmB;cAC5ByB,QAAQ,EAAEnC,QAAS;cACnB4B,SAAS,EAAE,+LACTlC,MAAM,aAANA,MAAM,eAANA,MAAM,CAAEkB,OAAO,GACX,2DAA2D,GAC3D,iEAAiE,IACnEZ,QAAQ,GAAG,yCAAyC,GAAG,EAAE,EAAG;cAAA6B,QAAA,gBAEhExC,OAAA,CAACJ,KAAK;gBAAC2C,SAAS,EAAC;cAAc;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,EACjCjC,QAAQ,GAAG,QAAQ,GAAIN,MAAM,aAANA,MAAM,eAANA,MAAM,CAAEkB,OAAO,GAAG,QAAQ,GAAG,QAAS;YAAA;cAAAkB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACxD,CAAC,eACT5C,OAAA;cACE6C,OAAO,EAAE3B,YAAa;cACtBqB,SAAS,EAAC,0PAA0P;cAAAC,QAAA,gBAEpQxC,OAAA,CAACL,SAAS;gBAAC4C,SAAS,EAAC;cAAc;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,4BAExC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGN5C,OAAA;MAAKuC,SAAS,EAAC,6BAA6B;MAAAC,QAAA,eAC1CxC,OAAA;QAAKuC,SAAS,EAAC,UAAU;QAAAC,QAAA,gBACvBxC,OAAA,CAACN,MAAM;UAAC6C,SAAS,EAAC;QAA0E;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eAC/F5C,OAAA;UACEsC,IAAI,EAAC,MAAM;UACXS,KAAK,EAAEtC,UAAW;UAClBuC,QAAQ,EAAGC,CAAC,IAAKvC,aAAa,CAACuC,CAAC,CAACC,MAAM,CAACH,KAAK,CAAE;UAC/CI,WAAW,EAAC,mEAAiB;UAC7BZ,SAAS,EAAC;QAA8L;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACzM,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGN5C,OAAA;MAAKuC,SAAS,EAAC,wBAAwB;MAAAC,QAAA,gBACrCxC,OAAA;QAAKuC,SAAS,EAAC,MAAM;QAAAC,QAAA,eACnBxC,OAAA;UAAIuC,SAAS,EAAC,kCAAkC;UAAAC,QAAA,GAAC,4BACzC,EAACd,aAAa,CAAC0B,MAAM,EAAC,KAAG,EAAC,CAAA/C,MAAM,aAANA,MAAM,uBAANA,MAAM,CAAEsB,KAAK,CAACyB,MAAM,KAAI,CAAC,EAAC,GAC5D;QAAA;UAAAX,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACF,CAAC,EAELlB,aAAa,CAAC0B,MAAM,KAAK,CAAC,gBACzBpD,OAAA;QAAKuC,SAAS,EAAC,mBAAmB;QAAAC,QAAA,gBAChCxC,OAAA,CAACP,MAAM;UAAC8C,SAAS,EAAC;QAAiC;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eACtD5C,OAAA;UAAIuC,SAAS,EAAC,wCAAwC;UAAAC,QAAA,EAAC;QAAS;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACrE5C,OAAA;UAAGuC,SAAS,EAAC,4BAA4B;UAAAC,QAAA,EAAC;QAAS;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACpD,CAAC,gBAEN5C,OAAA;QAAKuC,SAAS,EAAC,WAAW;QAAAC,QAAA,EACvBd,aAAa,CAAC2B,GAAG,CAAExB,IAAI,iBACtB7B,OAAA;UAAmBuC,SAAS,EAAC,sHAAsH;UAAAC,QAAA,eACjJxC,OAAA;YAAKuC,SAAS,EAAC,kCAAkC;YAAAC,QAAA,gBAC/CxC,OAAA;cAAKuC,SAAS,EAAC,QAAQ;cAAAC,QAAA,gBACrBxC,OAAA;gBAAKuC,SAAS,EAAC,kCAAkC;gBAAAC,QAAA,gBAC/CxC,OAAA;kBAAIuC,SAAS,EAAC,kCAAkC;kBAAAC,QAAA,EAAEX,IAAI,CAACC;gBAAI;kBAAAW,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC,eACjE5C,OAAA;kBAAMuC,SAAS,EAAE,uEAAuEJ,gBAAgB,CAACN,IAAI,CAACO,QAAQ,CAAC,EAAG;kBAAAI,QAAA,EACvHX,IAAI,CAACO;gBAAQ;kBAAAK,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACV,CAAC,eACP5C,OAAA;kBAAMuC,SAAS,EAAE,uEAAuEF,YAAY,CAACR,IAAI,CAACS,IAAI,CAAC,EAAG;kBAAAE,QAAA,EAC/GX,IAAI,CAACS;gBAAI;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACN,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACJ,CAAC,eACN5C,OAAA;gBAAGuC,SAAS,EAAC,4BAA4B;gBAAAC,QAAA,EAAEX,IAAI,CAACI;cAAW;gBAAAQ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eAChE5C,OAAA;gBAAKuC,SAAS,EAAC,4BAA4B;gBAAAC,QAAA,GAAC,MAAI,EAACX,IAAI,CAACK,EAAE;cAAA;gBAAAO,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eAE/D5C,OAAA;gBAAKuC,SAAS,EAAC,WAAW;gBAAAC,QAAA,GACvBX,IAAI,CAACyB,UAAU,IAAIzB,IAAI,CAACyB,UAAU,CAACF,MAAM,GAAG,CAAC,iBAC5CpD,OAAA;kBAAKuC,SAAS,EAAC,SAAS;kBAAAC,QAAA,gBACtBxC,OAAA;oBAAMuC,SAAS,EAAC,2BAA2B;oBAAAC,QAAA,EAAC;kBAAG;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC,eACtD5C,OAAA;oBAAMuC,SAAS,EAAC,oBAAoB;oBAAAC,QAAA,EAAEX,IAAI,CAACyB,UAAU,CAACC,IAAI,CAAC,IAAI;kBAAC;oBAAAd,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACrE,CACN,EACAf,IAAI,CAAC2B,OAAO,iBACXxD,OAAA;kBAAKuC,SAAS,EAAC,SAAS;kBAAAC,QAAA,gBACtBxC,OAAA;oBAAMuC,SAAS,EAAC,2BAA2B;oBAAAC,QAAA,EAAC;kBAAG;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC,eACtD5C,OAAA;oBAAMuC,SAAS,EAAC,oFAAoF;oBAAAC,QAAA,EAAEX,IAAI,CAAC2B;kBAAO;oBAAAf,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACvH,CACN,eACD5C,OAAA;kBAAKuC,SAAS,EAAC,SAAS;kBAAAC,QAAA,gBACtBxC,OAAA;oBAAMuC,SAAS,EAAC,2BAA2B;oBAAAC,QAAA,EAAC;kBAAG;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC,eACtD5C,OAAA;oBAAMuC,SAAS,EAAC,qFAAqF;oBAAAC,QAAA,EAAEX,IAAI,CAAC4B;kBAAS;oBAAAhB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC1H,CAAC,EACLf,IAAI,CAAC6B,eAAe,IAAI7B,IAAI,CAAC6B,eAAe,CAACN,MAAM,GAAG,CAAC,iBACtDpD,OAAA;kBAAKuC,SAAS,EAAC,SAAS;kBAAAC,QAAA,gBACtBxC,OAAA;oBAAMuC,SAAS,EAAC,2BAA2B;oBAAAC,QAAA,EAAC;kBAAK;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC,eACxD5C,OAAA;oBAAMuC,SAAS,EAAC,oBAAoB;oBAAAC,QAAA,EAAEX,IAAI,CAAC6B,eAAe,CAACH,IAAI,CAAC,IAAI;kBAAC;oBAAAd,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC1E,CACN;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACE,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eAEN5C,OAAA;cAAKuC,SAAS,EAAC,wCAAwC;cAAAC,QAAA,gBACrDxC,OAAA;gBAAKuC,SAAS,EAAC,mBAAmB;gBAAAC,QAAA,EAC/BX,IAAI,CAACN,OAAO,gBACXvB,OAAA,CAAAE,SAAA;kBAAAsC,QAAA,gBACExC,OAAA;oBAAKuC,SAAS,EAAC;kBAAsD;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC,eAC5E5C,OAAA;oBAAMuC,SAAS,EAAC,oCAAoC;oBAAAC,QAAA,EAAC;kBAAG;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC;gBAAA,eAC/D,CAAC,gBAEH5C,OAAA,CAAAE,SAAA;kBAAAsC,QAAA,gBACExC,OAAA;oBAAKuC,SAAS,EAAC;kBAAuC;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC,eAC7D5C,OAAA;oBAAMuC,SAAS,EAAC,mCAAmC;oBAAAC,QAAA,EAAC;kBAAG;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC;gBAAA,eAC9D;cACH;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACE,CAAC,eACN5C,OAAA;gBAAKuC,SAAS,EAAC,wDAAwD;gBAAAC,QAAA,GAAC,sBACjE,EAACX,IAAI,CAAC8B,QAAQ;cAAA;gBAAAlB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAChB,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC,GA3DEf,IAAI,CAACK,EAAE;UAAAO,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OA4DZ,CACN;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CACN;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAACxC,EAAA,CA1OID,eAAyB;AAAAyD,EAAA,GAAzBzD,eAAyB;AA4O/B,eAAeA,eAAe;AAAC,IAAAyD,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}