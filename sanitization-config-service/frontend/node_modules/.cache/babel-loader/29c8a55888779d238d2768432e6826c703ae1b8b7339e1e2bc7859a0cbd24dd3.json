{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"M22 2 2 22\",\n  key: \"y4kqgn\"\n}]];\nconst Slash = createLucideIcon(\"slash\", __iconNode);\nexport { __iconNode, Slash as default };\n//# sourceMappingURL=slash.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}