{"version": 3, "sources": ["../src/headless/index.ts", "../src/core/types.ts", "../src/core/utils.ts", "../src/core/store.ts", "../src/core/toast.ts", "../src/core/use-toaster.ts"], "sourcesContent": ["import { toast } from '../core/toast';\n\nexport type {\n  DefaultToastOptions,\n  IconTheme,\n  Renderable,\n  Toast,\n  ToasterProps,\n  ToastOptions,\n  ToastPosition,\n  ToastType,\n  ValueFunction,\n  ValueOrFunction,\n} from '../core/types';\n\nexport { resolveValue } from '../core/types';\nexport { useToaster } from '../core/use-toaster';\nexport { useStore as useToasterStore } from '../core/store';\n\nexport { toast };\nexport default toast;\n", "import { CSSProperties } from 'react';\n\nexport type ToastType = 'success' | 'error' | 'loading' | 'blank' | 'custom';\nexport type ToastPosition =\n  | 'top-left'\n  | 'top-center'\n  | 'top-right'\n  | 'bottom-left'\n  | 'bottom-center'\n  | 'bottom-right';\n\nexport type Renderable = React.ReactElement | string | null;\n\nexport interface IconTheme {\n  primary: string;\n  secondary: string;\n}\n\nexport type ValueFunction<TValue, TArg> = (arg: TArg) => TValue;\nexport type ValueOrFunction<TValue, TArg> =\n  | TValue\n  | ValueFunction<TValue, TArg>;\n\nconst isFunction = <TValue, TArg>(\n  valOrFunction: ValueOrFunction<TValue, TArg>\n): valOrFunction is ValueFunction<TValue, TArg> =>\n  typeof valOrFunction === 'function';\n\nexport const resolveValue = <TValue, TArg>(\n  valOrFunction: ValueOrFunction<TValue, TArg>,\n  arg: TArg\n): TValue => (isFunction(valOrFunction) ? valOrFunction(arg) : valOrFunction);\n\nexport interface Toast {\n  type: ToastType;\n  id: string;\n  message: ValueOrFunction<Renderable, Toast>;\n  icon?: Renderable;\n  duration?: number;\n  pauseDuration: number;\n  position?: ToastPosition;\n  removeDelay?: number;\n\n  ariaProps: {\n    role: 'status' | 'alert';\n    'aria-live': 'assertive' | 'off' | 'polite';\n  };\n\n  style?: CSSProperties;\n  className?: string;\n  iconTheme?: IconTheme;\n\n  createdAt: number;\n  visible: boolean;\n  dismissed: boolean;\n  height?: number;\n}\n\nexport type ToastOptions = Partial<\n  Pick<\n    Toast,\n    | 'id'\n    | 'icon'\n    | 'duration'\n    | 'ariaProps'\n    | 'className'\n    | 'style'\n    | 'position'\n    | 'iconTheme'\n    | 'removeDelay'\n  >\n>;\n\nexport type DefaultToastOptions = ToastOptions & {\n  [key in ToastType]?: ToastOptions;\n};\n\nexport interface ToasterProps {\n  position?: ToastPosition;\n  toastOptions?: DefaultToastOptions;\n  reverseOrder?: boolean;\n  gutter?: number;\n  containerStyle?: React.CSSProperties;\n  containerClassName?: string;\n  children?: (toast: Toast) => React.ReactElement;\n}\n\nexport interface ToastWrapperProps {\n  id: string;\n  className?: string;\n  style?: React.CSSProperties;\n  onHeightUpdate: (id: string, height: number) => void;\n  children?: React.ReactNode;\n}\n", "export const genId = (() => {\n  let count = 0;\n  return () => {\n    return (++count).toString();\n  };\n})();\n\nexport const prefersReducedMotion = (() => {\n  // Cache result\n  let shouldReduceMotion: boolean | undefined = undefined;\n\n  return () => {\n    if (shouldReduceMotion === undefined && typeof window !== 'undefined') {\n      const mediaQuery = matchMedia('(prefers-reduced-motion: reduce)');\n      shouldReduceMotion = !mediaQuery || mediaQuery.matches;\n    }\n    return shouldReduceMotion;\n  };\n})();\n", "import { useEffect, useState, useRef } from 'react';\nimport { DefaultToastOptions, Toast, ToastType } from './types';\n\nconst TOAST_LIMIT = 20;\n\nexport enum ActionType {\n  ADD_TOAST,\n  UPDATE_TOAST,\n  UPSERT_TOAST,\n  DISMISS_TOAST,\n  REMOVE_TOAST,\n  START_PAUSE,\n  END_PAUSE,\n}\n\ntype Action =\n  | {\n      type: ActionType.ADD_TOAST;\n      toast: Toast;\n    }\n  | {\n      type: ActionType.UPSERT_TOAST;\n      toast: Toast;\n    }\n  | {\n      type: ActionType.UPDATE_TOAST;\n      toast: Partial<Toast>;\n    }\n  | {\n      type: ActionType.DISMISS_TOAST;\n      toastId?: string;\n    }\n  | {\n      type: ActionType.REMOVE_TOAST;\n      toastId?: string;\n    }\n  | {\n      type: ActionType.START_PAUSE;\n      time: number;\n    }\n  | {\n      type: ActionType.END_PAUSE;\n      time: number;\n    };\n\ninterface State {\n  toasts: Toast[];\n  pausedAt: number | undefined;\n}\n\nexport const reducer = (state: State, action: Action): State => {\n  switch (action.type) {\n    case ActionType.ADD_TOAST:\n      return {\n        ...state,\n        toasts: [action.toast, ...state.toasts].slice(0, TOAST_LIMIT),\n      };\n\n    case ActionType.UPDATE_TOAST:\n      return {\n        ...state,\n        toasts: state.toasts.map((t) =>\n          t.id === action.toast.id ? { ...t, ...action.toast } : t\n        ),\n      };\n\n    case ActionType.UPSERT_TOAST:\n      const { toast } = action;\n      return reducer(state, {\n        type: state.toasts.find((t) => t.id === toast.id)\n          ? ActionType.UPDATE_TOAST\n          : ActionType.ADD_TOAST,\n        toast,\n      });\n\n    case ActionType.DISMISS_TOAST:\n      const { toastId } = action;\n\n      return {\n        ...state,\n        toasts: state.toasts.map((t) =>\n          t.id === toastId || toastId === undefined\n            ? {\n                ...t,\n                dismissed: true,\n                visible: false,\n              }\n            : t\n        ),\n      };\n    case ActionType.REMOVE_TOAST:\n      if (action.toastId === undefined) {\n        return {\n          ...state,\n          toasts: [],\n        };\n      }\n      return {\n        ...state,\n        toasts: state.toasts.filter((t) => t.id !== action.toastId),\n      };\n\n    case ActionType.START_PAUSE:\n      return {\n        ...state,\n        pausedAt: action.time,\n      };\n\n    case ActionType.END_PAUSE:\n      const diff = action.time - (state.pausedAt || 0);\n\n      return {\n        ...state,\n        pausedAt: undefined,\n        toasts: state.toasts.map((t) => ({\n          ...t,\n          pauseDuration: t.pauseDuration + diff,\n        })),\n      };\n  }\n};\n\nconst listeners: Array<(state: State) => void> = [];\n\nlet memoryState: State = { toasts: [], pausedAt: undefined };\n\nexport const dispatch = (action: Action) => {\n  memoryState = reducer(memoryState, action);\n  listeners.forEach((listener) => {\n    listener(memoryState);\n  });\n};\n\nexport const defaultTimeouts: {\n  [key in ToastType]: number;\n} = {\n  blank: 4000,\n  error: 4000,\n  success: 2000,\n  loading: Infinity,\n  custom: 4000,\n};\n\nexport const useStore = (toastOptions: DefaultToastOptions = {}): State => {\n  const [state, setState] = useState<State>(memoryState);\n  const initial = useRef(memoryState);\n\n  // TODO: Switch to useSyncExternalStore when targeting React 18+\n  useEffect(() => {\n    if (initial.current !== memoryState) {\n      setState(memoryState);\n    }\n    listeners.push(setState);\n    return () => {\n      const index = listeners.indexOf(setState);\n      if (index > -1) {\n        listeners.splice(index, 1);\n      }\n    };\n  }, []);\n\n  const mergedToasts = state.toasts.map((t) => ({\n    ...toastOptions,\n    ...toastOptions[t.type],\n    ...t,\n    removeDelay:\n      t.removeDelay ||\n      toastOptions[t.type]?.removeDelay ||\n      toastOptions?.removeDelay,\n    duration:\n      t.duration ||\n      toastOptions[t.type]?.duration ||\n      toastOptions?.duration ||\n      defaultTimeouts[t.type],\n    style: {\n      ...toastOptions.style,\n      ...toastOptions[t.type]?.style,\n      ...t.style,\n    },\n  }));\n\n  return {\n    ...state,\n    toasts: mergedToasts,\n  };\n};\n", "import {\n  Renderable,\n  Toast,\n  ToastOptions,\n  ToastType,\n  DefaultToastOptions,\n  ValueOrFunction,\n  resolveValue,\n} from './types';\nimport { genId } from './utils';\nimport { dispatch, ActionType } from './store';\n\ntype Message = ValueOrFunction<Renderable, Toast>;\n\ntype ToastHandler = (message: Message, options?: ToastOptions) => string;\n\nconst createToast = (\n  message: Message,\n  type: ToastType = 'blank',\n  opts?: ToastOptions\n): Toast => ({\n  createdAt: Date.now(),\n  visible: true,\n  dismissed: false,\n  type,\n  ariaProps: {\n    role: 'status',\n    'aria-live': 'polite',\n  },\n  message,\n  pauseDuration: 0,\n  ...opts,\n  id: opts?.id || genId(),\n});\n\nconst createHandler =\n  (type?: ToastType): ToastHandler =>\n  (message, options) => {\n    const toast = createToast(message, type, options);\n    dispatch({ type: ActionType.UPSERT_TOAST, toast });\n    return toast.id;\n  };\n\nconst toast = (message: Message, opts?: ToastOptions) =>\n  createHandler('blank')(message, opts);\n\ntoast.error = createHandler('error');\ntoast.success = createHandler('success');\ntoast.loading = createHandler('loading');\ntoast.custom = createHandler('custom');\n\ntoast.dismiss = (toastId?: string) => {\n  dispatch({\n    type: ActionType.DISMISS_TOAST,\n    toastId,\n  });\n};\n\ntoast.remove = (toastId?: string) =>\n  dispatch({ type: ActionType.REMOVE_TOAST, toastId });\n\ntoast.promise = <T>(\n  promise: Promise<T> | (() => Promise<T>),\n  msgs: {\n    loading: Renderable;\n    success?: ValueOrFunction<Renderable, T>;\n    error?: ValueOrFunction<Renderable, any>;\n  },\n  opts?: DefaultToastOptions\n) => {\n  const id = toast.loading(msgs.loading, { ...opts, ...opts?.loading });\n\n  if (typeof promise === 'function') {\n    promise = promise();\n  }\n\n  promise\n    .then((p) => {\n      const successMessage = msgs.success\n        ? resolveValue(msgs.success, p)\n        : undefined;\n\n      if (successMessage) {\n        toast.success(successMessage, {\n          id,\n          ...opts,\n          ...opts?.success,\n        });\n      } else {\n        toast.dismiss(id);\n      }\n      return p;\n    })\n    .catch((e) => {\n      const errorMessage = msgs.error ? resolveValue(msgs.error, e) : undefined;\n\n      if (errorMessage) {\n        toast.error(errorMessage, {\n          id,\n          ...opts,\n          ...opts?.error,\n        });\n      } else {\n        toast.dismiss(id);\n      }\n    });\n\n  return promise;\n};\n\nexport { toast };\n", "import { useEffect, useCallback } from 'react';\nimport { dispatch, ActionType, useStore } from './store';\nimport { toast } from './toast';\nimport { DefaultToastOptions, Toast, ToastPosition } from './types';\n\nconst updateHeight = (toastId: string, height: number) => {\n  dispatch({\n    type: ActionType.UPDATE_TOAST,\n    toast: { id: toastId, height },\n  });\n};\nconst startPause = () => {\n  dispatch({\n    type: ActionType.START_PAUSE,\n    time: Date.now(),\n  });\n};\n\nconst toastTimeouts = new Map<Toast['id'], ReturnType<typeof setTimeout>>();\n\nexport const REMOVE_DELAY = 1000;\n\nconst addToRemoveQueue = (toastId: string, removeDelay = REMOVE_DELAY) => {\n  if (toastTimeouts.has(toastId)) {\n    return;\n  }\n\n  const timeout = setTimeout(() => {\n    toastTimeouts.delete(toastId);\n    dispatch({\n      type: ActionType.REMOVE_TOAST,\n      toastId: toastId,\n    });\n  }, removeDelay);\n\n  toastTimeouts.set(toastId, timeout);\n};\n\nexport const useToaster = (toastOptions?: DefaultToastOptions) => {\n  const { toasts, pausedAt } = useStore(toastOptions);\n\n  useEffect(() => {\n    if (pausedAt) {\n      return;\n    }\n\n    const now = Date.now();\n    const timeouts = toasts.map((t) => {\n      if (t.duration === Infinity) {\n        return;\n      }\n\n      const durationLeft =\n        (t.duration || 0) + t.pauseDuration - (now - t.createdAt);\n\n      if (durationLeft < 0) {\n        if (t.visible) {\n          toast.dismiss(t.id);\n        }\n        return;\n      }\n      return setTimeout(() => toast.dismiss(t.id), durationLeft);\n    });\n\n    return () => {\n      timeouts.forEach((timeout) => timeout && clearTimeout(timeout));\n    };\n  }, [toasts, pausedAt]);\n\n  const endPause = useCallback(() => {\n    if (pausedAt) {\n      dispatch({ type: ActionType.END_PAUSE, time: Date.now() });\n    }\n  }, [pausedAt]);\n\n  const calculateOffset = useCallback(\n    (\n      toast: Toast,\n      opts?: {\n        reverseOrder?: boolean;\n        gutter?: number;\n        defaultPosition?: ToastPosition;\n      }\n    ) => {\n      const { reverseOrder = false, gutter = 8, defaultPosition } = opts || {};\n\n      const relevantToasts = toasts.filter(\n        (t) =>\n          (t.position || defaultPosition) ===\n            (toast.position || defaultPosition) && t.height\n      );\n      const toastIndex = relevantToasts.findIndex((t) => t.id === toast.id);\n      const toastsBefore = relevantToasts.filter(\n        (toast, i) => i < toastIndex && toast.visible\n      ).length;\n\n      const offset = relevantToasts\n        .filter((t) => t.visible)\n        .slice(...(reverseOrder ? [toastsBefore + 1] : [0, toastsBefore]))\n        .reduce((acc, t) => acc + (t.height || 0) + gutter, 0);\n\n      return offset;\n    },\n    [toasts]\n  );\n\n  useEffect(() => {\n    // Add dismissed toasts to remove queue\n    toasts.forEach((toast) => {\n      if (toast.dismissed) {\n        addToRemoveQueue(toast.id, toast.removeDelay);\n      } else {\n        // If toast becomes visible again, remove it from the queue\n        const timeout = toastTimeouts.get(toast.id);\n        if (timeout) {\n          clearTimeout(timeout);\n          toastTimeouts.delete(toast.id);\n        }\n      }\n    });\n  }, [toasts]);\n\n  return {\n    toasts,\n    handlers: {\n      updateHeight,\n      startPause,\n      endPause,\n      calculateOffset,\n    },\n  };\n};\n"], "mappings": "yaAAA,IAAAA,EAAA,GAAAC,EAAAD,EAAA,aAAAE,EAAA,iBAAAC,EAAA,UAAAC,EAAA,eAAAC,EAAA,oBAAAC,IAAA,eAAAC,EAAAP,GCuBA,IAAMQ,EACJC,GAEA,OAAOA,GAAkB,WAEdC,EAAe,CAC1BD,EACAE,IACYH,EAAWC,CAAa,EAAIA,EAAcE,CAAG,EAAIF,EC/BxD,IAAMG,GAAS,IAAM,CAC1B,IAAIC,EAAQ,EACZ,MAAO,KACG,EAAEA,GAAO,SAAS,CAE9B,GAAG,EAEUC,GAAwB,IAAM,CAEzC,IAAIC,EAEJ,MAAO,IAAM,CACX,GAAIA,IAAuB,QAAa,OAAO,OAAW,IAAa,CACrE,IAAMC,EAAa,WAAW,kCAAkC,EAChED,EAAqB,CAACC,GAAcA,EAAW,QAEjD,OAAOD,CACT,CACF,GAAG,EClBH,IAAAE,EAA4C,iBAGtCC,EAAc,GA+Cb,IAAMC,EAAU,CAACC,EAAcC,IAA0B,CAC9D,OAAQA,EAAO,KAAM,CACnB,IAAK,GACH,MAAO,CACL,GAAGD,EACH,OAAQ,CAACC,EAAO,MAAO,GAAGD,EAAM,MAAM,EAAE,MAAM,EAAGE,CAAW,CAC9D,EAEF,IAAK,GACH,MAAO,CACL,GAAGF,EACH,OAAQA,EAAM,OAAO,IAAKG,GACxBA,EAAE,KAAOF,EAAO,MAAM,GAAK,CAAE,GAAGE,EAAG,GAAGF,EAAO,KAAM,EAAIE,CACzD,CACF,EAEF,IAAK,GACH,GAAM,CAAE,MAAAC,CAAM,EAAIH,EAClB,OAAOF,EAAQC,EAAO,CACpB,KAAMA,EAAM,OAAO,KAAMG,GAAMA,EAAE,KAAOC,EAAM,EAAE,EAC5C,EACA,EACJ,MAAAA,CACF,CAAC,EAEH,IAAK,GACH,GAAM,CAAE,QAAAC,CAAQ,EAAIJ,EAEpB,MAAO,CACL,GAAGD,EACH,OAAQA,EAAM,OAAO,IAAKG,GACxBA,EAAE,KAAOE,GAAWA,IAAY,OAC5B,CACE,GAAGF,EACH,UAAW,GACX,QAAS,EACX,EACAA,CACN,CACF,EACF,IAAK,GACH,OAAIF,EAAO,UAAY,OACd,CACL,GAAGD,EACH,OAAQ,CAAC,CACX,EAEK,CACL,GAAGA,EACH,OAAQA,EAAM,OAAO,OAAQG,GAAMA,EAAE,KAAOF,EAAO,OAAO,CAC5D,EAEF,IAAK,GACH,MAAO,CACL,GAAGD,EACH,SAAUC,EAAO,IACnB,EAEF,IAAK,GACH,IAAMK,EAAOL,EAAO,MAAQD,EAAM,UAAY,GAE9C,MAAO,CACL,GAAGA,EACH,SAAU,OACV,OAAQA,EAAM,OAAO,IAAKG,IAAO,CAC/B,GAAGA,EACH,cAAeA,EAAE,cAAgBG,CACnC,EAAE,CACJ,CACJ,CACF,EAEMC,EAA2C,CAAC,EAE9CC,EAAqB,CAAE,OAAQ,CAAC,EAAG,SAAU,MAAU,EAE9CC,EAAYR,GAAmB,CAC1CO,EAAcT,EAAQS,EAAaP,CAAM,EACzCM,EAAU,QAASG,GAAa,CAC9BA,EAASF,CAAW,CACtB,CAAC,CACH,EAEaG,EAET,CACF,MAAO,IACP,MAAO,IACP,QAAS,IACT,QAAS,IACT,OAAQ,GACV,EAEaC,EAAW,CAACC,EAAoC,CAAC,IAAa,CACzE,GAAM,CAACb,EAAOc,CAAQ,KAAI,YAAgBN,CAAW,EAC/CO,KAAU,UAAOP,CAAW,KAGlC,aAAU,KACJO,EAAQ,UAAYP,GACtBM,EAASN,CAAW,EAEtBD,EAAU,KAAKO,CAAQ,EAChB,IAAM,CACX,IAAME,EAAQT,EAAU,QAAQO,CAAQ,EACpCE,EAAQ,IACVT,EAAU,OAAOS,EAAO,CAAC,CAE7B,GACC,CAAC,CAAC,EAEL,IAAMC,EAAejB,EAAM,OAAO,IAAKG,GAAG,CAjK5C,IAAAe,EAAAC,EAAAC,EAiKgD,OAC5C,GAAGP,EACH,GAAGA,EAAaV,EAAE,IAAI,EACtB,GAAGA,EACH,YACEA,EAAE,eACFe,EAAAL,EAAaV,EAAE,IAAI,IAAnB,YAAAe,EAAsB,eACtBL,GAAA,YAAAA,EAAc,aAChB,SACEV,EAAE,YACFgB,EAAAN,EAAaV,EAAE,IAAI,IAAnB,YAAAgB,EAAsB,YACtBN,GAAA,YAAAA,EAAc,WACdF,EAAgBR,EAAE,IAAI,EACxB,MAAO,CACL,GAAGU,EAAa,MAChB,IAAGO,EAAAP,EAAaV,EAAE,IAAI,IAAnB,YAAAiB,EAAsB,MACzB,GAAGjB,EAAE,KACP,CACF,EAAE,EAEF,MAAO,CACL,GAAGH,EACH,OAAQiB,CACV,CACF,ECzKA,IAAMI,EAAc,CAClBC,EACAC,EAAkB,QAClBC,KACW,CACX,UAAW,KAAK,IAAI,EACpB,QAAS,GACT,UAAW,GACX,KAAAD,EACA,UAAW,CACT,KAAM,SACN,YAAa,QACf,EACA,QAAAD,EACA,cAAe,EACf,GAAGE,EACH,IAAIA,GAAA,YAAAA,EAAM,KAAMC,EAAM,CACxB,GAEMC,EACHH,GACD,CAACD,EAASK,IAAY,CACpB,IAAMC,EAAQP,EAAYC,EAASC,EAAMI,CAAO,EAChD,OAAAE,EAAS,CAAE,OAA+B,MAAAD,CAAM,CAAC,EAC1CA,EAAM,EACf,EAEIA,EAAQ,CAACN,EAAkBE,IAC/BE,EAAc,OAAO,EAAEJ,EAASE,CAAI,EAEtCI,EAAM,MAAQF,EAAc,OAAO,EACnCE,EAAM,QAAUF,EAAc,SAAS,EACvCE,EAAM,QAAUF,EAAc,SAAS,EACvCE,EAAM,OAASF,EAAc,QAAQ,EAErCE,EAAM,QAAWE,GAAqB,CACpCD,EAAS,CACP,OACA,QAAAC,CACF,CAAC,CACH,EAEAF,EAAM,OAAUE,GACdD,EAAS,CAAE,OAA+B,QAAAC,CAAQ,CAAC,EAErDF,EAAM,QAAU,CACdG,EACAC,EAKAR,IACG,CACH,IAAMS,EAAKL,EAAM,QAAQI,EAAK,QAAS,CAAE,GAAGR,EAAM,GAAGA,GAAA,YAAAA,EAAM,OAAQ,CAAC,EAEpE,OAAI,OAAOO,GAAY,aACrBA,EAAUA,EAAQ,GAGpBA,EACG,KAAMG,GAAM,CACX,IAAMC,EAAiBH,EAAK,QACxBI,EAAaJ,EAAK,QAASE,CAAC,EAC5B,OAEJ,OAAIC,EACFP,EAAM,QAAQO,EAAgB,CAC5B,GAAAF,EACA,GAAGT,EACH,GAAGA,GAAA,YAAAA,EAAM,OACX,CAAC,EAEDI,EAAM,QAAQK,CAAE,EAEXC,CACT,CAAC,EACA,MAAOG,GAAM,CACZ,IAAMC,EAAeN,EAAK,MAAQI,EAAaJ,EAAK,MAAOK,CAAC,EAAI,OAE5DC,EACFV,EAAM,MAAMU,EAAc,CACxB,GAAAL,EACA,GAAGT,EACH,GAAGA,GAAA,YAAAA,EAAM,KACX,CAAC,EAEDI,EAAM,QAAQK,CAAE,CAEpB,CAAC,EAEIF,CACT,EC5GA,IAAAQ,EAAuC,iBAKvC,IAAMC,EAAe,CAACC,EAAiBC,IAAmB,CACxDC,EAAS,CACP,OACA,MAAO,CAAE,GAAIF,EAAS,OAAAC,CAAO,CAC/B,CAAC,CACH,EACME,EAAa,IAAM,CACvBD,EAAS,CACP,OACA,KAAM,KAAK,IAAI,CACjB,CAAC,CACH,EAEME,EAAgB,IAAI,IAEbC,EAAe,IAEtBC,EAAmB,CAACN,EAAiBO,EAAcF,IAAiB,CACxE,GAAID,EAAc,IAAIJ,CAAO,EAC3B,OAGF,IAAMQ,EAAU,WAAW,IAAM,CAC/BJ,EAAc,OAAOJ,CAAO,EAC5BE,EAAS,CACP,OACA,QAASF,CACX,CAAC,CACH,EAAGO,CAAW,EAEdH,EAAc,IAAIJ,EAASQ,CAAO,CACpC,EAEaC,EAAcC,GAAuC,CAChE,GAAM,CAAE,OAAAC,EAAQ,SAAAC,CAAS,EAAIC,EAASH,CAAY,KAElD,aAAU,IAAM,CACd,GAAIE,EACF,OAGF,IAAME,EAAM,KAAK,IAAI,EACfC,EAAWJ,EAAO,IAAKK,GAAM,CACjC,GAAIA,EAAE,WAAa,IACjB,OAGF,IAAMC,GACHD,EAAE,UAAY,GAAKA,EAAE,eAAiBF,EAAME,EAAE,WAEjD,GAAIC,EAAe,EAAG,CAChBD,EAAE,SACJE,EAAM,QAAQF,EAAE,EAAE,EAEpB,OAEF,OAAO,WAAW,IAAME,EAAM,QAAQF,EAAE,EAAE,EAAGC,CAAY,CAC3D,CAAC,EAED,MAAO,IAAM,CACXF,EAAS,QAASP,GAAYA,GAAW,aAAaA,CAAO,CAAC,CAChE,CACF,EAAG,CAACG,EAAQC,CAAQ,CAAC,EAErB,IAAMO,KAAW,eAAY,IAAM,CAC7BP,GACFV,EAAS,CAAE,OAA4B,KAAM,KAAK,IAAI,CAAE,CAAC,CAE7D,EAAG,CAACU,CAAQ,CAAC,EAEPQ,KAAkB,eACtB,CACEF,EACAG,IAKG,CACH,GAAM,CAAE,aAAAC,EAAe,GAAO,OAAAC,EAAS,EAAG,gBAAAC,CAAgB,EAAIH,GAAQ,CAAC,EAEjEI,EAAiBd,EAAO,OAC3BK,IACEA,EAAE,UAAYQ,MACZN,EAAM,UAAYM,IAAoBR,EAAE,MAC/C,EACMU,EAAaD,EAAe,UAAWT,GAAMA,EAAE,KAAOE,EAAM,EAAE,EAC9DS,EAAeF,EAAe,OAClC,CAACP,EAAOU,IAAMA,EAAIF,GAAcR,EAAM,OACxC,EAAE,OAOF,OALeO,EACZ,OAAQT,GAAMA,EAAE,OAAO,EACvB,MAAM,GAAIM,EAAe,CAACK,EAAe,CAAC,EAAI,CAAC,EAAGA,CAAY,CAAE,EAChE,OAAO,CAACE,EAAKb,IAAMa,GAAOb,EAAE,QAAU,GAAKO,EAAQ,CAAC,CAGzD,EACA,CAACZ,CAAM,CACT,EAEA,sBAAU,IAAM,CAEdA,EAAO,QAASO,GAAU,CACxB,GAAIA,EAAM,UACRZ,EAAiBY,EAAM,GAAIA,EAAM,WAAW,MACvC,CAEL,IAAMV,EAAUJ,EAAc,IAAIc,EAAM,EAAE,EACtCV,IACF,aAAaA,CAAO,EACpBJ,EAAc,OAAOc,EAAM,EAAE,GAGnC,CAAC,CACH,EAAG,CAACP,CAAM,CAAC,EAEJ,CACL,OAAAA,EACA,SAAU,CACR,aAAAZ,EACA,WAAAI,EACA,SAAAgB,EACA,gBAAAC,CACF,CACF,CACF,EL/GA,IAAOU,EAAQC", "names": ["headless_exports", "__export", "headless_default", "resolveValue", "toast", "useToaster", "useStore", "__toCommonJS", "isFunction", "valOrFunction", "resolveValue", "arg", "genId", "count", "prefersReducedMotion", "shouldReduceMotion", "mediaQuery", "import_react", "TOAST_LIMIT", "reducer", "state", "action", "TOAST_LIMIT", "t", "toast", "toastId", "diff", "listeners", "memoryState", "dispatch", "listener", "defaultTimeouts", "useStore", "toastOptions", "setState", "initial", "index", "mergedToasts", "_a", "_b", "_c", "createToast", "message", "type", "opts", "genId", "createHandler", "options", "toast", "dispatch", "toastId", "promise", "msgs", "id", "p", "successMessage", "resolveValue", "e", "errorMessage", "import_react", "updateHeight", "toastId", "height", "dispatch", "startPause", "toastTimeouts", "REMOVE_DELAY", "addToRemoveQueue", "<PERSON><PERSON><PERSON><PERSON>", "timeout", "useToaster", "toastOptions", "toasts", "pausedAt", "useStore", "now", "timeouts", "t", "durationLeft", "toast", "endPause", "calculateOffset", "opts", "reverseOrder", "gutter", "defaultPosition", "relevantToasts", "toastIndex", "toastsBefore", "i", "acc", "headless_default", "toast"]}