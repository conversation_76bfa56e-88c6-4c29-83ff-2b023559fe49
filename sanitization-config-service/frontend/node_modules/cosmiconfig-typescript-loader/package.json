{"name": "cosmiconfig-typescript-loader", "version": "1.0.9", "description": "TypeScript loader for cosmiconfig", "author": "<PERSON> <<EMAIL>>", "license": "MIT", "main": "dist/index.js", "types": "dist/index.d.ts", "files": ["dist/**/*"], "homepage": "https://github.com/Codex-/cosmiconfig-typescript-loader#readme", "repository": {"type": "git", "url": "git+https://github.com/Codex-/cosmiconfig-typescript-loader.git"}, "bugs": {"url": "https://github.com/Codex-/cosmiconfig-typescript-loader/issues"}, "scripts": {"build": "tsc", "format:check": "prettier --check \"{**/*,*}.{js,ts}\"", "format:write": "npm run format:check -- --write", "lint": "eslint --ext \".js,.ts\" .", "lint:fix": "npm run lint -- --fix", "release": "release-it", "test": "jest"}, "engines": {"node": ">=12", "npm": ">=6"}, "peerDependencies": {"@types/node": "*", "cosmiconfig": ">=7", "typescript": ">=3"}, "dependencies": {"cosmiconfig": "^7", "ts-node": "^10.7.0"}, "devDependencies": {"@types/jest": "^27.4.1", "@typescript-eslint/eslint-plugin": "^5.18.0", "eslint": "^8.12.0", "eslint-config-airbnb-typescript": "^16.2.0", "eslint-config-prettier": "^8.5.0", "eslint-plugin-import": "^2.25.4", "eslint-plugin-prettier": "^4.0.0", "jest": "^27.5.1", "prettier": "^2.6.2", "release-it": "^14.14.0", "ts-jest": "^27.1.4", "typescript": "^4.6.3"}, "keywords": ["cosmiconfig", "typescript"]}