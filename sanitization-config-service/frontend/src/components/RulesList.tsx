import React, { useState, useEffect } from 'react';
import { Search, RefreshCw, Download, Shield } from 'lucide-react';
import { sanitizationApi } from '../services/api';
import { SanitizationConfig, SanitizationRule, RuleType, SeverityLevel } from '../types';
import { getSeverityBadgeColor, getRuleTypeColor, getRuleTypeIcon, truncateText, downloadJson } from '../utils';
import toast from 'react-hot-toast';

const RulesList: React.FC = () => {
  const [config, setConfig] = useState<SanitizationConfig | null>(null);
  const [filteredRules, setFilteredRules] = useState<SanitizationRule[]>([]);
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedType, setSelectedType] = useState<RuleType | 'ALL'>('ALL');
  const [selectedSeverity, setSelectedSeverity] = useState<SeverityLevel | 'ALL'>('ALL');
  const [showEnabledOnly, setShowEnabledOnly] = useState(false);
  const [serviceName, setServiceName] = useState('');

  const fetchRules = async () => {
    try {
      const data = await sanitizationApi.getRules(serviceName || undefined);
      setConfig(data);
      setFilteredRules(data.rules);
    } catch (error) {
      console.error('Failed to fetch rules:', error);
      toast.error('Failed to load rules');
    } finally {
      setLoading(false);
      setRefreshing(false);
    }
  };

  const handleRefresh = async () => {
    setRefreshing(true);
    await fetchRules();
  };

  const handleReloadConfig = async () => {
    try {
      await sanitizationApi.reloadRules();
      toast.success('Configuration reloaded successfully');
      await fetchRules();
    } catch (error) {
      console.error('Failed to reload config:', error);
      toast.error('Failed to reload configuration');
    }
  };

  const handleExport = () => {
    if (config) {
      downloadJson(config, `sanitization-config-${Date.now()}.json`);
      toast.success('Configuration exported successfully');
    }
  };

  const filterRules = () => {
    if (!config) return;

    let filtered = config.rules;

    // Filter by search term
    if (searchTerm) {
      filtered = filtered.filter(rule =>
        rule.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
        rule.description.toLowerCase().includes(searchTerm.toLowerCase()) ||
        rule.id.toLowerCase().includes(searchTerm.toLowerCase())
      );
    }

    // Filter by type
    if (selectedType !== 'ALL') {
      filtered = filtered.filter(rule => rule.type === selectedType);
    }

    // Filter by severity
    if (selectedSeverity !== 'ALL') {
      filtered = filtered.filter(rule => rule.severity === selectedSeverity);
    }

    // Filter by enabled status
    if (showEnabledOnly) {
      filtered = filtered.filter(rule => rule.enabled);
    }

    setFilteredRules(filtered);
  };

  useEffect(() => {
    fetchRules();
  }, [serviceName]); // eslint-disable-line react-hooks/exhaustive-deps

  useEffect(() => {
    filterRules();
  }, [config, searchTerm, selectedType, selectedSeverity, showEnabledOnly]); // eslint-disable-line react-hooks/exhaustive-deps

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary-600"></div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="space-y-8 fade-in">
          {/* Header */}
          <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
            <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between">
              <div className="mb-4 sm:mb-0">
                <h1 className="text-3xl font-bold text-gray-900 mb-2">
                  <Shield className="inline-block h-8 w-8 text-primary-600 mr-3" />
                  脱敏规则管理
                </h1>
                <p className="text-gray-600 text-lg">配置和管理数据脱敏规则</p>
              </div>
              <div className="flex flex-wrap items-center gap-3">
                <button
                  onClick={handleExport}
                  className="btn btn-secondary"
                >
                  <Download className="h-4 w-4 mr-2" />
                  导出配置
                </button>
                <button
                  onClick={handleReloadConfig}
                  className="btn bg-yellow-600 text-white hover:bg-yellow-700"
                >
                  <RefreshCw className="h-4 w-4 mr-2" />
                  重载配置
                </button>
                <button
                  onClick={handleRefresh}
                  disabled={refreshing}
                  className="btn btn-primary"
                >
                  <RefreshCw className={`h-4 w-4 mr-2 ${refreshing ? 'animate-spin' : ''}`} />
                  刷新
                </button>
              </div>
            </div>
          </div>

          {/* Filters */}
          <div className="card">
            <div className="card-body">
              <h3 className="text-lg font-semibold text-gray-900 mb-6 flex items-center">
                <div className="w-1 h-6 bg-primary-500 rounded-full mr-3"></div>
                筛选条件
              </h3>
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-4">
                {/* Search */}
                <div className="lg:col-span-2">
                  <label htmlFor="search" className="block text-sm font-medium text-gray-700 mb-2">
                    搜索规则
                  </label>
                  <div className="relative">
                    <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                    <input
                      type="text"
                      id="search"
                      value={searchTerm}
                      onChange={(e) => setSearchTerm(e.target.value)}
                      placeholder="搜索规则名称、描述或ID..."
                      className="form-input pl-10"
                    />
                  </div>
                </div>

                {/* Service Name */}
                <div>
                  <label htmlFor="service" className="block text-sm font-medium text-gray-700 mb-2">
                    服务名称
                  </label>
                  <input
                    type="text"
                    id="service"
                    value={serviceName}
                    onChange={(e) => setServiceName(e.target.value)}
                    placeholder="输入服务名称"
                    className="form-input"
                  />
                </div>

                {/* Type Filter */}
                <div>
                  <label htmlFor="type" className="block text-sm font-medium text-gray-700 mb-2">
                    规则类型
                  </label>
                  <select
                    id="type"
                    value={selectedType}
                    onChange={(e) => setSelectedType(e.target.value as RuleType | 'ALL')}
                    className="form-input form-select"
                  >
                    <option value="ALL">所有类型</option>
                    <option value="FIELD_NAME">字段名称</option>
                    <option value="PATTERN">正则模式</option>
                    <option value="CONTENT_TYPE">内容类型</option>
                    <option value="CUSTOM">自定义</option>
                  </select>
                </div>

                {/* Severity Filter */}
                <div>
                  <label htmlFor="severity" className="block text-sm font-medium text-gray-700 mb-2">
                    严重程度
                  </label>
                  <select
                    id="severity"
                    value={selectedSeverity}
                    onChange={(e) => setSelectedSeverity(e.target.value as SeverityLevel | 'ALL')}
                    className="form-input form-select"
                  >
                    <option value="ALL">所有级别</option>
                    <option value="CRITICAL">严重</option>
                    <option value="HIGH">高</option>
                    <option value="MEDIUM">中</option>
                    <option value="LOW">低</option>
                  </select>
                </div>
              </div>

              {/* Enabled Only Toggle */}
              <div className="mt-6 pt-6 border-t border-gray-200">
                <label className="flex items-center">
                  <input
                    type="checkbox"
                    checked={showEnabledOnly}
                    onChange={(e) => setShowEnabledOnly(e.target.checked)}
                    className="rounded border-gray-300 text-primary-600 shadow-sm focus:border-primary-300 focus:ring focus:ring-primary-200 focus:ring-opacity-50"
                  />
                  <span className="ml-3 text-sm font-medium text-gray-700">仅显示启用的规则</span>
                </label>
              </div>
            </div>
          </div>

          {/* Rules Table */}
          <div className="card overflow-hidden">
            <div className="px-6 py-4 border-b border-gray-200 bg-gray-50">
              <div className="flex items-center justify-between">
                <h3 className="text-lg font-semibold text-gray-900">
                  脱敏规则列表
                </h3>
                <div className="text-sm text-gray-600">
                  显示 {filteredRules.length} / {config?.rules.length || 0} 条规则
                </div>
              </div>
            </div>
            <div className="overflow-x-auto">
              <table className="table">
                <thead>
                  <tr>
                    <th>规则信息</th>
                    <th>类型</th>
                    <th>严重程度</th>
                    <th>优先级</th>
                    <th>状态</th>
                    <th>配置详情</th>
                  </tr>
                </thead>
                <tbody>
                  {filteredRules.map((rule) => {
                    const typeNames: Record<string, string> = {
                      'FIELD_NAME': '字段名称',
                      'PATTERN': '正则模式',
                      'CONTENT_TYPE': '内容类型',
                      'CUSTOM': '自定义'
                    };
                    const severityNames: Record<string, string> = {
                      'CRITICAL': '严重',
                      'HIGH': '高',
                      'MEDIUM': '中',
                      'LOW': '低'
                    };

                    return (
                      <tr key={rule.id}>
                        <td>
                          <div className="space-y-1">
                            <div className="font-semibold text-gray-900">{rule.name}</div>
                            <div className="text-sm text-gray-600">{truncateText(rule.description, 80)}</div>
                            <div className="text-xs text-gray-400 font-mono">ID: {rule.id}</div>
                          </div>
                        </td>
                        <td>
                          <span className={`badge ${getRuleTypeColor(rule.type)}`}>
                            <span className="mr-1">{getRuleTypeIcon(rule.type)}</span>
                            {typeNames[rule.type] || rule.type}
                          </span>
                        </td>
                        <td>
                          <span className={`badge ${getSeverityBadgeColor(rule.severity)}`}>
                            {severityNames[rule.severity] || rule.severity}
                          </span>
                        </td>
                        <td>
                          <div className="flex items-center">
                            <div className="w-8 h-8 rounded-full bg-gray-100 flex items-center justify-center text-sm font-semibold text-gray-700">
                              {rule.priority}
                            </div>
                          </div>
                        </td>
                        <td>
                          <div className="flex items-center">
                            {rule.enabled ? (
                              <>
                                <div className="w-2 h-2 bg-green-400 rounded-full mr-2 animate-pulse"></div>
                                <span className="text-sm font-medium text-green-600">已启用</span>
                              </>
                            ) : (
                              <>
                                <div className="w-2 h-2 bg-gray-400 rounded-full mr-2"></div>
                                <span className="text-sm font-medium text-gray-500">已禁用</span>
                              </>
                            )}
                          </div>
                        </td>
                        <td>
                          <div className="space-y-2 text-sm">
                            {rule.fieldNames && rule.fieldNames.length > 0 && (
                              <div>
                                <span className="font-medium text-gray-700">字段:</span>
                                <span className="ml-1 text-gray-600">
                                  {rule.fieldNames.slice(0, 2).join(', ')}
                                  {rule.fieldNames.length > 2 && ` +${rule.fieldNames.length - 2}个`}
                                </span>
                              </div>
                            )}
                            {rule.pattern && (
                              <div>
                                <span className="font-medium text-gray-700">模式:</span>
                                <code className="ml-1 text-xs">{truncateText(rule.pattern, 30)}</code>
                              </div>
                            )}
                            <div>
                              <span className="font-medium text-gray-700">掩码:</span>
                              <code className="ml-1 text-xs">{rule.maskValue}</code>
                            </div>
                            {rule.includeServices && rule.includeServices.length > 0 && (
                              <div>
                                <span className="font-medium text-gray-700">适用服务:</span>
                                <span className="ml-1 text-gray-600">{rule.includeServices.join(', ')}</span>
                              </div>
                            )}
                          </div>
                        </td>
                      </tr>
                    );
                  })}
                </tbody>
              </table>

              {filteredRules.length === 0 && (
                <div className="text-center py-12">
                  <Shield className="mx-auto h-12 w-12 text-gray-400" />
                  <h3 className="mt-2 text-sm font-medium text-gray-900">没有找到匹配的规则</h3>
                  <p className="mt-1 text-sm text-gray-500">请尝试调整筛选条件</p>
                </div>
              )}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default RulesList;
