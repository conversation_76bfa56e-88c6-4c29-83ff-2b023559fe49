import React, { useState, useEffect } from 'react';
import { Activity, Shield, AlertTriangle, CheckCircle, RefreshCw } from 'lucide-react';
import { sanitizationApi } from '../services/api';
import { HealthResponse, MetricsResponse } from '../types';
import { formatTimestamp } from '../utils';
import toast from 'react-hot-toast';

const Dashboard: React.FC = () => {
  const [health, setHealth] = useState<HealthResponse | null>(null);
  const [metrics, setMetrics] = useState<MetricsResponse | null>(null);
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);

  const fetchData = async () => {
    try {
      const [healthData, metricsData] = await Promise.all([
        sanitizationApi.getHealth(),
        sanitizationApi.getMetrics(),
      ]);
      setHealth(healthData);
      setMetrics(metricsData);
    } catch (error) {
      console.error('Failed to fetch dashboard data:', error);
      toast.error('Failed to load dashboard data');
    } finally {
      setLoading(false);
      setRefreshing(false);
    }
  };

  const handleRefresh = async () => {
    setRefreshing(true);
    await fetchData();
  };

  useEffect(() => {
    fetchData();
    const interval = setInterval(fetchData, 30000); // Refresh every 30 seconds
    return () => clearInterval(interval);
  }, []);

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary-600"></div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="space-y-8 fade-in">
          {/* Header */}
          <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
            <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between">
              <div className="mb-4 sm:mb-0">
                <h1 className="text-3xl font-bold text-gray-900 mb-2">
                  <Shield className="inline-block h-8 w-8 text-primary-600 mr-3" />
                  数据脱敏控制台
                </h1>
                <p className="text-gray-600 text-lg">实时监控和管理数据脱敏服务</p>
              </div>
              <div className="flex items-center space-x-3">
                <div className="flex items-center">
                  <div className={`h-3 w-3 rounded-full mr-2 ${health?.status === 'healthy' ? 'bg-green-400 animate-pulse' : 'bg-red-400'}`}></div>
                  <span className={`text-sm font-medium ${health?.status === 'healthy' ? 'text-green-600' : 'text-red-600'}`}>
                    {health?.status === 'healthy' ? '服务正常' : '服务异常'}
                  </span>
                </div>
                <button
                  onClick={handleRefresh}
                  disabled={refreshing}
                  className="btn btn-primary"
                >
                  <RefreshCw className={`h-4 w-4 mr-2 ${refreshing ? 'animate-spin' : ''}`} />
                  刷新
                </button>
              </div>
            </div>
          </div>

          {/* Status Cards */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            {/* Service Status */}
            <div className="card hover:shadow-lg transition-all duration-200">
              <div className="card-body">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-gray-600 mb-1">服务状态</p>
                    <p className="text-2xl font-bold text-gray-900">
                      {health?.status === 'healthy' ? (
                        <span className="text-green-600">正常</span>
                      ) : (
                        <span className="text-red-600">异常</span>
                      )}
                    </p>
                  </div>
                  <div className={`p-3 rounded-full ${health?.status === 'healthy' ? 'bg-green-100' : 'bg-red-100'}`}>
                    <Activity className={`h-6 w-6 ${health?.status === 'healthy' ? 'text-green-600' : 'text-red-600'}`} />
                  </div>
                </div>
              </div>
            </div>

            {/* Total Rules */}
            <div className="card hover:shadow-lg transition-all duration-200">
              <div className="card-body">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-gray-600 mb-1">规则总数</p>
                    <p className="text-2xl font-bold text-gray-900">{metrics?.totalRules || 0}</p>
                    <p className="text-xs text-gray-500 mt-1">已配置的脱敏规则</p>
                  </div>
                  <div className="p-3 rounded-full bg-blue-100">
                    <Shield className="h-6 w-6 text-blue-600" />
                  </div>
                </div>
              </div>
            </div>

            {/* Enabled Rules */}
            <div className="card hover:shadow-lg transition-all duration-200">
              <div className="card-body">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-gray-600 mb-1">启用规则</p>
                    <p className="text-2xl font-bold text-green-600">{metrics?.enabledRules || 0}</p>
                    <p className="text-xs text-gray-500 mt-1">当前生效的规则</p>
                  </div>
                  <div className="p-3 rounded-full bg-green-100">
                    <CheckCircle className="h-6 w-6 text-green-600" />
                  </div>
                </div>
              </div>
            </div>

            {/* Disabled Rules */}
            <div className="card hover:shadow-lg transition-all duration-200">
              <div className="card-body">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-gray-600 mb-1">禁用规则</p>
                    <p className="text-2xl font-bold text-yellow-600">{metrics?.disabledRules || 0}</p>
                    <p className="text-xs text-gray-500 mt-1">暂停使用的规则</p>
                  </div>
                  <div className="p-3 rounded-full bg-yellow-100">
                    <AlertTriangle className="h-6 w-6 text-yellow-600" />
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* Charts Section */}
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {/* Rules by Type */}
            <div className="card">
              <div className="card-body">
                <h3 className="text-lg font-semibold text-gray-900 mb-6 flex items-center">
                  <div className="w-1 h-6 bg-primary-500 rounded-full mr-3"></div>
                  按类型分布
                </h3>
                <div className="space-y-4">
                  {metrics?.rulesByType && Object.entries(metrics.rulesByType).map(([type, count]) => {
                    const total = metrics.totalRules || 1;
                    const percentage = Math.round((count / total) * 100);
                    const typeNames: Record<string, string> = {
                      'FIELD_NAME': '字段名称',
                      'PATTERN': '正则模式',
                      'CONTENT_TYPE': '内容类型',
                      'CUSTOM': '自定义'
                    };
                    const colors: Record<string, string> = {
                      'FIELD_NAME': 'bg-blue-500',
                      'PATTERN': 'bg-purple-500',
                      'CONTENT_TYPE': 'bg-indigo-500',
                      'CUSTOM': 'bg-gray-500'
                    };

                    return (
                      <div key={type} className="space-y-2">
                        <div className="flex items-center justify-between">
                          <div className="flex items-center">
                            <div className={`w-3 h-3 rounded-full mr-3 ${colors[type] || 'bg-gray-500'}`}></div>
                            <span className="text-sm font-medium text-gray-700">{typeNames[type] || type}</span>
                          </div>
                          <div className="flex items-center space-x-2">
                            <span className="text-sm text-gray-500">{percentage}%</span>
                            <span className="text-sm font-semibold text-gray-900">{count}</span>
                          </div>
                        </div>
                        <div className="w-full bg-gray-200 rounded-full h-2">
                          <div
                            className={`h-2 rounded-full transition-all duration-500 ${colors[type] || 'bg-gray-500'}`}
                            style={{ width: `${percentage}%` }}
                          ></div>
                        </div>
                      </div>
                    );
                  })}
                </div>
              </div>
            </div>

            {/* Rules by Severity */}
            <div className="card">
              <div className="card-body">
                <h3 className="text-lg font-semibold text-gray-900 mb-6 flex items-center">
                  <div className="w-1 h-6 bg-red-500 rounded-full mr-3"></div>
                  按严重程度分布
                </h3>
                <div className="space-y-4">
                  {metrics?.rulesBySeverity && Object.entries(metrics.rulesBySeverity).map(([severity, count]) => {
                    const total = metrics.totalRules || 1;
                    const percentage = Math.round((count / total) * 100);
                    const severityNames: Record<string, string> = {
                      'CRITICAL': '严重',
                      'HIGH': '高',
                      'MEDIUM': '中',
                      'LOW': '低'
                    };
                    const colors: Record<string, string> = {
                      'CRITICAL': 'bg-red-500',
                      'HIGH': 'bg-orange-500',
                      'MEDIUM': 'bg-yellow-500',
                      'LOW': 'bg-green-500'
                    };

                    return (
                      <div key={severity} className="space-y-2">
                        <div className="flex items-center justify-between">
                          <div className="flex items-center">
                            <div className={`w-3 h-3 rounded-full mr-3 ${colors[severity] || 'bg-gray-500'}`}></div>
                            <span className="text-sm font-medium text-gray-700">{severityNames[severity] || severity}</span>
                          </div>
                          <div className="flex items-center space-x-2">
                            <span className="text-sm text-gray-500">{percentage}%</span>
                            <span className="text-sm font-semibold text-gray-900">{count}</span>
                          </div>
                        </div>
                        <div className="w-full bg-gray-200 rounded-full h-2">
                          <div
                            className={`h-2 rounded-full transition-all duration-500 ${colors[severity] || 'bg-gray-500'}`}
                            style={{ width: `${percentage}%` }}
                          ></div>
                        </div>
                      </div>
                    );
                  })}
                </div>
              </div>
            </div>
          </div>

          {/* System Info */}
          <div className="card">
            <div className="card-body">
              <h3 className="text-lg font-semibold text-gray-900 mb-6 flex items-center">
                <div className="w-1 h-6 bg-green-500 rounded-full mr-3"></div>
                系统信息
              </h3>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                <div className="text-center p-4 bg-gray-50 rounded-lg">
                  <dt className="text-sm font-medium text-gray-500 mb-2">服务版本</dt>
                  <dd className="text-lg font-semibold text-gray-900">{health?.version || 'N/A'}</dd>
                </div>
                <div className="text-center p-4 bg-gray-50 rounded-lg">
                  <dt className="text-sm font-medium text-gray-500 mb-2">配置版本</dt>
                  <dd className="text-lg font-semibold text-gray-900">{metrics?.configVersion || 'N/A'}</dd>
                </div>
                <div className="text-center p-4 bg-gray-50 rounded-lg">
                  <dt className="text-sm font-medium text-gray-500 mb-2">最后更新</dt>
                  <dd className="text-sm font-semibold text-gray-900">
                    {metrics?.lastUpdated ? formatTimestamp(metrics.lastUpdated) : 'N/A'}
                  </dd>
                </div>
              </div>

              {/* Additional Info */}
              <div className="mt-6 pt-6 border-t border-gray-200">
                <div className="flex items-center justify-between text-sm">
                  <span className="text-gray-500">数据刷新间隔</span>
                  <span className="font-medium text-gray-900">30秒</span>
                </div>
                <div className="flex items-center justify-between text-sm mt-2">
                  <span className="text-gray-500">API端点</span>
                  <span className="font-medium text-gray-900">http://localhost:8081</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Dashboard;
