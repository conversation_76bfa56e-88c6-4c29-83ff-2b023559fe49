import React, { useState, useEffect } from 'react';
import { RefreshCw, Download, Settings, AlertCircle } from 'lucide-react';
import { sanitizationApi } from '../services/api';
import { SanitizationConfig } from '../types';
import { formatTimestamp, downloadJson } from '../utils';
import toast from 'react-hot-toast';

const ConfigManager: React.FC = () => {
  const [config, setConfig] = useState<SanitizationConfig | null>(null);
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  const [refreshing, setRefreshing] = useState(false);

  const fetchConfig = async () => {
    try {
      const data = await sanitizationApi.getRules();
      setConfig(data);
    } catch (error) {
      console.error('Failed to fetch config:', error);
      toast.error('Failed to load configuration');
    } finally {
      setLoading(false);
      setRefreshing(false);
    }
  };

  const handleRefresh = async () => {
    setRefreshing(true);
    await fetchConfig();
  };

  const handleReloadConfig = async () => {
    try {
      setSaving(true);
      await sanitizationApi.reloadRules();
      toast.success('Configuration reloaded successfully');
      await fetchConfig();
    } catch (error) {
      console.error('Failed to reload config:', error);
      toast.error('Failed to reload configuration');
    } finally {
      setSaving(false);
    }
  };

  const handleExport = () => {
    if (config) {
      downloadJson(config, `sanitization-config-${Date.now()}.json`);
      toast.success('Configuration exported successfully');
    }
  };

  const handleConfigChange = (field: keyof SanitizationConfig, value: any) => {
    if (config) {
      setConfig({
        ...config,
        [field]: value,
        timestamp: Date.now(),
      });
    }
  };

  const handleGlobalSettingChange = (key: string, value: any) => {
    if (config) {
      setConfig({
        ...config,
        globalSettings: {
          ...config.globalSettings,
          [key]: value,
        },
        timestamp: Date.now(),
      });
    }
  };

  useEffect(() => {
    fetchConfig();
  }, []);

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary-600"></div>
      </div>
    );
  }

  if (!config) {
    return (
      <div className="text-center py-12">
        <AlertCircle className="mx-auto h-12 w-12 text-gray-400" />
        <h3 className="mt-2 text-sm font-medium text-gray-900">No configuration found</h3>
        <p className="mt-1 text-sm text-gray-500">Unable to load the sanitization configuration.</p>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="bg-white shadow rounded-lg p-6">
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-2xl font-bold text-gray-900 flex items-center">
              <Settings className="h-8 w-8 text-blue-600 mr-3" />
              配置管理
            </h1>
            <p className="text-gray-600 mt-1">管理全局脱敏设置和配置参数</p>
          </div>
          <div className="flex space-x-3">
            <button
              onClick={handleExport}
              className="inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
            >
              <Download className="h-4 w-4 mr-2" />
              导出配置
            </button>
            <button
              onClick={handleReloadConfig}
              disabled={saving}
              className="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-yellow-600 hover:bg-yellow-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-yellow-500 disabled:opacity-50"
            >
              <RefreshCw className={`h-4 w-4 mr-2 ${saving ? 'animate-spin' : ''}`} />
              重载配置
            </button>
            <button
              onClick={handleRefresh}
              disabled={refreshing}
              className="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50"
            >
              <RefreshCw className={`h-4 w-4 mr-2 ${refreshing ? 'animate-spin' : ''}`} />
              刷新
            </button>
          </div>
        </div>
      </div>

      {/* Configuration Info */}
      <div className="bg-white shadow rounded-lg p-6">
        <div className="flex items-center mb-4">
          <Settings className="h-5 w-5 text-gray-400 mr-2" />
          <h3 className="text-lg font-medium text-gray-900">Configuration Information</h3>
        </div>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <div>
            <dt className="text-sm font-medium text-gray-500">Version</dt>
            <dd className="mt-1 text-sm text-gray-900">{config.version}</dd>
          </div>
          <div>
            <dt className="text-sm font-medium text-gray-500">Last Updated</dt>
            <dd className="mt-1 text-sm text-gray-900">{formatTimestamp(config.timestamp)}</dd>
          </div>
          <div>
            <dt className="text-sm font-medium text-gray-500">Total Rules</dt>
            <dd className="mt-1 text-sm text-gray-900">{config.rules.length}</dd>
          </div>
        </div>
      </div>

      {/* Global Settings */}
      <div className="bg-white shadow rounded-lg p-6">
        <h3 className="text-lg font-medium text-gray-900 mb-6">Global Settings</h3>
        
        <div className="space-y-6">
          {/* Basic Settings */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
              <label className="flex items-center">
                <input
                  type="checkbox"
                  checked={config.enabled}
                  onChange={(e) => handleConfigChange('enabled', e.target.checked)}
                  className="rounded border-gray-300 text-primary-600 shadow-sm focus:border-primary-300 focus:ring focus:ring-primary-200 focus:ring-opacity-50"
                />
                <span className="ml-2 text-sm font-medium text-gray-700">Enable Sanitization</span>
              </label>
              <p className="mt-1 text-sm text-gray-500">Enable or disable the entire sanitization system</p>
            </div>

            <div>
              <label className="flex items-center">
                <input
                  type="checkbox"
                  checked={config.markersEnabled}
                  onChange={(e) => handleConfigChange('markersEnabled', e.target.checked)}
                  className="rounded border-gray-300 text-primary-600 shadow-sm focus:border-primary-300 focus:ring focus:ring-primary-200 focus:ring-opacity-50"
                />
                <span className="ml-2 text-sm font-medium text-gray-700">Enable Markers</span>
              </label>
              <p className="mt-1 text-sm text-gray-500">Add markers to indicate sanitized data</p>
            </div>
          </div>

          {/* Marker Format */}
          <div>
            <label htmlFor="markerFormat" className="block text-sm font-medium text-gray-700 mb-1">
              Marker Format
            </label>
            <select
              id="markerFormat"
              value={config.markerFormat}
              onChange={(e) => handleConfigChange('markerFormat', e.target.value)}
              className="block w-full md:w-1/3 border-gray-300 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500 sm:text-sm"
            >
              <option value="BRACKET">Bracket [SANITIZED]</option>
              <option value="ASTERISK">Asterisk *SANITIZED*</option>
              <option value="HASH">Hash #SANITIZED#</option>
              <option value="NONE">None</option>
            </select>
            <p className="mt-1 text-sm text-gray-500">Format for sanitization markers</p>
          </div>

          {/* Version */}
          <div>
            <label htmlFor="version" className="block text-sm font-medium text-gray-700 mb-1">
              Configuration Version
            </label>
            <input
              type="text"
              id="version"
              value={config.version}
              onChange={(e) => handleConfigChange('version', e.target.value)}
              className="block w-full md:w-1/3 border-gray-300 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500 sm:text-sm"
            />
            <p className="mt-1 text-sm text-gray-500">Version identifier for this configuration</p>
          </div>
        </div>
      </div>

      {/* Advanced Settings */}
      {config.globalSettings && (
        <div className="bg-white shadow rounded-lg p-6">
          <h3 className="text-lg font-medium text-gray-900 mb-6">Advanced Settings</h3>
          
          <div className="space-y-6">
            {Object.entries(config.globalSettings).map(([key, value]) => (
              <div key={key}>
                <label htmlFor={key} className="block text-sm font-medium text-gray-700 mb-1">
                  {key.replace(/([A-Z])/g, ' $1').replace(/^./, str => str.toUpperCase())}
                </label>
                {typeof value === 'boolean' ? (
                  <label className="flex items-center">
                    <input
                      type="checkbox"
                      checked={value}
                      onChange={(e) => handleGlobalSettingChange(key, e.target.checked)}
                      className="rounded border-gray-300 text-primary-600 shadow-sm focus:border-primary-300 focus:ring focus:ring-primary-200 focus:ring-opacity-50"
                    />
                    <span className="ml-2 text-sm text-gray-700">{value ? 'Enabled' : 'Disabled'}</span>
                  </label>
                ) : typeof value === 'number' ? (
                  <input
                    type="number"
                    value={value}
                    onChange={(e) => handleGlobalSettingChange(key, parseInt(e.target.value))}
                    className="block w-full md:w-1/3 border-gray-300 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500 sm:text-sm"
                  />
                ) : (
                  <input
                    type="text"
                    value={String(value)}
                    onChange={(e) => handleGlobalSettingChange(key, e.target.value)}
                    className="block w-full md:w-1/2 border-gray-300 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500 sm:text-sm"
                  />
                )}
              </div>
            ))}
          </div>
        </div>
      )}

      {/* Configuration Preview */}
      <div className="bg-white shadow rounded-lg p-6">
        <h3 className="text-lg font-medium text-gray-900 mb-4">Configuration Preview</h3>
        <div className="bg-gray-50 rounded-md p-4 overflow-x-auto">
          <pre className="text-sm text-gray-800">
            {JSON.stringify(config, null, 2)}
          </pre>
        </div>
      </div>
    </div>
  );
};

export default ConfigManager;
