import React, { useState, useEffect } from 'react';
import { Shield, Search, RefreshCw, Power } from 'lucide-react';
import { sanitizationApi } from '../services/api';
import { SanitizationConfig } from '../types';
import toast from 'react-hot-toast';

const SimpleRulesList: React.FC = () => {
  const [config, setConfig] = useState<SanitizationConfig | null>(null);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [toggling, setToggling] = useState(false);

  const fetchRules = async () => {
    try {
      setLoading(true);
      const response = await sanitizationApi.getRules();
      setConfig(response);
    } catch (error) {
      toast.error('获取规则失败');
      console.error('Error fetching rules:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleReload = async () => {
    try {
      await sanitizationApi.reloadRules();
      toast.success('规则重载成功');
      await fetchRules();
    } catch (error) {
      toast.error('规则重载失败');
      console.error('Error reloading rules:', error);
    }
  };

  const handleToggleGlobal = async () => {
    if (!config) return;

    try {
      setToggling(true);
      const newEnabled = !config.enabled;
      const response = await sanitizationApi.toggleGlobalSwitch(newEnabled);

      setConfig(prev => prev ? { ...prev, enabled: response.enabled } : null);
      toast.success(newEnabled ? '全局脱敏已启用' : '全局脱敏已禁用');
    } catch (error) {
      toast.error('切换全局开关失败');
      console.error('Error toggling global switch:', error);
    } finally {
      setToggling(false);
    }
  };

  useEffect(() => {
    fetchRules();
  }, []);

  const filteredRules = config?.rules.filter(rule =>
    rule.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
    rule.description.toLowerCase().includes(searchTerm.toLowerCase()) ||
    rule.id.toLowerCase().includes(searchTerm.toLowerCase())
  ) || [];

  const getSeverityColor = (severity: string) => {
    switch (severity) {
      case 'CRITICAL': return 'bg-red-900/50 text-red-400 border border-red-800';
      case 'HIGH': return 'bg-orange-900/50 text-orange-400 border border-orange-800';
      case 'MEDIUM': return 'bg-yellow-900/50 text-yellow-400 border border-yellow-800';
      case 'LOW': return 'bg-green-900/50 text-green-400 border border-green-800';
      default: return 'bg-gray-800 text-gray-400 border border-gray-700';
    }
  };

  const getTypeColor = (type: string) => {
    switch (type) {
      case 'FIELD_NAME': return 'bg-blue-900/50 text-blue-400 border border-blue-800';
      case 'PATTERN': return 'bg-purple-900/50 text-purple-400 border border-purple-800';
      case 'CONTENT_TYPE': return 'bg-indigo-900/50 text-indigo-400 border border-indigo-800';
      default: return 'bg-gray-800 text-gray-400 border border-gray-700';
    }
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-black flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500 mx-auto"></div>
          <p className="text-gray-400 mt-4">加载中...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-black text-white">
      {/* Header */}
      <div className="sticky top-0 bg-black/80 backdrop-blur-md border-b border-gray-800 z-10">
        <div className="max-w-6xl mx-auto px-4 py-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-4">
              <div className="p-2 bg-blue-600 rounded-full">
                <Shield className="h-6 w-6 text-white" />
              </div>
              <div>
                <h1 className="text-xl font-bold text-white">
                  数据脱敏规则管理
                </h1>
                <div className="flex items-center space-x-2 mt-1">
                  <span className="text-gray-400 text-sm">管理和配置数据脱敏规则</span>
                  <span className={`inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium ${
                    config?.enabled
                      ? 'bg-green-900/50 text-green-400 border border-green-800'
                      : 'bg-red-900/50 text-red-400 border border-red-800'
                  }`}>
                    {config?.enabled ? '已启用' : '已禁用'}
                  </span>
                </div>
              </div>
            </div>
            <div className="flex items-center space-x-3">
              <button
                onClick={handleToggleGlobal}
                disabled={toggling}
                className={`inline-flex items-center px-4 py-2 rounded-full text-sm font-medium transition-all duration-200 hover:scale-105 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-offset-black ${
                  config?.enabled
                    ? 'bg-red-600 hover:bg-red-700 text-white focus:ring-red-500'
                    : 'bg-green-600 hover:bg-green-700 text-white focus:ring-green-500'
                } ${toggling ? 'opacity-50 cursor-not-allowed scale-100' : ''}`}
              >
                <Power className="h-4 w-4 mr-2" />
                {toggling ? '切换中...' : (config?.enabled ? '禁用全局脱敏' : '启用全局脱敏')}
              </button>
              <button
                onClick={handleReload}
                className="inline-flex items-center px-4 py-2 rounded-full text-sm font-medium bg-blue-600 hover:bg-blue-700 text-white transition-all duration-200 hover:scale-105 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 focus:ring-offset-black"
              >
                <RefreshCw className="h-4 w-4 mr-2" />
                重载规则
              </button>
            </div>
          </div>
        </div>
      </div>

      {/* Search */}
      <div className="max-w-6xl mx-auto px-4 py-6">
        <div className="relative">
          <Search className="absolute left-4 top-1/2 transform -translate-y-1/2 h-5 w-5 text-gray-500" />
          <input
            type="text"
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            placeholder="搜索规则名称、描述或ID..."
            className="w-full pl-12 pr-4 py-3 bg-gray-900 border border-gray-700 rounded-full text-white placeholder-gray-500 focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200"
          />
        </div>
      </div>

      {/* Rules List */}
      <div className="max-w-6xl mx-auto px-4">
        <div className="mb-4">
          <h3 className="text-lg font-semibold text-white">
            规则列表 ({filteredRules.length} / {config?.rules.length || 0})
          </h3>
        </div>
        
        {filteredRules.length === 0 ? (
          <div className="text-center py-16">
            <Shield className="mx-auto h-16 w-16 text-gray-600" />
            <h3 className="mt-4 text-lg font-medium text-gray-300">没有找到匹配的规则</h3>
            <p className="mt-2 text-sm text-gray-500">请尝试调整搜索条件</p>
          </div>
        ) : (
          <div className="space-y-4">
            {filteredRules.map((rule) => (
              <div key={rule.id} className="bg-gray-900 border border-gray-800 rounded-2xl p-6 hover:border-gray-700 transition-all duration-200 hover:shadow-lg">
                <div className="flex items-start justify-between">
                  <div className="flex-1">
                    <div className="flex items-center space-x-3 mb-2">
                      <h4 className="text-lg font-semibold text-white">{rule.name}</h4>
                      <span className={`inline-flex items-center px-3 py-1 rounded-full text-xs font-medium ${getSeverityColor(rule.severity)}`}>
                        {rule.severity}
                      </span>
                      <span className={`inline-flex items-center px-3 py-1 rounded-full text-xs font-medium ${getTypeColor(rule.type)}`}>
                        {rule.type}
                      </span>
                    </div>
                    <p className="text-sm text-gray-300 mb-2">{rule.description}</p>
                    <div className="text-xs text-gray-500 mb-3">ID: {rule.id}</div>
                    
                    <div className="space-y-3">
                      {rule.fieldNames && rule.fieldNames.length > 0 && (
                        <div className="text-sm">
                          <span className="font-medium text-gray-400">字段:</span>
                          <span className="ml-2 text-gray-300">{rule.fieldNames.join(', ')}</span>
                        </div>
                      )}
                      {rule.pattern && (
                        <div className="text-sm">
                          <span className="font-medium text-gray-400">模式:</span>
                          <code className="ml-2 text-xs bg-gray-800 text-blue-400 px-2 py-1 rounded-md border border-gray-700">{rule.pattern}</code>
                        </div>
                      )}
                      <div className="text-sm">
                        <span className="font-medium text-gray-400">掩码:</span>
                        <code className="ml-2 text-xs bg-gray-800 text-green-400 px-2 py-1 rounded-md border border-gray-700">{rule.maskValue}</code>
                      </div>
                      {rule.includeServices && rule.includeServices.length > 0 && (
                        <div className="text-sm">
                          <span className="font-medium text-gray-400">适用服务:</span>
                          <span className="ml-2 text-gray-300">{rule.includeServices.join(', ')}</span>
                        </div>
                      )}
                    </div>
                  </div>
                  
                  <div className="ml-6 flex flex-col items-end space-y-3">
                    <div className="flex items-center">
                      {rule.enabled ? (
                        <>
                          <div className="w-2 h-2 bg-green-400 rounded-full mr-2 animate-pulse"></div>
                          <span className="text-sm font-medium text-green-400">已启用</span>
                        </>
                      ) : (
                        <>
                          <div className="w-2 h-2 bg-gray-600 rounded-full mr-2"></div>
                          <span className="text-sm font-medium text-gray-500">已禁用</span>
                        </>
                      )}
                    </div>
                    <div className="text-sm text-gray-500 bg-gray-800 px-2 py-1 rounded-md">
                      优先级: {rule.priority}
                    </div>
                  </div>
                </div>
              </div>
            ))}
          </div>
        )}
      </div>
    </div>
  );
};

export default SimpleRulesList;
