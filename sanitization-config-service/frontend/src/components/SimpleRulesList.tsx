import React, { useState, useEffect } from 'react';
import { Shield, Search, RefreshCw, Power } from 'lucide-react';
import { sanitizationApi } from '../services/api';
import { SanitizationConfig } from '../types';
import toast from 'react-hot-toast';

const SimpleRulesList: React.FC = () => {
  const [config, setConfig] = useState<SanitizationConfig | null>(null);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [toggling, setToggling] = useState(false);

  const fetchRules = async () => {
    try {
      setLoading(true);
      const response = await sanitizationApi.getRules();
      setConfig(response);
    } catch (error) {
      toast.error('获取规则失败');
      console.error('Error fetching rules:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleReload = async () => {
    try {
      await sanitizationApi.reloadRules();
      toast.success('规则重载成功');
      await fetchRules();
    } catch (error) {
      toast.error('规则重载失败');
      console.error('Error reloading rules:', error);
    }
  };

  const handleToggleGlobal = async () => {
    if (!config) return;

    try {
      setToggling(true);
      const newEnabled = !config.enabled;
      const response = await sanitizationApi.toggleGlobalSwitch(newEnabled);

      setConfig(prev => prev ? { ...prev, enabled: response.enabled } : null);
      toast.success(newEnabled ? '全局脱敏已启用' : '全局脱敏已禁用');
    } catch (error) {
      toast.error('切换全局开关失败');
      console.error('Error toggling global switch:', error);
    } finally {
      setToggling(false);
    }
  };

  useEffect(() => {
    fetchRules();
  }, []);

  const filteredRules = config?.rules.filter(rule =>
    rule.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
    rule.description.toLowerCase().includes(searchTerm.toLowerCase()) ||
    rule.id.toLowerCase().includes(searchTerm.toLowerCase())
  ) || [];

  const getSeverityColor = (severity: string) => {
    switch (severity) {
      case 'CRITICAL': return 'bg-red-100 text-red-800';
      case 'HIGH': return 'bg-orange-100 text-orange-800';
      case 'MEDIUM': return 'bg-yellow-100 text-yellow-800';
      case 'LOW': return 'bg-green-100 text-green-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const getTypeColor = (type: string) => {
    switch (type) {
      case 'FIELD_NAME': return 'bg-blue-100 text-blue-800';
      case 'PATTERN': return 'bg-purple-100 text-purple-800';
      case 'CONTENT_TYPE': return 'bg-indigo-100 text-indigo-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="bg-white shadow rounded-lg p-6">
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-2xl font-bold text-gray-900 flex items-center">
              <Shield className="h-8 w-8 text-blue-600 mr-3" />
              数据脱敏规则管理
            </h1>
            <p className="text-gray-600 mt-1">
              管理和配置数据脱敏规则
              <span className={`ml-2 inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${
                config?.enabled ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'
              }`}>
                {config?.enabled ? '已启用' : '已禁用'}
              </span>
            </p>
          </div>
          <div className="flex items-center space-x-3">
            <button
              onClick={handleToggleGlobal}
              disabled={toggling}
              className={`inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium focus:outline-none focus:ring-2 focus:ring-offset-2 ${
                config?.enabled
                  ? 'text-white bg-red-600 hover:bg-red-700 focus:ring-red-500'
                  : 'text-white bg-green-600 hover:bg-green-700 focus:ring-green-500'
              } ${toggling ? 'opacity-50 cursor-not-allowed' : ''}`}
            >
              <Power className="h-4 w-4 mr-2" />
              {toggling ? '切换中...' : (config?.enabled ? '禁用全局脱敏' : '启用全局脱敏')}
            </button>
            <button
              onClick={handleReload}
              className="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
            >
              <RefreshCw className="h-4 w-4 mr-2" />
              重载规则
            </button>
          </div>
        </div>
      </div>

      {/* Search */}
      <div className="bg-white shadow rounded-lg p-6">
        <div className="relative">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
          <input
            type="text"
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            placeholder="搜索规则名称、描述或ID..."
            className="pl-10 block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
          />
        </div>
      </div>

      {/* Rules List */}
      <div className="bg-white shadow rounded-lg overflow-hidden">
        <div className="px-6 py-4 border-b border-gray-200">
          <h3 className="text-lg font-medium text-gray-900">
            规则列表 ({filteredRules.length} / {config?.rules.length || 0})
          </h3>
        </div>
        
        {filteredRules.length === 0 ? (
          <div className="text-center py-12">
            <Shield className="mx-auto h-12 w-12 text-gray-400" />
            <h3 className="mt-2 text-sm font-medium text-gray-900">没有找到匹配的规则</h3>
            <p className="mt-1 text-sm text-gray-500">请尝试调整搜索条件</p>
          </div>
        ) : (
          <div className="divide-y divide-gray-200">
            {filteredRules.map((rule) => (
              <div key={rule.id} className="p-6 hover:bg-gray-50">
                <div className="flex items-start justify-between">
                  <div className="flex-1">
                    <div className="flex items-center space-x-3">
                      <h4 className="text-lg font-medium text-gray-900">{rule.name}</h4>
                      <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getSeverityColor(rule.severity)}`}>
                        {rule.severity}
                      </span>
                      <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getTypeColor(rule.type)}`}>
                        {rule.type}
                      </span>
                    </div>
                    <p className="mt-1 text-sm text-gray-600">{rule.description}</p>
                    <div className="mt-2 text-xs text-gray-400">ID: {rule.id}</div>
                    
                    <div className="mt-3 space-y-2">
                      {rule.fieldNames && rule.fieldNames.length > 0 && (
                        <div className="text-sm">
                          <span className="font-medium text-gray-700">字段:</span>
                          <span className="ml-2 text-gray-600">{rule.fieldNames.join(', ')}</span>
                        </div>
                      )}
                      {rule.pattern && (
                        <div className="text-sm">
                          <span className="font-medium text-gray-700">模式:</span>
                          <code className="ml-2 text-xs bg-gray-100 px-2 py-1 rounded">{rule.pattern}</code>
                        </div>
                      )}
                      <div className="text-sm">
                        <span className="font-medium text-gray-700">掩码:</span>
                        <code className="ml-2 text-xs bg-gray-100 px-2 py-1 rounded">{rule.maskValue}</code>
                      </div>
                      {rule.includeServices && rule.includeServices.length > 0 && (
                        <div className="text-sm">
                          <span className="font-medium text-gray-700">适用服务:</span>
                          <span className="ml-2 text-gray-600">{rule.includeServices.join(', ')}</span>
                        </div>
                      )}
                    </div>
                  </div>
                  
                  <div className="ml-6 flex flex-col items-end space-y-2">
                    <div className="flex items-center">
                      {rule.enabled ? (
                        <>
                          <div className="w-2 h-2 bg-green-400 rounded-full mr-2 animate-pulse"></div>
                          <span className="text-sm font-medium text-green-600">已启用</span>
                        </>
                      ) : (
                        <>
                          <div className="w-2 h-2 bg-gray-400 rounded-full mr-2"></div>
                          <span className="text-sm font-medium text-gray-500">已禁用</span>
                        </>
                      )}
                    </div>
                    <div className="text-sm text-gray-500">
                      优先级: {rule.priority}
                    </div>
                  </div>
                </div>
              </div>
            ))}
          </div>
        )}
      </div>
    </div>
  );
};

export default SimpleRulesList;
