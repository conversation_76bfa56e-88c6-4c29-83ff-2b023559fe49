import React, { useState, useEffect } from 'react';
import { Search, Settings, RefreshCw, AlertCircle, CheckCircle, XCircle } from 'lucide-react';
import { sanitizationApi } from '../services/api';
import { SanitizationConfig } from '../types';
import toast from 'react-hot-toast';

const SimpleRulesList: React.FC = () => {
  const [config, setConfig] = useState<SanitizationConfig | null>(null);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [toggling, setToggling] = useState(false);

  const fetchRules = async () => {
    try {
      setLoading(true);
      const response = await sanitizationApi.getRules();
      setConfig(response);
    } catch (error) {
      toast.error('获取规则失败');
      console.error('Error fetching rules:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleReload = async () => {
    try {
      await sanitizationApi.reloadRules();
      toast.success('规则重载成功');
      await fetchRules();
    } catch (error) {
      toast.error('规则重载失败');
      console.error('Error reloading rules:', error);
    }
  };

  const handleToggleGlobal = async () => {
    if (!config) return;

    try {
      setToggling(true);
      const newEnabled = !config.enabled;
      const response = await sanitizationApi.toggleGlobalSwitch(newEnabled);

      setConfig(prev => prev ? { ...prev, enabled: response.enabled } : null);
      toast.success(newEnabled ? '全局脱敏已启用' : '全局脱敏已禁用');
    } catch (error) {
      toast.error('切换全局开关失败');
      console.error('Error toggling global switch:', error);
    } finally {
      setToggling(false);
    }
  };

  useEffect(() => {
    fetchRules();
  }, []);

  const filteredRules = config?.rules.filter(rule =>
    rule.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
    rule.description.toLowerCase().includes(searchTerm.toLowerCase()) ||
    rule.id.toLowerCase().includes(searchTerm.toLowerCase())
  ) || [];

  const getSeverityColor = (severity: string) => {
    switch (severity) {
      case 'CRITICAL': return 'bg-red-100 text-red-800';
      case 'HIGH': return 'bg-orange-100 text-orange-800';
      case 'MEDIUM': return 'bg-yellow-100 text-yellow-800';
      case 'LOW': return 'bg-green-100 text-green-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const getTypeColor = (type: string) => {
    switch (type) {
      case 'FIELD_NAME': return 'bg-blue-100 text-blue-800';
      case 'PATTERN': return 'bg-purple-100 text-purple-800';
      case 'CONTENT_TYPE': return 'bg-indigo-100 text-indigo-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-indigo-600 mx-auto"></div>
          <p className="text-gray-600 mt-4">加载中...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <div className="bg-white shadow">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="py-6">
            <div className="md:flex md:items-center md:justify-between">
              <div className="flex-1 min-w-0">
                <h2 className="text-2xl font-bold leading-7 text-gray-900 sm:text-3xl sm:truncate">
                  数据脱敏规则
                </h2>
                <div className="mt-1 flex flex-col sm:flex-row sm:flex-wrap sm:mt-0 sm:space-x-6">
                  <div className="mt-2 flex items-center text-sm text-gray-500">
                    <Settings className="flex-shrink-0 mr-1.5 h-5 w-5 text-gray-400" />
                    共 {filteredRules.length} 条规则
                  </div>
                  <div className="mt-2 flex items-center text-sm">
                    {config?.enabled ? (
                      <>
                        <CheckCircle className="flex-shrink-0 mr-1.5 h-5 w-5 text-green-400" />
                        <span className="text-green-600">全局脱敏已启用</span>
                      </>
                    ) : (
                      <>
                        <XCircle className="flex-shrink-0 mr-1.5 h-5 w-5 text-red-400" />
                        <span className="text-red-600">全局脱敏已禁用</span>
                      </>
                    )}
                  </div>
                </div>
              </div>
              <div className="mt-4 flex md:mt-0 md:ml-4">
                <button
                  onClick={handleReload}
                  className="inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
                >
                  <RefreshCw className="h-4 w-4 mr-2" />
                  重载规则
                </button>
                <button
                  onClick={handleToggleGlobal}
                  disabled={toggling}
                  className={`ml-3 inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white focus:outline-none focus:ring-2 focus:ring-offset-2 ${
                    config?.enabled
                      ? 'bg-red-600 hover:bg-red-700 focus:ring-red-500'
                      : 'bg-green-600 hover:bg-green-700 focus:ring-green-500'
                  } ${toggling ? 'opacity-50 cursor-not-allowed' : ''}`}
                >
                  {toggling ? '处理中...' : (config?.enabled ? '禁用全局脱敏' : '启用全局脱敏')}
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Search */}
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
        <div className="relative max-w-md">
          <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
            <Search className="h-5 w-5 text-gray-400" />
          </div>
          <input
            type="text"
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            placeholder="搜索规则..."
            className="block w-full pl-10 pr-3 py-2 border border-gray-300 rounded-md leading-5 bg-white placeholder-gray-500 focus:outline-none focus:placeholder-gray-400 focus:ring-1 focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
          />
        </div>
      </div>

      {/* Rules List */}
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {filteredRules.length === 0 ? (
          <div className="text-center py-12">
            <AlertCircle className="mx-auto h-12 w-12 text-gray-400" />
            <h3 className="mt-2 text-sm font-medium text-gray-900">没有找到匹配的规则</h3>
            <p className="mt-1 text-sm text-gray-500">请尝试调整搜索条件</p>
          </div>
        ) : (
          <div className="bg-white shadow overflow-hidden sm:rounded-md">
            <ul className="divide-y divide-gray-200">
              {filteredRules.map((rule) => (
                <li key={rule.id}>
                  <div className="px-4 py-4 sm:px-6">
                    <div className="flex items-center justify-between">
                      <div className="flex items-center">
                        <div className="flex-shrink-0">
                          <div className={`inline-flex items-center justify-center h-8 w-8 rounded-full ${
                            rule.enabled ? 'bg-green-100' : 'bg-gray-100'
                          }`}>
                            {rule.enabled ? (
                              <CheckCircle className="h-5 w-5 text-green-600" />
                            ) : (
                              <XCircle className="h-5 w-5 text-gray-400" />
                            )}
                          </div>
                        </div>
                        <div className="ml-4">
                          <div className="flex items-center">
                            <div className="text-sm font-medium text-gray-900">{rule.name}</div>
                            <span className={`ml-2 inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getSeverityColor(rule.severity)}`}>
                              {rule.severity}
                            </span>
                            <span className={`ml-2 inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getTypeColor(rule.type)}`}>
                              {rule.type}
                            </span>
                          </div>
                          <div className="mt-1 text-sm text-gray-500">{rule.description}</div>
                          <div className="mt-2 text-xs text-gray-400">ID: {rule.id}</div>
                        </div>
                      </div>
                      <div className="flex items-center space-x-4">
                        <div className="text-sm text-gray-500">
                          优先级: {rule.priority}
                        </div>
                        <div className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                          rule.enabled ? 'bg-green-100 text-green-800' : 'bg-gray-100 text-gray-800'
                        }`}>
                          {rule.enabled ? '已启用' : '已禁用'}
                        </div>
                      </div>
                    </div>

                    <div className="mt-4">
                      <dl className="grid grid-cols-1 gap-x-4 gap-y-2 sm:grid-cols-2">
                        {rule.fieldNames && rule.fieldNames.length > 0 && (
                          <div>
                            <dt className="text-sm font-medium text-gray-500">字段名称</dt>
                            <dd className="mt-1 text-sm text-gray-900">{rule.fieldNames.join(', ')}</dd>
                          </div>
                        )}
                        {rule.pattern && (
                          <div>
                            <dt className="text-sm font-medium text-gray-500">匹配模式</dt>
                            <dd className="mt-1 text-sm text-gray-900">
                              <code className="bg-gray-100 px-2 py-1 rounded text-xs">{rule.pattern}</code>
                            </dd>
                          </div>
                        )}
                        <div>
                          <dt className="text-sm font-medium text-gray-500">掩码值</dt>
                          <dd className="mt-1 text-sm text-gray-900">
                            <code className="bg-gray-100 px-2 py-1 rounded text-xs">{rule.maskValue}</code>
                          </dd>
                        </div>
                        {rule.includeServices && rule.includeServices.length > 0 && (
                          <div>
                            <dt className="text-sm font-medium text-gray-500">适用服务</dt>
                            <dd className="mt-1 text-sm text-gray-900">{rule.includeServices.join(', ')}</dd>
                          </div>
                        )}
                      </dl>
                    </div>
                  </div>
                </li>
              ))}
            </ul>
          </div>
        )}
      </div>
    </div>
  );
};

export default SimpleRulesList;
