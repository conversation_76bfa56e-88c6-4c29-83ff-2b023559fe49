import React from 'react';
import { Toaster } from 'react-hot-toast';
import SimpleRulesList from './components/SimpleRulesList';

function App() {
  return (
    <div className="min-h-screen bg-black">
      <SimpleRulesList />
      <Toaster
        position="top-right"
        toastOptions={{
          style: {
            background: '#1f2937',
            color: '#f3f4f6',
            border: '1px solid #374151',
          },
          success: {
            iconTheme: {
              primary: '#10b981',
              secondary: '#1f2937',
            },
          },
          error: {
            iconTheme: {
              primary: '#ef4444',
              secondary: '#1f2937',
            },
          },
        }}
      />
    </div>
  );
}

export default App;
