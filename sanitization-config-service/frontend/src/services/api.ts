import axios from 'axios';
import { SanitizationConfig, HealthResponse, MetricsResponse, ApiResponse } from '../types';

const API_BASE_URL = process.env.REACT_APP_API_URL || 'http://localhost:8080';

const api = axios.create({
  baseURL: API_BASE_URL,
  timeout: 10000,
  headers: {
    'Content-Type': 'application/json',
  },
});

// Request interceptor for adding auth headers if needed
api.interceptors.request.use(
  (config) => {
    // Add auth token if available
    const token = localStorage.getItem('auth_token');
    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
    }
    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

// Response interceptor for handling errors
api.interceptors.response.use(
  (response) => response,
  (error) => {
    if (error.response?.status === 401) {
      // Handle unauthorized access
      localStorage.removeItem('auth_token');
      window.location.href = '/login';
    }
    return Promise.reject(error);
  }
);

export const sanitizationApi = {
  // Get all sanitization rules
  getRules: async (serviceName?: string): Promise<SanitizationConfig> => {
    const headers = serviceName ? { 'X-Service-Name': serviceName } : {};
    const response = await api.get('/api/sanitization/rules', { headers });
    return response.data;
  },

  // Reload rules from configuration file
  reloadRules: async (): Promise<ApiResponse<any>> => {
    const response = await api.post('/api/sanitization/rules/reload');
    return response.data;
  },

  // Get service health
  getHealth: async (): Promise<HealthResponse> => {
    const response = await api.get('/health');
    return response.data;
  },

  // Get service metrics
  getMetrics: async (): Promise<MetricsResponse> => {
    const response = await api.get('/metrics');
    return response.data;
  },
};

export default api;
