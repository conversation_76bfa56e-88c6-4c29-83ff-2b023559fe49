<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>全局开关测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .status {
            padding: 15px;
            border-radius: 5px;
            margin: 10px 0;
            font-weight: bold;
        }
        .enabled {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .disabled {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        button {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
            font-size: 14px;
        }
        button:hover {
            background-color: #0056b3;
        }
        button:disabled {
            background-color: #6c757d;
            cursor: not-allowed;
        }
        .toggle-btn.enabled {
            background-color: #28a745;
        }
        .toggle-btn.disabled {
            background-color: #dc3545;
        }
        pre {
            background-color: #f8f9fa;
            padding: 15px;
            border-radius: 5px;
            overflow-x: auto;
            border: 1px solid #e9ecef;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🛡️ 全局脱敏开关测试</h1>
        
        <div id="status" class="status">
            正在加载状态...
        </div>
        
        <div>
            <button onclick="getCurrentStatus()">🔄 刷新状态</button>
            <button id="toggleBtn" onclick="toggleGlobalSwitch()" disabled>
                切换开关
            </button>
        </div>
        
        <h3>📊 当前配置信息</h3>
        <pre id="configInfo">正在加载...</pre>
        
        <h3>📝 操作日志</h3>
        <div id="logs" style="max-height: 200px; overflow-y: auto; border: 1px solid #ddd; padding: 10px; background: #f9f9f9;">
        </div>
    </div>

    <script>
        let currentConfig = null;
        
        function log(message) {
            const logs = document.getElementById('logs');
            const timestamp = new Date().toLocaleTimeString();
            logs.innerHTML += `<div>[${timestamp}] ${message}</div>`;
            logs.scrollTop = logs.scrollHeight;
        }
        
        async function getCurrentStatus() {
            try {
                log('正在获取当前状态...');
                const response = await fetch('http://localhost:8081/api/sanitization/rules');
                const data = await response.json();
                currentConfig = data;
                
                updateUI(data.enabled);
                document.getElementById('configInfo').textContent = JSON.stringify(data, null, 2);
                log(`状态获取成功: ${data.enabled ? '已启用' : '已禁用'}`);
            } catch (error) {
                log(`获取状态失败: ${error.message}`);
                console.error('Error:', error);
            }
        }
        
        async function toggleGlobalSwitch() {
            if (!currentConfig) {
                log('请先获取当前状态');
                return;
            }
            
            const newEnabled = !currentConfig.enabled;
            const toggleBtn = document.getElementById('toggleBtn');
            
            try {
                toggleBtn.disabled = true;
                toggleBtn.textContent = '切换中...';
                log(`正在${newEnabled ? '启用' : '禁用'}全局脱敏...`);
                
                const response = await fetch('http://localhost:8081/api/sanitization/toggle', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({ enabled: newEnabled })
                });
                
                const result = await response.json();
                
                if (response.ok) {
                    currentConfig.enabled = result.enabled;
                    updateUI(result.enabled);
                    log(`✅ ${result.message}`);
                    
                    // 重新获取完整配置
                    setTimeout(getCurrentStatus, 500);
                } else {
                    log(`❌ 切换失败: ${result.message || '未知错误'}`);
                }
            } catch (error) {
                log(`❌ 切换失败: ${error.message}`);
                console.error('Error:', error);
            } finally {
                toggleBtn.disabled = false;
                updateUI(currentConfig.enabled);
            }
        }
        
        function updateUI(enabled) {
            const status = document.getElementById('status');
            const toggleBtn = document.getElementById('toggleBtn');
            
            if (enabled) {
                status.className = 'status enabled';
                status.textContent = '🟢 全局脱敏已启用';
                toggleBtn.textContent = '🔴 禁用全局脱敏';
                toggleBtn.className = 'toggle-btn enabled';
            } else {
                status.className = 'status disabled';
                status.textContent = '🔴 全局脱敏已禁用';
                toggleBtn.textContent = '🟢 启用全局脱敏';
                toggleBtn.className = 'toggle-btn disabled';
            }
            
            toggleBtn.disabled = false;
        }
        
        // 页面加载时获取状态
        window.onload = function() {
            log('页面加载完成，正在初始化...');
            getCurrentStatus();
        };
    </script>
</body>
</html>
