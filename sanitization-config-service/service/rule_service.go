package service

import (
	"encoding/json"
	"fmt"
	"log"
	"os"
	"sync"
	"time"

	"sanitization-config-service/models"
)

// RuleService manages sanitization rules
type RuleService struct {
	config       *models.SanitizationConfig
	configFile   string
	mutex        sync.RWMutex
	lastModified time.Time
}

// NewRuleService creates a new rule service
func NewRuleService(configFile string) *RuleService {
	service := &RuleService{
		configFile: configFile,
		config:     models.NewSanitizationConfig(),
	}

	// Load initial configuration
	if err := service.LoadConfig(); err != nil {
		log.Printf("Failed to load initial config: %v", err)
		// Use default configuration with some basic rules
		service.loadDefaultRules()
	}

	return service
}

// LoadConfig loads configuration from file
func (s *RuleService) LoadConfig() error {
	s.mutex.Lock()
	defer s.mutex.Unlock()

	// Check if file exists
	if _, err := os.Stat(s.configFile); os.IsNotExist(err) {
		log.Printf("Config file %s does not exist, using default rules", s.configFile)
		s.loadDefaultRules()
		return nil
	}

	// Get file modification time
	fileInfo, err := os.Stat(s.configFile)
	if err != nil {
		return fmt.Errorf("failed to get file info: %w", err)
	}

	// Skip if file hasn't been modified
	if !fileInfo.ModTime().After(s.lastModified) {
		return nil
	}

	// Read file
	data, err := os.ReadFile(s.configFile)
	if err != nil {
		return fmt.Errorf("failed to read config file: %w", err)
	}

	// Parse JSON (simplified for now)
	var config models.SanitizationConfig
	if err := json.Unmarshal(data, &config); err != nil {
		return fmt.Errorf("failed to parse config file: %w", err)
	}

	// Validate configuration
	if err := config.Validate(); err != nil {
		return fmt.Errorf("invalid configuration: %w", err)
	}

	s.config = &config
	s.lastModified = fileInfo.ModTime()

	log.Printf("Loaded configuration with %d rules", len(config.Rules))
	return nil
}

// GetConfig returns the current sanitization configuration
func (s *RuleService) GetConfig() *models.SanitizationConfig {
	s.mutex.RLock()
	defer s.mutex.RUnlock()

	// Create a copy to avoid race conditions
	configCopy := *s.config
	configCopy.Rules = make([]models.SanitizationRule, len(s.config.Rules))
	copy(configCopy.Rules, s.config.Rules)

	return &configCopy
}

// GetConfigForService returns configuration filtered for a specific service
func (s *RuleService) GetConfigForService(serviceName string) *models.SanitizationConfig {
	s.mutex.RLock()
	defer s.mutex.RUnlock()

	config := *s.config
	config.Rules = s.config.GetRulesForService(serviceName)

	return &config
}

// ReloadConfig reloads configuration from file
func (s *RuleService) ReloadConfig() error {
	return s.LoadConfig()
}

// SetGlobalEnabled sets the global sanitization enabled flag
func (s *RuleService) SetGlobalEnabled(enabled bool) error {
	s.mutex.Lock()
	defer s.mutex.Unlock()

	s.config.Enabled = enabled
	s.config.Timestamp = time.Now().UnixMilli()

	// Save the updated configuration to file
	return s.saveConfig()
}

// saveConfig saves the current configuration to file
func (s *RuleService) saveConfig() error {
	data, err := json.MarshalIndent(s.config, "", "  ")
	if err != nil {
		return fmt.Errorf("failed to marshal config: %w", err)
	}

	if err := os.WriteFile(s.configFile, data, 0o644); err != nil {
		return fmt.Errorf("failed to write config file: %w", err)
	}

	log.Printf("Configuration saved to %s", s.configFile)
	return nil
}

// loadDefaultRules loads default sanitization rules
func (s *RuleService) loadDefaultRules() {
	s.config = models.NewSanitizationConfig()

	// Password rule
	passwordRule := models.SanitizationRule{
		ID:          "default-password",
		Name:        "Password Fields",
		Description: "Sanitize common password field names",
		Type:        models.FieldNameRule,
		Severity:    models.HighSeverity,
		Enabled:     true,
		Priority:    100,
		FieldNames:  []string{"password", "passwd", "pwd", "secret", "token", "apiKey", "api_key"},
		MaskValue:   "****",
		MarkerType:  "PASSWORD",
	}

	// Email pattern rule
	emailRule := models.SanitizationRule{
		ID:             "default-email",
		Name:           "Email Addresses",
		Description:    "Sanitize email addresses using pattern matching",
		Type:           models.PatternRule,
		Severity:       models.MediumSeverity,
		Enabled:        true,
		Priority:       200,
		Pattern:        `[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}`,
		ContentTypes:   []string{"application/json", "application/xml", "text/plain"},
		MaskValue:      "****@****.***",
		MarkerType:     "EMAIL",
		PreserveFormat: true,
	}

	// Credit card rule
	creditCardRule := models.SanitizationRule{
		ID:          "default-credit-card",
		Name:        "Credit Card Numbers",
		Description: "Sanitize credit card numbers",
		Type:        models.PatternRule,
		Severity:    models.CriticalSeverity,
		Enabled:     true,
		Priority:    50,
		Pattern:     `\b(?:\d{4}[-\s]?){3}\d{4}\b`,
		MaskValue:   "****-****-****-****",
		MarkerType:  "CREDIT_CARD",
	}

	// Phone number rule
	phoneRule := models.SanitizationRule{
		ID:          "default-phone",
		Name:        "Phone Numbers",
		Description: "Sanitize phone numbers",
		Type:        models.PatternRule,
		Severity:    models.MediumSeverity,
		Enabled:     true,
		Priority:    150,
		Pattern:     `\+?1?[-.\s]?\(?[0-9]{3}\)?[-.\s]?[0-9]{3}[-.\s]?[0-9]{4}`,
		MaskValue:   "***-***-****",
		MarkerType:  "PHONE",
	}

	// SSN rule
	ssnRule := models.SanitizationRule{
		ID:          "default-ssn",
		Name:        "Social Security Numbers",
		Description: "Sanitize US Social Security Numbers",
		Type:        models.PatternRule,
		Severity:    models.CriticalSeverity,
		Enabled:     true,
		Priority:    25,
		Pattern:     `\b\d{3}-?\d{2}-?\d{4}\b`,
		MaskValue:   "***-**-****",
		MarkerType:  "SSN",
	}

	s.config.AddRule(passwordRule)
	s.config.AddRule(emailRule)
	s.config.AddRule(creditCardRule)
	s.config.AddRule(phoneRule)
	s.config.AddRule(ssnRule)

	// Set global settings
	s.config.GlobalSettings = map[string]interface{}{
		"defaultMaskValue": "****",
		"enableLogging":    true,
		"logLevel":         "INFO",
	}

	log.Printf("Loaded %d default rules", len(s.config.Rules))
}
