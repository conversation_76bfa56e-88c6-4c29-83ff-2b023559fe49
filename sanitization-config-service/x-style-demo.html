<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>X.com 风格 - 数据脱敏规则管理</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Helvetica, Arial, sans-serif;
        }
        .animate-pulse-slow {
            animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
        }
        .backdrop-blur-md {
            backdrop-filter: blur(12px);
        }
        .hover-scale {
            transition: transform 0.2s ease-in-out;
        }
        .hover-scale:hover {
            transform: scale(1.02);
        }
    </style>
</head>
<body class="bg-black text-white min-h-screen">
    <!-- Header -->
    <div class="sticky top-0 bg-black/80 backdrop-blur-md border-b border-gray-800 z-10">
        <div class="max-w-6xl mx-auto px-4 py-4">
            <div class="flex items-center justify-between">
                <div class="flex items-center space-x-4">
                    <div class="p-2 bg-blue-600 rounded-full">
                        <svg class="h-6 w-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.031 9-11.622 0-1.042-.133-2.052-.382-3.016z"></path>
                        </svg>
                    </div>
                    <div>
                        <h1 class="text-xl font-bold text-white">数据脱敏规则管理</h1>
                        <div class="flex items-center space-x-2 mt-1">
                            <span class="text-gray-400 text-sm">管理和配置数据脱敏规则</span>
                            <span class="inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium bg-green-900/50 text-green-400 border border-green-800">
                                已启用
                            </span>
                        </div>
                    </div>
                </div>
                <div class="flex items-center space-x-3">
                    <button class="inline-flex items-center px-4 py-2 rounded-full text-sm font-medium bg-red-600 hover:bg-red-700 text-white transition-all duration-200 hover:scale-105 focus:outline-none focus:ring-2 focus:ring-red-500 focus:ring-offset-2 focus:ring-offset-black">
                        <svg class="h-4 w-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5.636 5.636a9 9 0 1012.728 0M12 3v9"></path>
                        </svg>
                        禁用全局脱敏
                    </button>
                    <button class="inline-flex items-center px-4 py-2 rounded-full text-sm font-medium bg-blue-600 hover:bg-blue-700 text-white transition-all duration-200 hover:scale-105 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 focus:ring-offset-black">
                        <svg class="h-4 w-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"></path>
                        </svg>
                        重载规则
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Search -->
    <div class="max-w-6xl mx-auto px-4 py-6">
        <div class="relative">
            <svg class="absolute left-4 top-1/2 transform -translate-y-1/2 h-5 w-5 text-gray-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
            </svg>
            <input
                type="text"
                placeholder="搜索规则名称、描述或ID..."
                class="w-full pl-12 pr-4 py-3 bg-gray-900 border border-gray-700 rounded-full text-white placeholder-gray-500 focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200"
            />
        </div>
    </div>

    <!-- Rules List -->
    <div class="max-w-6xl mx-auto px-4">
        <div class="mb-4">
            <h3 class="text-lg font-semibold text-white">规则列表 (10 / 10)</h3>
        </div>

        <div class="space-y-4">
            <!-- Rule Card 1 -->
            <div class="bg-gray-900 border border-gray-800 rounded-2xl p-6 hover:border-gray-700 transition-all duration-200 hover:shadow-lg hover-scale">
                <div class="flex items-start justify-between">
                    <div class="flex-1">
                        <div class="flex items-center space-x-3 mb-2">
                            <h4 class="text-lg font-semibold text-white">Password Fields</h4>
                            <span class="inline-flex items-center px-3 py-1 rounded-full text-xs font-medium bg-red-900/50 text-red-400 border border-red-800">
                                HIGH
                            </span>
                            <span class="inline-flex items-center px-3 py-1 rounded-full text-xs font-medium bg-blue-900/50 text-blue-400 border border-blue-800">
                                FIELD_NAME
                            </span>
                        </div>
                        <p class="text-sm text-gray-300 mb-2">Sanitize common password field names - completely masked</p>
                        <div class="text-xs text-gray-500 mb-3">ID: password-fields</div>
                        
                        <div class="space-y-3">
                            <div class="text-sm">
                                <span class="font-medium text-gray-400">字段:</span>
                                <span class="ml-2 text-gray-300">password, passwd, pwd, secret, token</span>
                            </div>
                            <div class="text-sm">
                                <span class="font-medium text-gray-400">掩码:</span>
                                <code class="ml-2 text-xs bg-gray-800 text-green-400 px-2 py-1 rounded-md border border-gray-700">********</code>
                            </div>
                        </div>
                    </div>
                    
                    <div class="ml-6 flex flex-col items-end space-y-3">
                        <div class="flex items-center">
                            <div class="w-2 h-2 bg-green-400 rounded-full mr-2 animate-pulse"></div>
                            <span class="text-sm font-medium text-green-400">已启用</span>
                        </div>
                        <div class="text-sm text-gray-500 bg-gray-800 px-2 py-1 rounded-md">
                            优先级: 100
                        </div>
                    </div>
                </div>
            </div>

            <!-- Rule Card 2 -->
            <div class="bg-gray-900 border border-gray-800 rounded-2xl p-6 hover:border-gray-700 transition-all duration-200 hover:shadow-lg hover-scale">
                <div class="flex items-start justify-between">
                    <div class="flex-1">
                        <div class="flex items-center space-x-3 mb-2">
                            <h4 class="text-lg font-semibold text-white">Email Addresses</h4>
                            <span class="inline-flex items-center px-3 py-1 rounded-full text-xs font-medium bg-yellow-900/50 text-yellow-400 border border-yellow-800">
                                MEDIUM
                            </span>
                            <span class="inline-flex items-center px-3 py-1 rounded-full text-xs font-medium bg-purple-900/50 text-purple-400 border border-purple-800">
                                PATTERN
                            </span>
                        </div>
                        <p class="text-sm text-gray-300 mb-2">Sanitize email addresses - show first 2 chars and domain</p>
                        <div class="text-xs text-gray-500 mb-3">ID: email-pattern</div>
                        
                        <div class="space-y-3">
                            <div class="text-sm">
                                <span class="font-medium text-gray-400">模式:</span>
                                <code class="ml-2 text-xs bg-gray-800 text-blue-400 px-2 py-1 rounded-md border border-gray-700">([a-zA-Z0-9._%+-]{1,2})[a-zA-Z0-9._%+-]*@([a-zA-Z0-9.-]+\.[a-zA-Z]{2,})</code>
                            </div>
                            <div class="text-sm">
                                <span class="font-medium text-gray-400">掩码:</span>
                                <code class="ml-2 text-xs bg-gray-800 text-green-400 px-2 py-1 rounded-md border border-gray-700">$1***@$2</code>
                            </div>
                        </div>
                    </div>
                    
                    <div class="ml-6 flex flex-col items-end space-y-3">
                        <div class="flex items-center">
                            <div class="w-2 h-2 bg-green-400 rounded-full mr-2 animate-pulse"></div>
                            <span class="text-sm font-medium text-green-400">已启用</span>
                        </div>
                        <div class="text-sm text-gray-500 bg-gray-800 px-2 py-1 rounded-md">
                            优先级: 200
                        </div>
                    </div>
                </div>
            </div>

            <!-- Rule Card 3 -->
            <div class="bg-gray-900 border border-gray-800 rounded-2xl p-6 hover:border-gray-700 transition-all duration-200 hover:shadow-lg hover-scale">
                <div class="flex items-start justify-between">
                    <div class="flex-1">
                        <div class="flex items-center space-x-3 mb-2">
                            <h4 class="text-lg font-semibold text-white">Credit Card Numbers</h4>
                            <span class="inline-flex items-center px-3 py-1 rounded-full text-xs font-medium bg-red-900/50 text-red-400 border border-red-800">
                                CRITICAL
                            </span>
                            <span class="inline-flex items-center px-3 py-1 rounded-full text-xs font-medium bg-purple-900/50 text-purple-400 border border-purple-800">
                                PATTERN
                            </span>
                        </div>
                        <p class="text-sm text-gray-300 mb-2">Sanitize credit card numbers - show last 4 digits only</p>
                        <div class="text-xs text-gray-500 mb-3">ID: credit-card</div>
                        
                        <div class="space-y-3">
                            <div class="text-sm">
                                <span class="font-medium text-gray-400">模式:</span>
                                <code class="ml-2 text-xs bg-gray-800 text-blue-400 px-2 py-1 rounded-md border border-gray-700">\b(\d{4})[-\s]?(\d{4})[-\s]?(\d{4})[-\s]?(\d{4})\b</code>
                            </div>
                            <div class="text-sm">
                                <span class="font-medium text-gray-400">掩码:</span>
                                <code class="ml-2 text-xs bg-gray-800 text-green-400 px-2 py-1 rounded-md border border-gray-700">****-****-****-$4</code>
                            </div>
                        </div>
                    </div>
                    
                    <div class="ml-6 flex flex-col items-end space-y-3">
                        <div class="flex items-center">
                            <div class="w-2 h-2 bg-green-400 rounded-full mr-2 animate-pulse"></div>
                            <span class="text-sm font-medium text-green-400">已启用</span>
                        </div>
                        <div class="text-sm text-gray-500 bg-gray-800 px-2 py-1 rounded-md">
                            优先级: 50
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Footer -->
    <div class="max-w-6xl mx-auto px-4 py-8 mt-8">
        <div class="text-center text-gray-500 text-sm">
            <p>数据脱敏规则管理系统 - X.com 风格界面</p>
            <p class="mt-1">现代化、简洁、高效的规则管理体验</p>
        </div>
    </div>
</body>
</html>
